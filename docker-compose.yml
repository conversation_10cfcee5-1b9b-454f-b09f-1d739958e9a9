version: "3.9"
services:
  db:
    image: postgres:16
    container_name: rejoyce-db
    environment:
      POSTGRES_USER: rejoyce
      POSTGRES_PASSWORD: rejoyce
      POSTGRES_DB: rejoyce_dev
    ports:
      - "5432:5432"
    volumes:
      - rejoyce_pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rejoyce -d rejoyce_dev"]
      interval: 5s
      timeout: 5s
      retries: 10
volumes:
  rejoyce_pgdata:
