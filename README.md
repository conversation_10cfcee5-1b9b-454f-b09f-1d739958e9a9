# Rejoyce

Rejoyce is a full-stack enterprise analytics platform built with React, Express, and PostgreSQL. It provides strategic insights, KPI monitoring, and executive dashboards for telecommunications companies.

## Local Development

### Prerequisites
- Docker and Docker Compose
- Node.js (for non-Docker development)

### Docker Development (Recommended)

#### Start Local Environment
```bash
# Start all services (app + database)
./run-local.sh

# Or manually:
docker-compose -f docker-compose.local.yml up --build
```

#### Stop Environment
```bash
# Stop all services
docker-compose -f docker-compose.local.yml down

# Stop and remove volumes (reset database)
docker-compose -f docker-compose.local.yml down -v
```

#### Rebuild Application
```bash
# Rebuild app container only
docker-compose -f docker-compose.local.yml up --build app

# Force rebuild without cache
docker-compose -f docker-compose.local.yml build --no-cache app
```

#### View Logs
```bash
# All services
docker-compose -f docker-compose.local.yml logs -f

# App only
docker-compose -f docker-compose.local.yml logs -f app

# Database only
docker-compose -f docker-compose.local.yml logs -f db
```

#### Environment Details
- **App**: Runs on http://localhost:3000 (mapped from container port 5000)
- **Database**: PostgreSQL on localhost:5432
- **Environment**: Development mode with OIDC skipped
- **Hot Reload**: Code changes require rebuild

### Native Development (Alternative)

```bash
# Install dependencies
npm install

# Start database only
docker-compose -f docker-compose.local.yml up db

# Start development server
npm run dev

# In another terminal, push database schema
npm run db:push
```

### Production Docker

```bash
# Start production-like environment
docker-compose up --build

# Access at http://localhost:5000
```

### Database Operations

```bash
# Push schema changes
npm run db:push

# Generate migrations
npm run db:generate

# Seed database
npm run db:seed

# Database health check
npm run smoke:db
```

### Commands Reference

```bash
# Development
npm run dev          # Start dev server
npm run check        # Type checking
npm run build        # Build for production
npm start           # Start production server

# Docker Local
./run-local.sh                                        # Start local environment
docker-compose -f docker-compose.local.yml down      # Stop environment
docker-compose -f docker-compose.local.yml down -v   # Stop and reset database
docker-compose -f docker-compose.local.yml logs -f   # View logs

# Docker Production
docker-compose up --build    # Start production environment
docker-compose down         # Stop production environment
```