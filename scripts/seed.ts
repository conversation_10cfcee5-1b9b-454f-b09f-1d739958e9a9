import "dotenv/config";
import bcrypt from "bcrypt";
import { db } from "../server/db";
import {
  users,
  companies,
  kpiMetrics,
  performanceData,
  strategicInsights,
  executionPriorities,
} from "../shared/schema";
import { eq } from "drizzle-orm";

async function main() {
  // Seed users with bcrypt-hashed passwords
  const demoPass = await bcrypt.hash("demo123", 10);
  const analystPass = await bcrypt.hash("password", 10);
  await db
    .insert(users)
    .values([
      { username: "demo", password: demoPass, email: "<EMAIL>" },
      { username: "analyst", password: analystPass, email: "<EMAIL>" },
    ])
    .onConflictDoNothing({ target: users.username });

  // Seed companies including Verizon (VZ) used by the UI
  await db
    .insert(companies)
    .values([
      {
        symbol: "ACME",
        name: "Acme Corp",
        sector: "Tech",
        description: "Example technology company for local development",
      },
      {
        symbol: "HLTH",
        name: "Health Co",
        sector: "Healthcare",
        description: "Example healthcare company for local development",
      },
      {
        symbol: "VZ",
        name: "Verizon Communications Inc.",
        sector: "Telecommunications",
        marketCap: "150000000000",
        description:
          "Verizon delivers a broad portfolio of telecommunications products and services, including wireless connectivity, broadband internet, and solutions for both consumers and businesses.",
      },
    ])
    .onConflictDoNothing({ target: companies.symbol });

  // Fetch VZ to seed related tables (avoid duplicates on re-run)
  const [vz] = await db
    .select()
    .from(companies)
    .where(eq(companies.symbol, "VZ"))
    .limit(1);

  if (vz) {
    // KPIs for VZ
    const existingKpi = await db
      .select()
      .from(kpiMetrics)
      .where(eq(kpiMetrics.companyId, vz.id))
      .limit(1);

    if (existingKpi.length === 0) {
      await db.insert(kpiMetrics).values([
        {
          companyId: vz.id,
          metricName: "Service Revenue",
          value: "28.6",
          previousValue: "28.0",
          period: "Q2 2023",
          unit: "B",
          category: "financial",
          updatedAt: new Date(),
        },
        {
          companyId: vz.id,
          metricName: "Postpaid Phone Net Adds",
          value: "420",
          previousValue: "400",
          period: "Q2 2023",
          unit: "K",
          category: "operational",
          updatedAt: new Date(),
        },
        {
          companyId: vz.id,
          metricName: "Free Cash Flow",
          value: "7.1",
          previousValue: "6.3",
          period: "Q2 2023",
          unit: "B",
          category: "financial",
          updatedAt: new Date(),
        },
        {
          companyId: vz.id,
          metricName: "Churn Rate",
          value: "0.83",
          previousValue: "0.88",
          period: "Q2 2023",
          unit: "%",
          category: "operational",
          updatedAt: new Date(),
        },
        {
          companyId: vz.id,
          metricName: "ARPU",
          value: "124.50",
          previousValue: "123.80",
          period: "Q2 2023",
          unit: "$",
          category: "financial",
          updatedAt: new Date(),
        },
        {
          companyId: vz.id,
          metricName: "5G Coverage",
          value: "230",
          previousValue: "220",
          period: "Q2 2023",
          unit: "M people",
          category: "operational",
          updatedAt: new Date(),
        },
      ]);
    }

    // Performance data for VZ
    const existingPerf = await db
      .select()
      .from(performanceData)
      .where(eq(performanceData.companyId, vz.id))
      .limit(1);

    if (existingPerf.length === 0) {
      await db.insert(performanceData).values([
        {
          companyId: vz.id,
          period: "Q1 2023",
          revenue: "32800",
          netIncome: "5200",
          freeCashFlow: "6800",
          churnRate: "0.88",
          customerCount: 92000000,
          arpu: "123.80",
          data: {},
        },
        {
          companyId: vz.id,
          period: "Q2 2023",
          revenue: "33300",
          netIncome: "5400",
          freeCashFlow: "7100",
          churnRate: "0.83",
          customerCount: 93200000,
          arpu: "124.50",
          data: {},
        },
      ]);
    }

    // Strategic insights for VZ
    const existingInsights = await db
      .select()
      .from(strategicInsights)
      .where(eq(strategicInsights.companyId, vz.id))
      .limit(1);

    if (existingInsights.length === 0) {
      await db.insert(strategicInsights).values([
        {
          companyId: vz.id,
          category: "Growth Trajectory",
          title: "5G Network Leadership",
          description:
            "Consumer Wireless revenue has shown steady growth, supported by increased postpaid phone net additions and stable ARPUs.",
          priority: "high",
          status: "active",
          assignedTo: "Network Team",
          dueDate: new Date("2023-12-31"),
        },
        {
          companyId: vz.id,
          category: "Areas for Improvement",
          title: "Cost Management Focus",
          description:
            "Reducing churn remains a focus, as competitive pressures persist in the wireless market.",
          priority: "medium",
          status: "in_progress",
          assignedTo: "Customer Experience Team",
          dueDate: new Date("2023-09-30"),
        },
        {
          companyId: vz.id,
          category: "Key Performance Drivers",
          title: "Digital Transformation",
          description:
            "Key drivers include network reliability, 5G adoption, and effective promotional strategies.",
          priority: "high",
          status: "active",
          assignedTo: "Technology Team",
          dueDate: new Date("2024-03-31"),
        },
      ]);
    }

    // Execution priorities for VZ
    const existingPrio = await db
      .select()
      .from(executionPriorities)
      .where(eq(executionPriorities.companyId, vz.id))
      .limit(1);

    if (existingPrio.length === 0) {
      await db.insert(executionPriorities).values([
        {
          companyId: vz.id,
          title: "Accelerate 5G Network Rollout",
          description: "Expand coverage and capacity to maintain competitive edge",
          priority: "HIGH",
          category: "Network Infrastructure",
          progress: 75,
          owner: "Network Operations",
          timeline: "Q4 2023",
        },
        {
          companyId: vz.id,
          title: "Enhance Customer Retention",
          description: "Reduce churn through improved service and loyalty programs",
          priority: "HIGH",
          category: "Customer Experience",
          progress: 60,
          owner: "Customer Experience Team",
          timeline: "Q3 2023",
        },
        {
          companyId: vz.id,
          title: "Optimize Capital Allocation",
          description:
            "Balance network investment with debt reduction and shareholder returns",
          priority: "MEDIUM",
          category: "Financial Management",
          progress: 45,
          owner: "Finance Team",
          timeline: "Q4 2023",
        },
        {
          companyId: vz.id,
          title: "Advance Digital Transformation",
          description: "Leverage automation and AI to streamline operations",
          priority: "MEDIUM",
          category: "Technology",
          progress: 30,
          owner: "Technology Team",
          timeline: "Q1 2024",
        },
      ]);
    }
  }

  console.log("Seeded demo and analyst users, companies (incl. VZ), and VZ sample data");
  process.exit(0);
}

main().catch((e) => {
  console.error(e);
  process.exit(1);
});
