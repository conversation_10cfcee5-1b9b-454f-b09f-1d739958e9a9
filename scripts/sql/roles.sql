-- Least-privileged roles for Rejoy<PERSON> Postgres (run per environment)
-- Adjust database name, usernames, and strong passwords before executing.

-- 1) Create users (roles)
-- NOTE: Replace the passwords with strong, unique values per environment.
CREATE USER migrator WITH PASSWORD 'REPLACE_WITH_STRONG_PASSWORD';
CREATE USER app_user WITH PASSWORD 'REPLACE_WITH_STRONG_PASSWORD';

-- 2) Allow both to connect and use public schema
-- For staging:
--   (If your DB name is different, update it below)
GRANT CONNECT ON DATABASE rejoyce_staging TO migrator, app_user;
GRANT USAGE ON SCHEMA public TO migrator, app_user;

-- For prod (run the same in the prod database context):
-- GRANT CONNECT ON DATABASE rejoyce_prod TO migrator, app_user;
-- GRANT USAGE ON SCHEMA public TO migrator, app_user;

-- 3) Run migrations with migrator first to create tables.

-- 4) After tables exist, grant DML privileges to app_user:
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;

-- 5) Ensure future tables created by migrator inherit the same DML grants:
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO app_user;

-- Optional: if you create sequences, you may also want:
-- GRANT USAGE, SELECT, UPDATE ON ALL SEQUENCES IN SCHEMA public TO app_user;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public
-- GRANT USAGE, SELECT, UPDATE ON SEQUENCES TO app_user;

-- Verification checklist:
-- - migrator can run DDL (create/alter/drop)
-- - app_user can only run DML (select/insert/update/delete)
-- - app connects with APP_DATABASE_URL (app_user)
-- - CI migrations run with DATABASE_URL (migrator)
