import "dotenv/config";
import { db } from "../server/db";
import { sql, eq } from "drizzle-orm";
import { users, companies } from "../shared/schema";

async function main() {
  console.log("Smoke test starting...");

  // 1) DB connectivity
  await db.execute(sql`SELECT 1`);
  console.log("SELECT 1 OK");

  // 2) Basic reads
  const [{ userCount }] = await db
    .select({ userCount: sql<number>`count(*)` })
    .from(users);
  const [{ companyCount }] = await db
    .select({ companyCount: sql<number>`count(*)` })
    .from(companies);
  console.log(`Users: ${userCount}, Companies: ${companyCount}`);

  const analyst = await db
    .select()
    .from(users)
    .where(eq(users.username, "analyst"))
    .limit(1);
  if (analyst.length === 0) {
    throw new Error("Expected 'analyst' user not found");
  }
  console.log("Found user 'analyst'");

  // 3) DML (insert/delete) sanity
  const sym = `TMP_${Date.now()}`;
  await db
    .insert(companies)
    .values({
      symbol: sym,
      name: "Temp Co",
      sector: "Test",
      description: "Temporary insert for smoke test",
    })
    .returning();

  const inserted = await db
    .select()
    .from(companies)
    .where(eq(companies.symbol, sym))
    .limit(1);

  if (inserted.length !== 1) {
    throw new Error("Insert verification failed");
  }
  console.log(`Insert OK (${sym})`);

  await db.delete(companies).where(eq(companies.symbol, sym));

  const afterDelete = await db
    .select()
    .from(companies)
    .where(eq(companies.symbol, sym))
    .limit(1);

  if (afterDelete.length !== 0) {
    throw new Error("Delete verification failed");
  }
  console.log("Delete OK");

  console.log("Smoke test PASSED");
  process.exit(0);
}

main().catch((e) => {
  console.error("Smoke test FAILED:", e);
  process.exit(1);
});
