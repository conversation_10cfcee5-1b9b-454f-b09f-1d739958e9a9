# Technical Specification - MVP

## Table of Contents

- [Current State Overview](#current-state-overview)
  - [High-Level Architecture](#high-level-architecture)
  - [Technology Deep Dive](#technology-deep-dive)
  - [Key Features & Functionality](#key-features-functionality)
  - [Code Structure & Best Practices](#code-structure-best-practices)
  - [Example of how a page is being built](#example-of-how-a-page-is-being-built)
    - [1. Routing Architecture](#1-routing-architecture)
    - [2. Component Structure](#2-component-structure)
    - [3. Data Flow Architecture](#3-data-flow-architecture)
    - [4. Server-Side Implementation](#4-server-side-implementation)
    - [5. Data Structure](#5-data-structure)
    - [6. UI Rendering](#6-ui-rendering)
    - [7. Key Features](#7-key-features)
  - [Summary](#summary)
- [Full Technical Roadmap](#full-technical-roadmap)
  - [Technical MVP](#technical-mvp)
    - [Objectives](#objectives)
    - [Success criteria](#success-criteria)
    - [Core architecture (lean AWS)](#core-architecture-lean-aws)
    - [Alternative deployment path (Render/Railway)](#alternative-deployment-path-renderrailway)
    - [Multi‑tenancy foundations (soft multi‑tenant)](#multitenancy-foundations-soft-multitenant)
    - [Identity and basic authorization](#identity-and-basic-authorization)
    - [Data layer and migration off in‑memory](#data-layer-and-migration-off-inmemory)
    - [App security basics](#app-security-basics)
    - [Observability (minimum viable)](#observability-minimum-viable)
    - [What to defer (nice-to-haves or post‑MVP)](#what-to-defer-nice-to-haves-or-postmvp)
    - [Minimal MVP architecture (textual)](#minimal-mvp-architecture-textual)
    - [Concrete MVP work plan (3--5 weeks)](#concrete-mvp-work-plan-35-weeks)
    - [Code‑level changes (MVP‑scope)](#codelevel-changes-mvpscope)
    - [MVP acceptance checklist](#mvp-acceptance-checklist)
    - [Optional speed-ups if MVP timeline is very aggressive](#optional-speed-ups-if-mvp-timeline-is-very-aggressive)
    - [Items explicitly out-of-scope for MVP (defer)](#items-explicitly-out-of-scope-for-mvp-defer)

## Current State Overview

This codebase is a well-structured, full-stack web application designed for business intelligence and analysis. It provides a dashboard-driven interface for visualizing company data and includes an AI-powered chat agent named "Joyce" for interactive analysis.

### High-Level Architecture

The project is organized into three main parts: a client, a server, and a shared directory, creating a monorepo-like structure. The entire application is written in **TypeScript**, ensuring type safety across the stack.

- **Client-Side (Frontend):** A modern Single Page Application (SPA) built with **React** and **Vite**. It features a component-based architecture with a clear separation of concerns (pages, components, hooks, services).

- **Server-Side (Backend):** A **Node.js** application using the **Express.js** framework. It serves a RESTful API for the client and handles the AI chat functionality.

- **Shared Code:** The shared directory contains code that is used by both the client and the server, primarily the database schema and data validation logic. This is a best practice that ensures data consistency.

### Technology Deep Dive

- **Frontend:**

  - **UI Framework:** **React** is used for building the user interface.

  - **UI Components:** The application uses a combination of **Radix UI** for accessible, unstyled components and **Tailwind CSS** for styling. This is a modern and flexible approach to UI development. **Lucide React** provides the icons.

  - **Routing:** **Wouter** is used for client-side routing, which is a lightweight and minimalist alternative to React Router.

  - **Data Fetching & State Management:** **Tanstack Query** is used for managing server state, which simplifies data fetching, caching, and synchronization. Global UI state is managed with React Context.

  - **Forms:** **React Hook Form** and **Zod** are used for building and validating forms, ensuring a robust and type-safe user input experience.

  - **Data Visualization:** **Recharts** is used to create charts and graphs for the dashboard.

- **Backend:**

  - **Web Framework:** **Express.js** is the foundation of the backend, providing a robust and minimalist framework for building the API.

  - **Database & ORM:** The application is designed to use a **PostgreSQL** database, with **Drizzle ORM** providing a type-safe query builder. For development, it currently uses an **in-memory database**, which is a convenient way to get started without a full database setup.

  - **Authentication:** **Passport.js** is used for authentication, with a local strategy for username/password login.

  - **AI Integration:** The backend integrates with **Anthropic's Claude 3.5 Sonnet** model via the **AI SDK**. It streams responses to the client for a real-time chat experience.

### Key Features & Functionality

- **Authentication:** The application has a secure login system with protected routes, ensuring that only authenticated users can access the main features.

- **Dashboard & Reporting:** The core of the application is a set of dashboards and reports that visualize key business metrics, including:

  - Key Performance Indicators (KPIs)
  - Financial Performance Data
  - Strategic Insights
  - Execution Priorities

- **AI Chat Agent ("Joyce"):** A standout feature is the AI-powered chat agent, which allows users to ask questions about the company data in natural language. The backend builds a context-specific prompt for the AI based on the selected company, enabling more relevant and accurate responses.

- **Hybrid Data Model:** The application uses a flexible data model that combines a dynamic database with static JSON files. This allows for both user-specific data and pre-defined, version-controlled data.

### Code Structure & Best Practices

- **Type Safety:** The use of TypeScript, Drizzle ORM, and Zod ensures end-to-end type safety, from the database to the frontend. This reduces bugs and improves developer experience.

- **Separation of Concerns:** The code is well-organized, with clear separation between UI, business logic, and data access layers.

- **Shared Schema:** The shared/schema.ts file is a great example of code sharing between the client and server, which reduces duplication and ensures consistency.

- **Development & Production Parity:** The application is designed to work seamlessly in both development (with Vite middleware and an in-memory database) and production (with a static build and a real database). The IStorage interface is a key part of this design, allowing for easy swapping of the data layer.

### Example of how a page is being built

Based on my analysis of the codebase, here's a comprehensive breakdown of how the executive summary page at [/sei-report/executive-summary](http://localhost:3000/sei-report/executive-summary) is constructed:

*0. Flow Diagram*

![Flow Diagram](./media/FlowDiagram.png)

#### 1. Routing Architecture

The URL routing follows this path:

- **App.tsx**: Defines the main route /sei-report/:section? which maps to the SEIReport component

- **SEIReport component**: Handles the executive-summary section parameter and renders the appropriate component

#### 2. Component Structure

**Main Page Component (sei-report.tsx)**

- Uses useParams() to extract the section parameter from the URL
- Defaults to "executive-summary" if no section is provided
- Maintains a tabs array that maps section IDs to their corresponding components
- Renders the ExecutiveSummary component when the section is "executive-summary"

**Executive Summary Component (executive-summary.tsx)**

The component is structured into several key sections:

- **Summary Analysis**: High-level company overview
- **Executive Snapshot**: 4 key metrics cards (Investability Grade, Joy Score, Fog Score, ROI Potential)
- **Key Strengths & Risk Flags**: Two-column layout showing positive and negative factors
- **Strategic Objectives**: Company's strategic goals
- **Key Performance Indicators**: Financial metrics from API
- **Strategic Insights**: Analysis points
- **Execution Priorities**: Action items with priority levels
- **Stakeholder Takeaways**: Separate sections for investors and executives

#### 3. Data Flow Architecture

**Static Content Data**

- **Source**: JSON files in /client/public/data/companies/{company}/executive-summary.json
- **Hook**: useCompanyData(selectedCompany, "executive-summary")
- **Fetching**: Direct HTTP fetch to the public JSON files
- **Structure**: Comprehensive data object with all page content

**Dynamic API Data**

The component also fetches real-time data via React Query:

- **KPIs**: /api/companies/{companyDisplayName}/kpis
- **Insights**: /api/companies/{companyDisplayName}/insights
- **Priorities**: /api/companies/{companyDisplayName}/priorities

**Company Context**

- **Provider**: CompanyProvider wraps the entire app
- **Default**: Currently defaults to 'vz' (Verizon)
- **Display Name Mapping**: Converts company codes to display names (e.g., 'vz' → 'VZ')

#### 4. Server-Side Implementation

**API Endpoints (routes.ts)**

- /api/companies/{symbol}/kpis - Returns KPI metrics from database
- /api/companies/{symbol}/insights - Returns strategic insights
- /api/companies/{symbol}/priorities - Returns execution priorities
- Uses a storage layer to fetch data from the database

**Static File Serving**

- JSON files are served directly from /client/public/data/companies/ via Vite's static file serving
- No server processing required for the main content data

#### 5. Data Structure

The executive summary JSON contains:

```json
{
  "companySymbol": "string",
  "companyName": "string",
  "executiveSnapshot": {
    "investabilityGrade": "string",
    "joyScore": "number",
    "fogScore": "number",
    "roiPotential": "string"
  },
  "summaryAnalysis": {
    "text": "string"
  },
  "keyStrengths": [
    {
      "text": "string",
      "status": "string"
    }
  ],
  "riskFlags": [
    {
      "text": "string",
      "status": "string"
    }
  ],
  "strategicObjectives": [
    {
      "text": "string",
      "status": "string"
    }
  ],
  "strategicInsights": [
    {
      "text": "string",
      "status": "string"
    }
  ],
  "stakeholderTakeaways": {
    "investor": [
      {
        "priority": "string",
        "text": "string",
        "status": "string"
      }
    ],
    "executive": [
      {
        "priority": "string",
        "text": "string",
        "status": "string"
      }
    ]
  }
}
```

#### 6. UI Rendering

- **Layout**: Uses MainLayout with header and navigation
- **Styling**: Tailwind CSS with custom card components
- **Icons**: Lucide React icons for visual elements
- **Status Indicators**: Color-coded based on status values (positive/negative/normal)
- **Navigation**: Previous/Next section buttons for report navigation

#### 7. Key Features

- **Responsive Design**: Grid layouts that adapt to screen size
- **Loading States**: Shows loading spinner while data fetches
- **Error Handling**: Displays error messages if data fails to load
- **Strategic Lens**: Supports filtering/customization (passed as prop)
- **Real-time Data**: Combines static content with live API data

The architecture elegantly separates static report content (stored in JSON files) from dynamic business metrics (fetched from APIs), allowing for both comprehensive reporting and real-time data updates.

### Summary

In summary, this is a modern, well-architected, and feature-rich web application that leverages a powerful and popular technology stack to deliver a sophisticated business intelligence and analysis tool.

## Full Technical Roadmap

## Technical MVP

### Objectives

- Deliver core value: dashboards + Joyce chat on real data.
- Onboard first real tenant; support 5–50 users.
- Establish minimal but real cloud deployment with CI/CD and a managed database.

### Success criteria

- p95 non‑AI API latency < 400ms; p95 Joyce response < 6s.
- < 0.5% 5xx error rate over one week.
- Zero-downtime deploys to staging + prod via CI.
- Data persistence in managed Postgres; no in‑memory storage in prod.
- Basic auth with hosted login, email/password reset; role gates for admin areas.

### Core architecture (lean AWS)

**Interactive AWS Architecture Diagram**

[View AWS Architecture Diagram](https://rejoyce-ai.github.io/Rejoyce/media/AWS_Architecture_Diagram.html) - *Open in browser for interactive visualization*

This comprehensive diagram shows the complete AWS infrastructure including VPC networking, security layers, CI/CD pipeline, and all AWS services used in the MVP deployment.

- **Frontend hosting**

  - Build SPA and host on S3; optionally front with CloudFront if domain/TLS needed day 1.
  - Alt if speed > AWS: host on Render/Railway initially; we can still move to S3/CloudFront later.

- **API compute**

  - Single ECS Fargate service behind ALB, single target group, rolling deploys.
  - Small task size (e.g., 0.5 vCPU/1GB) with auto-scaling on CPU/requests later.

- **Database**

  - Amazon RDS for PostgreSQL, single‑AZ (Multi‑AZ optional later).
  - No RDS Proxy yet (add in post‑MVP).

- **Networking and security**

  - VPC with public (ALB) and private subnets (ECS, RDS). Security Groups with least privilege.
  - TLS via ACM on ALB (and CloudFront later).

- **Secrets/config**

  - Secrets in AWS Secrets Manager; non‑secrets in SSM Parameter Store.
  - ECS task role permits runtime fetch; no secrets in env files.

- **CI/CD**

  - One GitHub Actions workflow: lint/typecheck/test → build Docker → scan (Trivy) → push to ECR → run migrations → deploy ECS → (optional) S3 sync for client.
  - OIDC trust from GitHub to AWS (no static keys).

### Alternative deployment path (Render/Railway)

**Interactive Railway Architecture Diagram**

[View Railway Architecture Diagram](https://rejoyce-ai.github.io/Rejoyce/media/Railway_Architecture_Diagram.html) - *Open in browser for interactive visualization*

This diagram shows the simplified Railway deployment architecture with automatic CI/CD, managed services, and built-in monitoring.

For faster MVP deployment and reduced operational complexity, consider using Render or Railway as an alternative to AWS. This approach aligns with the "speed-ups" philosophy mentioned later in this document.

**Key advantages for MVP:**
- **Faster time-to-market**: Deploy in minutes vs. hours/days for AWS infrastructure
- **Simplified operations**: Less configuration and maintenance overhead
- **Predictable costs**: Clearer pricing models without surprise charges
- **Developer-friendly**: More intuitive interfaces and workflows
- **Automatic HTTPS**: Built-in SSL/TLS certificate management

**Component mapping:**

- **Frontend hosting**
  - **Render**: Static site hosting with global CDN and automatic deployments from Git
  - **Railway**: Static site hosting with edge caching
  - **Migration path**: Easy to move to S3/CloudFront later via build pipeline changes

- **API compute**
  - **Render**: Web services with automatic scaling, health checks, and zero-downtime deploys
  - **Railway**: Application hosting with automatic scaling and built-in load balancing
  - **Migration path**: Containerized apps can be moved to ECS with minimal changes

- **Database**
  - **Render**: Managed PostgreSQL with automated backups and connection pooling
  - **Railway**: Managed PostgreSQL with built-in monitoring and scaling
  - **Migration path**: Standard PostgreSQL dump/restore to RDS

- **Networking and security**
  - **Both platforms**: Automatic HTTPS, network isolation, and DDoS protection
  - **Migration path**: Move to VPC/Security Groups when advanced networking is needed

- **Secrets/config**
  - **Both platforms**: Environment variable management with encryption at rest
  - **Migration path**: Export to AWS Secrets Manager/SSM when needed

- **CI/CD**
  - **Both platforms**: Direct Git integration with automatic deployments
  - **Migration path**: Extend existing GitHub Actions to deploy to AWS alongside platform deployments

**When to migrate to AWS:**
- Scale beyond 100+ concurrent users
- Need advanced networking (VPC peering, custom routing)
- Require specific compliance certifications
- Need granular cost optimization
- Want multi-region deployment capabilities

**Recommended migration strategy:**
1. **Phase 1**: Deploy MVP on Render/Railway (1-2 weeks)
2. **Phase 2**: Validate product-market fit and gather usage patterns
3. **Phase 3**: Parallel AWS deployment while maintaining platform deployment
4. **Phase 4**: Gradual traffic migration with feature parity validation
5. **Phase 5**: Full cutover to AWS once operational confidence is established

This approach allows for rapid MVP deployment while maintaining a clear path to enterprise-grade AWS infrastructure as the product scales.

### Multi‑tenancy foundations (soft multi‑tenant)

- **Data model**

  - Add tenant_id to tenant‑scoped tables now; create composite indexes (tenant_id, fk).
  - Backfill a DefaultTenant for all existing data; add NOT NULL + FKs after backfill.

- **Enforcement**

  - Resolve tenantId from auth context; pass through request context.
  - Centralize access patterns to always require tenantId (helpers/DAOs). Skip RLS for MVP.

### Identity and basic authorization

- **Authentication**

  - Replace local auth with Cognito Hosted UI (OIDC PKCE). No SSO/federation in MVP.
  - Backend validates JWT via JWKS; cache keys; short token lifetimes with refresh.

- **Authorization**

  - Two roles for MVP: Admin and User. Check guards at route/service level.
  - Tenant binding: map user → org/tenant; include in token claim or lookup on first request.

### Data layer and migration off in‑memory

- Implement DrizzleStorage for Postgres; maintain IStorage interface for clean swap.
- drizzle‑kit migrations in repo; migrations run as a pre‑deploy step in CI.

### App security basics

- Express: helmet, strict CORS allowlist, rate limiting per IP (express-rate-limit in‑memory).
- CSP via ALB/CloudFront headers if using CloudFront; otherwise add nonce‑based CSP headers from app where feasible.
- Dependency and container scanning (Dependabot/Snyk + Trivy).

### Observability (minimum viable)

- **AWS**: Logs: structured JSON logs with requestId, tenantId; shipped to CloudWatch. Metrics/alerts: ALB target 5xx, high latency p95, ECS task restarts; SNS email alerts.
- **Railway**: Built-in application logs, performance metrics, and alerting via email/Slack integration.
- E2E smoke: Playwright checks for login, dashboard load, Joyce stream working in staging.

### What to defer (nice-to-haves or post‑MVP)

- WAF, bot control, and complex edge rules.
- Full RBAC matrix (Owner/Admin/Analyst/Viewer); audit log UI; impersonation workflows.
- Row‑Level Security (RLS) in Postgres; RDS Proxy; Redis/ElastiCache for rate limiting.
- OpenTelemetry traces and SLO/error‑budget program; deep dashboards.
- Multi‑AZ RDS (optional to include if compliance-driven), cross‑region replica, DR drills.
- Stripe billing and tax; usage‑based pricing; formal cost dashboards.
- AI provider abstraction and Bedrock/VPC endpoints; prompt evaluation harness and A/B tests.

### Minimal MVP architecture (textual)

- S3 (SPA) → [optional CloudFront] → Browser
- Browser → ALB (TLS) → ECS Fargate Service (Express API)
- ECS ↔ RDS PostgreSQL (private subnet, TLS)
- ECS → Secrets Manager/SSM (read at startup)
- GitHub Actions → ECR/ECS/S3 (via OIDC)
- CloudWatch Logs for ECS service and ALB metrics/alerts

### Concrete MVP work plan (3--5 weeks)

**Week 1: Foundations and local parity**

- Repo hygiene: TypeScript strict, ESLint/Prettier, basic unit tests, Playwright smoke.
- Dockerize client and server; docker‑compose with local Postgres; implement DrizzleStorage; first migrations.
- Introduce /api/v1 and standard error envelope.

**Week 2: AWS scaffolding and CI/CD**

- Terraform minimal stack: VPC (1 public/1 private per AZ), ALB, ECS Cluster + Service, ECR, RDS single‑AZ, Secrets Manager, IAM roles, ACM cert, Route 53 record.
- GitHub Actions workflow for build/scan/push/deploy + migration step; S3 upload for SPA; domain + TLS working.

**Week 3: Auth + multi‑tenancy groundwork**

- Cognito Hosted UI integration on client; JWKS verification middleware on server.
- Roles: Admin/User; tenant mapping; add tenant_id columns; backfill; enforce tenant scoping in DAOs.
- Replace any in‑memory/session logic with JWT‑based guards; add 401/403 handlers in client.

**Week 4: Security and observability basics**

- helmet, CORS, rate limiting (IP‑based).
- Structured logging to CloudWatch; ALB and ECS alarms (5xx, latency, restarts).
- Playwright E2E in CI against staging; test deploy with zero downtime.

**Week 5: Stabilization and cut**

- k6 or autocannon quick load test for critical endpoints; adjust ECS task size and DB params.
- Incident runbook draft; backup/restore smoke (RDS snapshot → restore to dev).
- Freeze MVP; tag v0.1; promote to prod with manual approval.

### Code‑level changes (MVP‑scope)

- **shared/schema.ts**

  - Add tenant_id; composite indexes; shared Role enum (Admin/User).

- **server/storage.drizzle.ts (new)**

  - Implement IStorage using Drizzle; require tenantId param in all reads/writes; enforce predicates.

- **server/index.ts**

  - JWT verification middleware (Cognito JWKS); request context (requestId, tenantId, userId); helmet/CORS/rate‑limit; JSON logging; /health route.

- **server/routes.ts**

  - Mount under /api/v1; apply Zod validation on inputs; ensure tenant context required.

- **server/joyce-context.ts**

  - Inject tenant configs (basic caps); timeouts; simple retry/backoff; log usage counters.

- **client/src/hooks/use-auth.tsx and client/src/lib/auth.ts**

  - Cognito Hosted UI integration; token handling; 401/403 interceptors; logout; tenant switch stub if user has multiple.

- **CI (.github/workflows/\*)**

  - pr‑checks: lint/typecheck/test/build; deploy: build → scan → ECR push → migrate → ECS update; SPA build → S3 sync → CloudFront invalidate (if used).

- **infra (Terraform minimal modules)**

  - vpc, ecs_svc, ecr, alb, rds_single, secrets, iam, acm, route53, s3.

### MVP acceptance checklist

- **Product**

  - Dashboards render from Postgres data; Joyce responds within p95 < 6s.
  - First tenant onboarded; Admin can invite a user (invite could be manual via Cognito in MVP).

- **Reliability**

  - Zero‑downtime rolling deploy; health checks pass; alarms wired.
  - Basic load test at 50 RPS sustained without error spikes.

- **Security**

  - TLS on public endpoints; helmet/CORS/rate‑limit enabled; no secrets in env files.
  - Dependencies and images scanned; no highs/criticals.

- **Data**

  - All queries scoped with tenantId; RDS automated backups enabled; snapshot restore validated once.

- **DevEx**

  - One‑click deploy to staging from main; manual approval to prod; smoke E2E runs in CI.

### Optional speed-ups if MVP timeline is very aggressive

- Host frontend on Vercel/Netlify for first month; move to S3/CloudFront later.
- Use Neon/Supabase Postgres for first month; migrate to RDS later (keep Drizzle and migrations to smooth the switch).
- Deploy API on Render/Fly.io initially; move to ECS when ready. Keep container and env interfaces consistent to ease migration.

### Items explicitly out-of-scope for MVP (defer)

- WAF, RDS Proxy, ElastiCache/Redis, OTel tracing, SLO/error budgets, complex RBAC, audit UI, billing/Stripe, multi‑region DR, AI provider abstraction/Bedrock, prompt evaluation harness.

This lays down the minimum architectural foundations (Cognito, tenant_id, managed Postgres, CI/CD, basic security) so we avoid high‑effort rework later, while deferring advanced enterprise features until after MVP traction.
