<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Railway Architecture v2 - Rejoyce MVP Enterprise Readiness</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }

    .diagram {
      padding: 36px;
      background: #f8fafc;
    }

    .architecture-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 36px;
    }

    .layer {
      background: white;
      border-radius: 14px;
      padding: 20px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      transition: all 0.25s ease;
    }
    .layer:hover {
      transform: translateY(-4px);
      box-shadow: 0 14px 32px rgba(0, 0, 0, 0.12);
    }

    .layer-title {
      font-size: 1.2rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 3px solid;
    }

    .external-layer { border-color: #3b82f6; }
    .external-layer .layer-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .edge-layer { border-color: #f59e0b; }
    .edge-layer .layer-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .railway-layer { border-color: #8b5cf6; }
    .railway-layer .layer-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .data-layer { border-color: #10b981; }
    .data-layer .layer-title { color: #10b981; border-bottom-color: #10b981; }

    .service-box {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 14px;
      margin-bottom: 12px;
      border-left: 4px solid;
      transition: all 0.2s ease;
    }
    .service-box:hover { transform: translateX(4px); }

    .external-service { border-left-color: #3b82f6; }
    .edge-service { border-left-color: #f59e0b; }
    .railway-service { border-left-color: #8b5cf6; }
    .data-service { border-left-color: #10b981; }

    .service-name {
      font-weight: 700;
      font-size: 1.05rem;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .service-desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section {
      background: white;
      border-radius: 14px;
      padding: 24px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      margin-top: 28px;
    }
    .section-title {
      text-align: center;
      font-size: 1.5rem;
      font-weight: 800;
      color: #1f2937;
      margin-bottom: 16px;
    }

    .flow {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
    }
    .flow-step {
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      color: white;
      border-radius: 12px;
      padding: 18px;
      text-align: center;
      position: relative;
    }
    .flow-step .num {
      width: 28px; height: 28px; border-radius: 50%;
      background: rgba(255,255,255,0.25);
      display: flex; align-items: center; justify-content: center;
      margin: 0 auto 8px; font-weight: 800;
    }
    .flow-step .title { font-weight: 700; margin-bottom: 6px; }
    .flow-step .desc { font-size: 0.9rem; opacity: 0.95; }

    .legend {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      margin-top: 18px;
    }
    .legend-item {
      background: #f8fafc;
      border: 1px dashed #cbd5e1;
      border-radius: 10px;
      padding: 12px;
    }
    .legend-item b { color: #111827; }

    .benefits {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 18px;
      margin-top: 16px;
    }
    .benefit {
      background: white;
      border-radius: 14px;
      padding: 18px;
      border-top: 4px solid;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }
    .benefit:nth-child(1) { border-top-color: #f59e0b; }
    .benefit:nth-child(2) { border-top-color: #ef4444; }
    .benefit:nth-child(3) { border-top-color: #10b981; }
    .benefit:nth-child(4) { border-top-color: #8b5cf6; }
    .benefit .title { font-weight: 800; margin-bottom: 8px; }
    .benefit ul { list-style: none; }
    .benefit li { position: relative; padding-left: 18px; margin: 6px 0; color: #374151; }
    .benefit li::before {
      content: '✓'; position: absolute; left: 0; color: #10b981; font-weight: 900;
    }

    @media (max-width: 1024px) {
      .architecture-grid { grid-template-columns: repeat(2, 1fr); }
    }
    @media (max-width: 640px) {
      .architecture-grid { grid-template-columns: 1fr; }
      .diagram { padding: 20px; }
      .header h1 { font-size: 2rem; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Railway Architecture v2</h1>
      <p>Rejoyce MVP — Enterprise Readiness with Real Auth and Postgres, Cloudflare Edge, Security, and Observability</p>
    </div>

    <div class="diagram">
      <div class="architecture-grid">

        <!-- External Services -->
        <div class="layer external-layer">
          <div class="layer-title">External Services</div>

          <div class="service-box external-service">
            <div class="service-name">👥 Users</div>
            <div class="service-desc">Browsers connect over HTTPS via Cloudflare to the Railway origin</div>
          </div>

          <div class="service-box external-service">
            <div class="service-name">🔐 Identity Provider (optional)</div>
            <div class="service-desc">Auth0/Clerk/Cognito for OIDC/OAuth tokens and managed auth flows; alternative is self-managed sessions in-app</div>
          </div>

          <div class="service-box external-service">
            <div class="service-name">📚 GitHub</div>
            <div class="service-desc">Source of truth; Actions for build, test, lint, audit, scan, and gated deploys</div>
          </div>

          <div class="service-box external-service">
            <div class="service-name">🤖 Anthropic Claude</div>
            <div class="service-desc">AI model for Joyce chat via server-side API calls; key managed in Railway Variables</div>
          </div>

          <div class="service-box external-service">
            <div class="service-name">📈 Log/Alert Provider</div>
            <div class="service-desc">Centralized logging and error monitoring (e.g., Logtail/Datadog + Sentry)</div>
          </div>
        </div>

        <!-- Edge (Cloudflare) -->
        <div class="layer edge-layer">
          <div class="layer-title">Edge (Cloudflare)</div>

          <div class="service-box edge-service">
            <div class="service-name">🛡️ TLS & HTTPS</div>
            <div class="service-desc">Full (strict) TLS, Always Use HTTPS, TLS 1.3, HSTS (prod)</div>
          </div>

          <div class="service-box edge-service">
            <div class="service-name">🧱 WAF & Bots</div>
            <div class="service-desc">Managed WAF rules, Bot protections, targeted rate limits (e.g., /api/auth/login)</div>
          </div>

          <div class="service-box edge-service">
            <div class="service-name">⚡ Caching & Performance</div>
            <div class="service-desc">Cache static assets; bypass /api and HTML; Brotli, HTTP/3, Early Hints</div>
          </div>

          <div class="service-box edge-service">
            <div class="service-name">🌐 DNS (Proxied)</div>
            <div class="service-desc">CNAME app.example.com → Railway target with orange cloud (proxy ON)</div>
          </div>
        </div>

        <!-- Railway Platform -->
        <div class="layer railway-layer">
          <div class="layer-title">Railway Platform</div>

          <div class="service-box railway-service">
            <div class="service-name">📱 Web Service (Frontend)</div>
            <div class="service-desc">Vite-built SPA served by Express; zero-downtime deploys with health checks</div>
          </div>

          <div class="service-box railway-service">
            <div class="service-name">⚙️ API Service (Backend)</div>
            <div class="service-desc">Node.js + Express with Helmet, CORS allowlist, rate limiting, validation, structured logs</div>
          </div>

          <div class="service-box railway-service">
            <div class="service-name">🔑 Authentication</div>
            <div class="service-desc">Session-based auth with Postgres session store (connect-pg-simple) and bcrypt-hashed passwords; or OIDC token verification when using an external IdP</div>
          </div>

          <div class="service-box railway-service">
            <div class="service-name">🔐 Config & Secrets</div>
            <div class="service-desc">Railway Variables per env (dev/stage/prod): ANTHROPIC_API_KEY, CORS_ALLOWED_ORIGINS, SESSION_SECRET, POSTGRES_URL, RATE_LIMIT_*, etc.</div>
          </div>

          <div class="service-box railway-service">
            <div class="service-name">🏁 Environments & Approvals</div>
            <div class="service-desc">Dev auto-deploy; stage on main; prod requires approval/tag. Team permissions restricted</div>
          </div>

          <div class="service-box railway-service">
            <div class="service-name">📊 Observability</div>
            <div class="service-desc">Log drain to provider, error monitoring (Sentry), SLO-based alerts for latency/error rate</div>
          </div>
        </div>

        <!-- Data & Storage -->
        <div class="layer data-layer">
          <div class="layer-title">Data & Storage</div>

          <div class="service-box data-service">
            <div class="service-name">🐘 PostgreSQL (Managed)</div>
            <div class="service-desc">SSL required; least-privilege app user; internal networking where possible</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">🧩 Schema & Migrations</div>
            <div class="service-desc">Drizzle ORM migrations on deploy; versioned changes; validation in CI</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">🔏 Session Store</div>
            <div class="service-desc">Sessions persisted in Postgres (connect-pg-simple) for real authentication and logout</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">🗄️ Backups & DR</div>
            <div class="service-desc">Automated backups (and PITR if available); monthly restore tests to staging</div>
          </div>
        </div>

      </div>

      <!-- Request & Deploy Flow -->
      <div class="section">
        <div class="section-title">Request and Deploy Flow</div>
        <div class="flow">
          <div class="flow-step">
            <div class="num">1</div>
            <div class="title">User Request</div>
            <div class="desc">Browser → Cloudflare (TLS, WAF, caching) → Railway Web/API</div>
          </div>
          <div class="flow-step">
            <div class="num">2</div>
            <div class="title">AuthN & Session</div>
            <div class="desc">OIDC ID token validated (IdP) or session verified (Postgres session store)</div>
          </div>
          <div class="flow-step">
            <div class="num">3</div>
            <div class="title">Health & Routing</div>
            <div class="desc">Railway healthcheck (/health) gates traffic; zero-downtime rollout</div>
          </div>
          <div class="flow-step">
            <div class="num">4</div>
            <div class="title">Data Access</div>
            <div class="desc">API → Postgres via internal/private networking with SSL; migrations enforced</div>
          </div>
          <div class="flow-step">
            <div class="num">5</div>
            <div class="title">Observability</div>
            <div class="desc">Structured logs → log drain; errors → Sentry; alerts on SLOs</div>
          </div>
          <div class="flow-step">
            <div class="num">6</div>
            <div class="title">CI/CD</div>
            <div class="desc">GitHub Actions: build, test, lint, audit, image scan → stage → approvals → prod</div>
          </div>
        </div>
        <div class="legend">
          <div class="legend-item"><b>Auth:</b> Session-based auth (Postgres store) with bcrypt, or OIDC IdP token verification</div>
          <div class="legend-item"><b>Security:</b> Helmet (CSP/HSTS), strict CORS, rate limiting, input validation</div>
          <div class="legend-item"><b>Edge:</b> Full (strict) TLS, HSTS, WAF, bot controls, cache rules</div>
          <div class="legend-item"><b>Resilience:</b> HA instances (prod), auto-restarts, graceful shutdown on SIGTERM</div>
          <div class="legend-item"><b>Compliance:</b> Secrets rotated; approvals required for prod deploy</div>
        </div>
      </div>

      <!-- Benefits -->
      <div class="section">
        <div class="section-title">Benefits</div>
        <div class="benefits">
          <div class="benefit">
            <div class="title">Security</div>
            <ul>
              <li>Real authentication with sessions or IdP tokens</li>
              <li>Edge WAF/Bot protection with strict TLS and HSTS</li>
              <li>App-level headers, CORS, and rate limiting</li>
              <li>Secrets in env; no credentials in code</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">Reliability</div>
            <ul>
              <li>Rolling deploys guarded by health checks</li>
              <li>HA instances and auto-restart policies</li>
              <li>Backups and restore validation</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">Performance</div>
            <ul>
              <li>Static asset caching at the edge</li>
              <li>HTTP/3, Brotli, Early Hints</li>
              <li>Proximity routing via Cloudflare</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">Governance</div>
            <ul>
              <li>Environments with prod approvals</li>
              <li>Centralized logs, SLO alerts, and runbooks</li>
              <li>CI/CD gates and image scanning</li>
            </ul>
          </div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
