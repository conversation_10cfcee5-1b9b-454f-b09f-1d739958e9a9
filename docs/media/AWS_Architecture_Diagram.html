<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS Architecture - Rejoyce MVP</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #232f3e 0%, #131a22 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .diagram-container {
            padding: 40px;
            background: #f8fafc;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: 1fr 3fr 1fr;
            gap: 30px;
            margin-bottom: 40px;
        }

        .layer {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .layer:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .layer-title {
            font-size: 1.4rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            padding-bottom: 15px;
            border-bottom: 3px solid;
        }

        .external-layer {
            border-color: #3b82f6;
        }

        .external-layer .layer-title {
            color: #3b82f6;
            border-bottom-color: #3b82f6;
        }

        .aws-layer {
            border-color: #ff9900;
        }

        .aws-layer .layer-title {
            color: #ff9900;
            border-bottom-color: #ff9900;
        }

        .security-layer {
            border-color: #dc2626;
        }

        .security-layer .layer-title {
            color: #dc2626;
            border-bottom-color: #dc2626;
        }

        .aws-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .aws-section {
            background: #fff7ed;
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #fed7aa;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #ea580c;
            margin-bottom: 15px;
            text-align: center;
            border-bottom: 2px solid #fed7aa;
            padding-bottom: 8px;
        }

        .service-box {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .service-box:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .external-service {
            border-left-color: #3b82f6;
        }

        .aws-service {
            border-left-color: #ff9900;
        }

        .security-service {
            border-left-color: #dc2626;
        }

        .service-name {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 5px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .service-desc {
            font-size: 0.9rem;
            color: #64748b;
            line-height: 1.4;
        }

        .vpc-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 2px solid #f59e0b;
        }

        .vpc-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #92400e;
            text-align: center;
            margin-bottom: 20px;
        }

        .subnet-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .subnet {
            background: white;
            border-radius: 10px;
            padding: 15px;
            border: 2px solid;
        }

        .public-subnet {
            border-color: #10b981;
        }

        .private-subnet {
            border-color: #6366f1;
        }

        .subnet-title {
            font-weight: 600;
            margin-bottom: 10px;
            text-align: center;
        }

        .public-subnet .subnet-title {
            color: #10b981;
        }

        .private-subnet .subnet-title {
            color: #6366f1;
        }

        .flow-section {
            margin-top: 40px;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .flow-title {
            font-size: 1.8rem;
            font-weight: 700;
            text-align: center;
            margin-bottom: 30px;
            color: #2d3748;
        }

        .flow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
        }

        .flow-step {
            background: linear-gradient(135deg, #ff9a56 0%, #ff6b35 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
        }

        .flow-step:hover {
            transform: scale(1.05);
        }

        .flow-step::after {
            content: '→';
            position: absolute;
            right: -12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.3rem;
            color: #ff9a56;
            font-weight: bold;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .step-number {
            background: rgba(255, 255, 255, 0.2);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 8px;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .step-title {
            font-weight: 600;
            margin-bottom: 6px;
            font-size: 0.95rem;
        }

        .step-desc {
            font-size: 0.8rem;
            opacity: 0.9;
        }

        .comparison-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
        }

        .comparison-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            border-top: 4px solid;
            transition: all 0.3s ease;
        }

        .comparison-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
        }

        .comparison-card:nth-child(1) { border-top-color: #ff9900; }
        .comparison-card:nth-child(2) { border-top-color: #dc2626; }
        .comparison-card:nth-child(3) { border-top-color: #059669; }
        .comparison-card:nth-child(4) { border-top-color: #7c3aed; }

        .comparison-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .comparison-list {
            list-style: none;
        }

        .comparison-list li {
            padding: 8px 0;
            padding-left: 20px;
            position: relative;
            color: #4a5568;
        }

        .comparison-list li::before {
            content: '⚙️';
            position: absolute;
            left: 0;
            font-size: 0.9rem;
        }

        .icon {
            font-size: 1.5rem;
        }

        @media (max-width: 1024px) {
            .architecture-grid {
                grid-template-columns: 1fr;
            }
            
            .aws-grid {
                grid-template-columns: 1fr;
            }
            
            .subnet-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .diagram-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>☁️ AWS Architecture</h1>
            <p>Rejoyce MVP - Enterprise-Grade Cloud Infrastructure</p>
        </div>

        <div class="diagram-container">
            <div class="architecture-grid">
                <!-- External Services -->
                <div class="layer external-layer">
                    <div class="layer-title">🌐 External Services</div>
                    
                    <div class="service-box external-service">
                        <div class="service-name">
                            <span class="icon">👥</span>
                            Users
                        </div>
                        <div class="service-desc">Web browsers accessing via CloudFront CDN and HTTPS</div>
                    </div>

                    <div class="service-box external-service">
                        <div class="service-name">
                            <span class="icon">📚</span>
                            GitHub
                        </div>
                        <div class="service-desc">Source code with OIDC trust to AWS for secure deployments</div>
                    </div>

                    <div class="service-box external-service">
                        <div class="service-name">
                            <span class="icon">🤖</span>
                            Anthropic Claude
                        </div>
                        <div class="service-desc">AI service for Joyce chat with potential VPC endpoints</div>
                    </div>
                </div>

                <!-- AWS Platform -->
                <div class="layer aws-layer">
                    <div class="layer-title">☁️ AWS Platform</div>
                    
                    <div class="aws-grid">
                        <div class="aws-section">
                            <div class="section-title">Frontend & CDN</div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">🪣</span>
                                    S3 Bucket
                                </div>
                                <div class="service-desc">Static website hosting for React SPA build</div>
                            </div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">🌍</span>
                                    CloudFront
                                </div>
                                <div class="service-desc">Global CDN with edge caching and custom domain</div>
                            </div>
                        </div>

                        <div class="aws-section">
                            <div class="section-title">Compute & Load Balancing</div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">⚖️</span>
                                    Application Load Balancer
                                </div>
                                <div class="service-desc">HTTPS termination with ACM certificates</div>
                            </div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">🐳</span>
                                    ECS Fargate
                                </div>
                                <div class="service-desc">Containerized Node.js API with auto-scaling</div>
                            </div>
                        </div>

                        <div class="aws-section">
                            <div class="section-title">Data & Storage</div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">🗄️</span>
                                    RDS PostgreSQL
                                </div>
                                <div class="service-desc">Managed database with automated backups</div>
                            </div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">📦</span>
                                    ECR
                                </div>
                                <div class="service-desc">Container registry for Docker images</div>
                            </div>
                        </div>

                        <div class="aws-section">
                            <div class="section-title">Security & Config</div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">🔐</span>
                                    Secrets Manager
                                </div>
                                <div class="service-desc">Encrypted secrets and API keys</div>
                            </div>
                            <div class="service-box aws-service">
                                <div class="service-name">
                                    <span class="icon">⚙️</span>
                                    SSM Parameter Store
                                </div>
                                <div class="service-desc">Configuration management</div>
                            </div>
                        </div>
                    </div>

                    <!-- VPC Section -->
                    <div class="vpc-section">
                        <div class="vpc-title">🏗️ VPC Network Architecture</div>
                        <div class="subnet-grid">
                            <div class="subnet public-subnet">
                                <div class="subnet-title">🌐 Public Subnets</div>
                                <div class="service-box aws-service">
                                    <div class="service-name">Application Load Balancer</div>
                                    <div class="service-desc">Internet-facing with public IPs</div>
                                </div>
                                <div class="service-box aws-service">
                                    <div class="service-name">NAT Gateway</div>
                                    <div class="service-desc">Outbound internet for private resources</div>
                                </div>
                            </div>
                            <div class="subnet private-subnet">
                                <div class="subnet-title">🔒 Private Subnets</div>
                                <div class="service-box aws-service">
                                    <div class="service-name">ECS Tasks</div>
                                    <div class="service-desc">API containers with no direct internet</div>
                                </div>
                                <div class="service-box aws-service">
                                    <div class="service-name">RDS Database</div>
                                    <div class="service-desc">Isolated database instances</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security & Monitoring -->
                <div class="layer security-layer">
                    <div class="layer-title">🛡️ Security & Monitoring</div>
                    
                    <div class="service-box security-service">
                        <div class="service-name">
                            <span class="icon">🔒</span>
                            Security Groups
                        </div>
                        <div class="service-desc">Least privilege network access control</div>
                    </div>

                    <div class="service-box security-service">
                        <div class="service-name">
                            <span class="icon">🏛️</span>
                            IAM Roles
                        </div>
                        <div class="service-desc">Fine-grained permissions for services</div>
                    </div>

                    <div class="service-box security-service">
                        <div class="service-name">
                            <span class="icon">📊</span>
                            CloudWatch
                        </div>
                        <div class="service-desc">Logs, metrics, and alerting with SNS</div>
                    </div>

                    <div class="service-box security-service">
                        <div class="service-name">
                            <span class="icon">🔍</span>
                            AWS Config
                        </div>
                        <div class="service-desc">Compliance monitoring and auditing</div>
                    </div>
                </div>
            </div>

            <!-- Deployment Flow -->
            <div class="flow-section">
                <div class="flow-title">🚀 CI/CD Pipeline</div>
                <div class="flow-steps">
                    <div class="flow-step">
                        <div class="step-number">1</div>
                        <div class="step-title">Code Push</div>
                        <div class="step-desc">Git push triggers GitHub Actions</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">2</div>
                        <div class="step-title">Build & Test</div>
                        <div class="step-desc">Lint, typecheck, unit tests</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">3</div>
                        <div class="step-title">Docker Build</div>
                        <div class="step-desc">Container image creation</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">4</div>
                        <div class="step-title">Security Scan</div>
                        <div class="step-desc">Trivy vulnerability scanning</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">5</div>
                        <div class="step-title">Push to ECR</div>
                        <div class="step-desc">Store container in registry</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">6</div>
                        <div class="step-title">Run Migrations</div>
                        <div class="step-desc">Database schema updates</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">7</div>
                        <div class="step-title">ECS Deploy</div>
                        <div class="step-desc">Rolling update with health checks</div>
                    </div>
                    <div class="flow-step">
                        <div class="step-number">8</div>
                        <div class="step-title">S3 Sync</div>
                        <div class="step-desc">Frontend assets to S3/CloudFront</div>
                    </div>
                </div>
            </div>

            <!-- AWS Benefits -->
            <div class="comparison-section">
                <div class="comparison-card">
                    <div class="comparison-title">
                        <span class="icon">🏢</span>
                        Enterprise Scale
                    </div>
                    <ul class="comparison-list">
                        <li>Global infrastructure with 99.99% SLA</li>
                        <li>Auto-scaling to handle traffic spikes</li>
                        <li>Multi-AZ deployment for high availability</li>
                        <li>Enterprise-grade security and compliance</li>
                    </ul>
                </div>

                <div class="comparison-card">
                    <div class="comparison-title">
                        <span class="icon">🔧</span>
                        Advanced Features
                    </div>
                    <ul class="comparison-list">
                        <li>VPC for network isolation and control</li>
                        <li>Fine-grained IAM permissions</li>
                        <li>Advanced monitoring and alerting</li>
                        <li>Disaster recovery capabilities</li>
                    </ul>
                </div>

                <div class="comparison-card">
                    <div class="comparison-title">
                        <span class="icon">💰</span>
                        Cost Optimization
                    </div>
                    <ul class="comparison-list">
                        <li>Pay-per-use with reserved instances</li>
                        <li>Spot instances for cost savings</li>
                        <li>Detailed cost analysis and budgets</li>
                        <li>Resource optimization recommendations</li>
                    </ul>
                </div>

                <div class="comparison-card">
                    <div class="comparison-title">
                        <span class="icon">🔒</span>
                        Security & Compliance
                    </div>
                    <ul class="comparison-list">
                        <li>SOC, PCI, HIPAA compliance ready</li>
                        <li>WAF and DDoS protection available</li>
                        <li>Encryption at rest and in transit</li>
                        <li>Audit trails and compliance reporting</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
