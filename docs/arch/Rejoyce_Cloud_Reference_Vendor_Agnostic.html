<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Rejoyce Cloud Reference Architecture (Vendor-Agnostic)</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }

    .diagram { padding: 36px; background: #f8fafc; }

    .grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 18px;
      margin-bottom: 28px;
    }

    .layer {
      background: white;
      border-radius: 14px;
      padding: 18px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      transition: all 0.25s ease;
      min-height: 280px;
    }
    .layer:hover { transform: translateY(-4px); box-shadow: 0 14px 32px rgba(0,0,0,0.12); }

    .layer-title {
      font-size: 1.1rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 12px;
      padding-bottom: 10px;
      border-bottom: 3px solid;
    }

    .edge { border-color: #3b82f6; }
    .edge .layer-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .identity { border-color: #ef4444; }
    .identity .layer-title { color: #ef4444; border-bottom-color: #ef4444; }

    .compute { border-color: #8b5cf6; }
    .compute .layer-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .data { border-color: #10b981; }
    .data .layer-title { color: #10b981; border-bottom-color: #10b981; }

    .messaging { border-color: #f59e0b; }
    .messaging .layer-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .observability { border-color: #06b6d4; }
    .observability .layer-title { color: #06b6d4; border-bottom-color: #06b6d4; }

    .cicd { border-color: #64748b; }
    .cicd .layer-title { color: #334155; border-bottom-color: #64748b; }

    .card {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 12px;
      margin-bottom: 10px;
      border-left: 4px solid #cbd5e1;
      transition: all 0.2s ease;
    }
    .card:hover { transform: translateX(4px); }
    .name { font-weight: 800; margin-bottom: 6px; display: flex; align-items: center; gap: 8px; }
    .desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section { background: white; border-radius: 14px; padding: 24px; box-shadow: 0 8px 24px rgba(0,0,0,0.08); margin-top: 24px; }
    .section-title { text-align: center; font-size: 1.4rem; font-weight: 800; color: #1f2937; margin-bottom: 14px; }

    .legend {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
    }
    .legend-item { background: #f8fafc; border: 1px dashed #cbd5e1; border-radius: 10px; padding: 12px; }
    .legend-item b { color: #111827; }

    .mapping {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 16px;
      margin-top: 12px;
    }
    .map-col { background: #ffffff; border: 1px solid #e5e7eb; border-radius: 10px; padding: 14px; }
    .map-col h4 { margin-bottom: 8px; color: #111827; }
    .map-col ul { list-style: none; }
    .map-col li { color: #374151; margin: 6px 0; }

    @media (max-width: 1200px) { .grid { grid-template-columns: repeat(2, 1fr); } }
    @media (max-width: 640px)  { .grid { grid-template-columns: 1fr; } .diagram { padding: 20px; } .header h1 { font-size: 2rem; } }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Cloud Reference Architecture (Vendor-Agnostic)</h1>
      <p>Modular layout that maps cleanly to any major cloud or platform-as-a-service</p>
    </div>

    <div class="diagram">
      <div class="grid">
        <!-- Edge & Networking -->
        <div class="layer edge">
          <div class="layer-title">Edge & Networking</div>
          <div class="card">
            <div class="name">🌐 DNS & CDN/Edge</div>
            <div class="desc">Managed DNS, global CDN for SPA assets, TLS termination, caching rules</div>
          </div>
          <div class="card">
            <div class="name">🧱 WAF & DDoS</div>
            <div class="desc">Managed WAF rules, bot protection, geo/IP rules, rate limits on auth</div>
          </div>
          <div class="card">
            <div class="name">🔒 Zero‑Trust Perimeter</div>
            <div class="desc">Private networking for services and data planes; public ingress/gateway only</div>
          </div>
        </div>

        <!-- Identity & Access -->
        <div class="layer identity">
          <div class="layer-title">Identity & Access</div>
          <div class="card">
            <div class="name">🔐 OIDC/OAuth2 IDP</div>
            <div class="desc">Hosted login, PKCE, organization/tenant claims, SCIM (later)</div>
          </div>
          <div class="card">
            <div class="name">🧩 AuthN & AuthZ</div>
            <div class="desc">JWT/JWKS validation, role-based + attribute-based access; tenant binding</div>
          </div>
          <div class="card">
            <div class="name">🔑 Secrets & KMS</div>
            <div class="desc">Central secret store and key management; no secrets in source or images</div>
          </div>
        </div>

        <!-- Compute & Ingress -->
        <div class="layer compute">
          <div class="layer-title">Compute & Ingress</div>
          <div class="card">
            <div class="name">📱 SPA Hosting</div>
            <div class="desc">Static site hosting for React/Vite build with edge cache</div>
          </div>
          <div class="card">
            <div class="name">⚙️ API Runtime</div>
            <div class="desc">Container service and/or functions for Express API and workers</div>
          </div>
          <div class="card">
            <div class="name">🚪 Ingress / API Gateway</div>
            <div class="desc">Path-based routing, health checks, mutual TLS (optional), canary deploys</div>
          </div>
        </div>

        <!-- Data Services -->
        <div class="layer data">
          <div class="layer-title">Data Services</div>
          <div class="card">
            <div class="name">🗄️ Operational Database (Relational)</div>
            <div class="desc">Managed relational DB (tenant-scoped; optional vector support)</div>
          </div>
          <div class="card">
            <div class="name">🪣 Data Lake (Bronze/Silver/Gold)</div>
            <div class="desc"><b>Bronze</b> (raw) → <b>Silver</b> (standardized) → <b>Gold</b> (curated, lakehouse); lifecycle & encryption policies</div>
          </div>
          <div class="card">
            <div class="name">🏗️ Analytics Warehouse</div>
            <div class="desc">Serverless warehouse or query engine querying <b>Gold</b> (curated) datasets</div>
          </div>
          <div class="card">
            <div class="name">🧱 Transforms</div>
            <div class="desc">Canonical models, medians/quartiles, snapshots; CI‑driven</div>
          </div>
        </div>

        <!-- Messaging & Orchestration -->
        <div class="layer messaging">
          <div class="layer-title">Messaging & Orchestration</div>
          <div class="card">
            <div class="name">📬 Queue & Topics</div>
            <div class="desc">Asynchronous tasks, fan‑out events, retry/backoff for resilience</div>
          </div>
          <div class="card">
            <div class="name">⏱️ Scheduler</div>
            <div class="desc">Time‑based triggers for daily/weekly/monthly/quarterly pipelines</div>
          </div>
          <div class="card">
            <div class="name">🧭 Workflow</div>
            <div class="desc">State machine/DAG for multi‑step ingestions and validations</div>
          </div>
        </div>

        <!-- Observability & Security -->
        <div class="layer observability">
          <div class="layer-title">Observability & Security</div>
          <div class="card">
            <div class="name">📈 Metrics & Logs</div>
            <div class="desc">Structured app logs, request traces (later), SLO alerts, job telemetry</div>
          </div>
          <div class="card">
            <div class="name">🔍 Posture & Audit</div>
            <div class="desc">Image/dependency scans, IAM analyzer, infra & data access audit trail</div>
          </div>
          <div class="card">
            <div class="name">🧩 Data Quality</div>
            <div class="desc">Freshness SLAs, anomaly checks, Fog Score surfacing</div>
          </div>
        </div>

        <!-- CI/CD & Artifacts -->
        <div class="layer cicd">
          <div class="layer-title">CI/CD & Artifacts</div>
          <div class="card">
            <div class="name">🏗️ Build & Test</div>
            <div class="desc">Lint/typecheck/unit/E2E; dbt tests; container image build & scan</div>
          </div>
          <div class="card">
            <div class="name">📦 Artifact Registry</div>
            <div class="desc">Container registry and static assets; provenance (SBOM) tracked</div>
          </div>
          <div class="card">
            <div class="name">🚀 Deploy & Migrate</div>
            <div class="desc">Blue/green or rolling; run DB migrations; invalidate CDN</div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Deployment Considerations</div>
        <div class="legend">
          <div class="legend-item"><b>Portability:</b> Map each capability (edge, compute, data, orchestration, observability, secrets) to your chosen provider's managed services.</div>
          <div class="legend-item"><b>Security:</b> Enforce least-privilege access, centralized secrets, KMS-backed encryption, and private networking.</div>
          <div class="legend-item"><b>Operations:</b> Prefer serverless/managed where possible; adopt standardized CI/CD, image scanning, and infrastructure as code.</div>
          <div class="legend-item"><b>Data:</b> Use object storage zones (raw/staged/curated), a SQL warehouse or query engine over Parquet, and dbt for transforms.</div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">End-to-End Request/Data Flow</div>
        <div class="legend">
          <div class="legend-item"><b>1. Browser → Edge:</b> TLS, WAF, CDN cache for SPA; API calls bypass cache</div>
          <div class="legend-item"><b>2. Edge → Ingress:</b> Route to API runtime; health checks control rollout</div>
          <div class="legend-item"><b>3. API → Data:</b> Operational relational DB (tenant‑scoped) for product; Data Lake <b>Bronze/Silver/Gold</b> for pipelines; warehouse/query over <b>Gold</b></div>
          <div class="legend-item"><b>4. Jobs:</b> Schedules/queues trigger ingestion → validation → transform → publish</div>
          <div class="legend-item"><b>5. Observability:</b> Logs/metrics/alerts on SLOs; job telemetry with SLAs</div>
          <div class="legend-item"><b>6. Security:</b> OIDC tokens, least‑privilege IAM, secrets in manager, KMS everywhere</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Examples (Chosen + Options) -->
  <div class="section">
    <div class="section-title">Examples: Chosen + Options</div>
    <div class="legend">
      <div class="legend-item"><b>Chosen UI:</b> React (SPA), TypeScript, Vite, Tailwind; Routing: Wouter</div>
      <div class="legend-item"><b>Chosen API:</b> Node.js + Express (REST; streaming)</div>
      <div class="legend-item"><b>Chosen Data:</b> PostgreSQL (operational), Drizzle ORM; Vector store: pgvector (or compatible)</div>
      <div class="legend-item"><b>Chosen AI:</b> AI SDK + Anthropic Claude (streaming)</div>
      <div class="legend-item"><b>Data Lake:</b> Bronze/Silver/Gold lakehouse; warehouse/query over Gold</div>
      <div class="legend-item"><b>Edge/Identity (options):</b> CDN/WAF/TLS; OIDC IdP (Auth0/Clerk/Cognito)</div>
      <div class="legend-item"><b>Orchestration (options):</b> Scheduler/cron; workflow engine (state machine/DAG)</div>
      <div class="legend-item"><b>Warehouse/Query (options):</b> Serverless SQL warehouse or query engine over Parquet</div>
      <div class="legend-item"><b>Observability (options):</b> Central logs/metrics, error monitoring, image/dependency scans</div>
    </div>
  </div>

</body>
</html>
