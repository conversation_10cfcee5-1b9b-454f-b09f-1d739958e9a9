<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Rejoyce System Overview - High-Level Architecture</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }

    .diagram {
      padding: 36px;
      background: #f8fafc;
    }

    .architecture-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 36px;
    }

    .layer {
      background: white;
      border-radius: 14px;
      padding: 20px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      transition: all 0.25s ease;
    }
    .layer:hover {
      transform: translateY(-4px);
      box-shadow: 0 14px 32px rgba(0, 0, 0, 0.12);
    }

    .layer-title {
      font-size: 1.2rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 3px solid;
    }

    .users-layer { border-color: #3b82f6; }
    .users-layer .layer-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .product-layer { border-color: #8b5cf6; }
    .product-layer .layer-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .data-layer { border-color: #10b981; }
    .data-layer .layer-title { color: #10b981; border-bottom-color: #10b981; }

    .integrations-layer { border-color: #f59e0b; }
    .integrations-layer .layer-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .service-box {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 14px;
      margin-bottom: 12px;
      border-left: 4px solid;
      transition: all 0.2s ease;
    }
    .service-box:hover { transform: translateX(4px); }

    .users-service { border-left-color: #3b82f6; }
    .product-service { border-left-color: #8b5cf6; }
    .data-service { border-left-color: #10b981; }
    .integrations-service { border-left-color: #f59e0b; }

    .service-name {
      font-weight: 700;
      font-size: 1.05rem;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .service-desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section {
      background: white;
      border-radius: 14px;
      padding: 24px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      margin-top: 28px;
    }
    .section-title {
      text-align: center;
      font-size: 1.5rem;
      font-weight: 800;
      color: #1f2937;
      margin-bottom: 16px;
    }

    .flow {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
    }
    .flow-step {
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      color: white;
      border-radius: 12px;
      padding: 18px;
      text-align: center;
      position: relative;
    }
    .flow-step .num {
      width: 28px; height: 28px; border-radius: 50%;
      background: rgba(255,255,255,0.25);
      display: flex; align-items: center; justify-content: center;
      margin: 0 auto 8px; font-weight: 800;
    }
    .flow-step .title { font-weight: 700; margin-bottom: 6px; }
    .flow-step .desc { font-size: 0.9rem; opacity: 0.95; }

    .legend {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      margin-top: 18px;
    }
    .legend-item {
      background: #f8fafc;
      border: 1px dashed #cbd5e1;
      border-radius: 10px;
      padding: 12px;
    }
    .legend-item b { color: #111827; }

    .benefits {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
      gap: 18px;
      margin-top: 16px;
    }
    .benefit {
      background: white;
      border-radius: 14px;
      padding: 18px;
      border-top: 4px solid;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
    }
    .benefit:nth-child(1) { border-top-color: #10b981; }
    .benefit:nth-child(2) { border-top-color: #8b5cf6; }
    .benefit:nth-child(3) { border-top-color: #f59e0b; }
    .benefit:nth-child(4) { border-top-color: #3b82f6; }
    .benefit .title { font-weight: 800; margin-bottom: 8px; }
    .benefit ul { list-style: none; }
    .benefit li { position: relative; padding-left: 18px; margin: 6px 0; color: #374151; }
    .benefit li::before {
      content: '✓'; position: absolute; left: 0; color: #10b981; font-weight: 900;
    }

    @media (max-width: 1024px) {
      .architecture-grid { grid-template-columns: repeat(2, 1fr); }
    }
    @media (max-width: 640px) {
      .architecture-grid { grid-template-columns: 1fr; }
      .diagram { padding: 20px; }
      .header h1 { font-size: 2rem; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Rejoyce System Overview</h1>
      <p>High-level product, data, and AI architecture — from user request to insight and alert</p>
    </div>

    <div class="diagram">
      <div class="architecture-grid">

        <!-- Users & Channels -->
        <div class="layer users-layer">
          <div class="layer-title">Users & Channels</div>

          <div class="service-box users-service">
            <div class="service-name">👥 Roles & Personas</div>
            <div class="service-desc">CEO, CFO, COO, CHRO, CMO, Analysts — tailored dashboards, alerts, and Joyce access</div>
          </div>

          <div class="service-box users-service">
            <div class="service-name">🌐 Web App (SPA)</div>
            <div class="service-desc">Single‑page application UI for Digital Mirror, ERA/SEI, Benchmark Explorer</div>
          </div>

          <div class="service-box users-service">
            <div class="service-name">🔔 Alerts & Reports</div>
            <div class="service-desc">In-app feed, email/Slack digests; daily/weekly/monthly/quarterly cadences</div>
          </div>
        </div>

        <!-- Product Services -->
        <div class="layer product-layer">
          <div class="layer-title">Product Services</div>

          <div class="service-box product-service">
            <div class="service-name">⚙️ API Service</div>
            <div class="service-desc">REST API for KPIs, benchmarks, alerts, Joyce streaming</div>
          </div>

          <div class="service-box product-service">
            <div class="service-name">🧠 Joyce Agent</div>
            <div class="service-desc">Conversational analysis with RAG over tenant context + benchmark library; streaming responses</div>
          </div>

          <div class="service-box product-service">
            <div class="service-name">📊 ERA/SEI Engine</div>
            <div class="service-desc">Computation of Joy/Agility indices and executive summaries with sector context</div>
          </div>

          <div class="service-box product-service">
            <div class="service-name">📣 Notification Rules</div>
            <div class="service-desc">Rule evaluation for events and cadences; role-based delivery to channels</div>
          </div>
        </div>

        <!-- Data & Analytics -->
        <div class="layer data-layer">
          <div class="layer-title">Data & Analytics</div>

          <div class="service-box data-service">
            <div class="service-name">🗄️ Operational Database (Relational)</div>
            <div class="service-desc">Tenant-scoped product data, KPIs, indices, alerts</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">📚 Benchmark Library</div>
            <div class="service-desc">Sector medians, quartiles, distributions; provenance, sample sizes, confidence (Fog Score)</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">🪣 Data Lake (Bronze/Silver/Gold)</div>
            <div class="service-desc"><b>Bronze</b> (raw) → <b>Silver</b> (standardized) → <b>Gold</b> (curated, lakehouse)</div>
          </div>

          <div class="service-box data-service">
            <div class="service-name">🧱 Analytics & Modeling</div>
            <div class="service-desc">Transformation and aggregation layer; warehouse-ready models</div>
          </div>
        </div>

        <!-- Integrations & AI -->
        <div class="layer integrations-layer">
          <div class="layer-title">Integrations & AI</div>

          <div class="service-box integrations-service">
            <div class="service-name">🔐 Identity Provider</div>
            <div class="service-desc">OIDC-compliant Identity Provider for auth; roles mapped to tenant permissions</div>
          </div>

          <div class="service-box integrations-service">
            <div class="service-name">🤖 AI Provider</div>
            <div class="service-desc">LLM provider for responses; embeddings for RAG</div>
          </div>

          <div class="service-box integrations-service">
            <div class="service-name">📈 Public Data Sources</div>
            <div class="service-desc">Public filings, macroeconomic and industry reports, market/talent/ESG datasets</div>
          </div>

          <div class="service-box integrations-service">
            <div class="service-name">🏢 Client Connectors</div>
            <div class="service-desc">Object storage/SFTP file ingest, API connectors (CRM/ERP/HRIS); schema contracts and validation</div>
          </div>
        </div>

      </div>

      <!-- End-to-End Insight Flow -->
      <div class="section">
        <div class="section-title">End-to-End Insight Flow</div>
        <div class="flow">
          <div class="flow-step">
            <div class="num">1</div>
            <div class="title">Ingest</div>
            <div class="desc">Public feeds + client uploads land to raw; contracts validated; Fog Score initialized</div>
          </div>
          <div class="flow-step">
            <div class="num">2</div>
            <div class="title">Transform</div>
            <div class="desc">Transforms layer normalizes metrics, computes medians/quartiles, updates Benchmark Library</div>
          </div>
          <div class="flow-step">
            <div class="num">3</div>
            <div class="title">Persist → Bronze/Silver/Gold</div>
            <div class="desc">OLTP stores KPIs, indices, alerts; Data Lake Bronze (raw), Silver (standardized), and Gold (curated/lakehouse)</div>
          </div>
          <div class="flow-step">
            <div class="num">4</div>
            <div class="title">Analyze</div>
            <div class="desc">Digital Mirror & ERA/SEI render KPIs vs sector distributions and trends</div>
          </div>
          <div class="flow-step">
            <div class="num">5</div>
            <div class="title">Ask Joyce</div>
            <div class="desc">RAG retrieves benchmark snippets + tenant context; LLM answers with citations</div>
          </div>
          <div class="flow-step">
            <div class="num">6</div>
            <div class="title">Alert</div>
            <div class="desc">Rules fire on events/cadences; role-based digests sent to in-app/email/Slack</div>
          </div>
        </div>
        <div class="legend">
          <div class="legend-item"><b>Tenancy:</b> All tenant-scoped data carries tenant_id; public benchmarks remain global</div>
          <div class="legend-item"><b>Quality:</b> Contracts, validation, and Fog Score surface confidence to users</div>
          <div class="legend-item"><b>Governance:</b> Data dictionary and provenance recorded for benchmarks</div>
          <div class="legend-item"><b>Scalability:</b> Bronze/Silver/Gold lakehouse with warehouse/query over Gold</div>
        </div>
      </div>

      <!-- Benefits -->
      <div class="section">
        <div class="section-title">Benefits</div>
        <div class="benefits">
          <div class="benefit">
            <div class="title">Data Integrity</div>
            <ul>
              <li>Canonical metric taxonomy and contracts</li>
              <li>Transparent provenance and confidence</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">AI Readiness</div>
            <ul>
              <li>Retrieval-augmented generation (RAG) with citations</li>
              <li>Indices (Joy/Agility) computed and versioned</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">Speed to Value</div>
            <ul>
              <li>Public benchmarks on day one</li>
              <li>Client data elevates relevance over time</li>
            </ul>
          </div>
          <div class="benefit">
            <div class="title">Enterprise Path</div>
            <ul>
              <li>Cloud-agnostic deployment; managed runtime and database</li>
              <li>Security-by-default and auditability</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Examples (Chosen + Options) -->
      <div class="section">
        <div class="section-title">Examples: Chosen + Options</div>
        <div class="legend">
          <div class="legend-item"><b>Chosen UI:</b> React (SPA), TypeScript, Vite, Tailwind; Routing: Wouter</div>
          <div class="legend-item"><b>Chosen API:</b> Node.js + Express (REST)</div>
          <div class="legend-item"><b>Chosen Data:</b> PostgreSQL (operational), Drizzle ORM</div>
          <div class="legend-item"><b>Chosen AI:</b> AI SDK + Anthropic Claude (streaming)</div>
          <div class="legend-item"><b>Retrieval:</b> Vector store (e.g., pgvector); hybrid lexical + vector</div>
          <div class="legend-item"><b>Identity:</b> OIDC IdP (e.g., Auth0/Clerk/Cognito)</div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
