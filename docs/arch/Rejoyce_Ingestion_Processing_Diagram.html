<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Rejoyce Ingestion, Processing & Benchmark Library</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }
    .diagram { padding: 36px; background: #f8fafc; }

    .architecture-grid {
      display: grid;
      grid-template-columns: repeat(6, 1fr);
      gap: 18px;
      margin-bottom: 36px;
    }

    .lane {
      background: white;
      border-radius: 14px;
      padding: 18px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      transition: all 0.25s ease;
      min-height: 360px;
    }
    .lane:hover { transform: translateY(-4px); box-shadow: 0 14px 32px rgba(0,0,0,0.12); }

    .lane-title {
      font-size: 1.05rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 14px;
      padding-bottom: 10px;
      border-bottom: 3px solid;
    }

    .sources-lane { border-color: #3b82f6; }
    .sources-lane .lane-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .orchestration-lane { border-color: #f59e0b; }
    .orchestration-lane .lane-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .validation-lane { border-color: #ef4444; }
    .validation-lane .lane-title { color: #ef4444; border-bottom-color: #ef4444; }

    .modeling-lane { border-color: #10b981; }
    .modeling-lane .lane-title { color: #10b981; border-bottom-color: #10b981; }

    .benchmarks-lane { border-color: #8b5cf6; }
    .benchmarks-lane .lane-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .serving-lane { border-color: #06b6d4; }
    .serving-lane .lane-title { color: #06b6d4; border-bottom-color: #06b6d4; }

    .card {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 12px;
      margin-bottom: 10px;
      border-left: 4px solid;
      transition: all 0.2s ease;
    }
    .card:hover { transform: translateX(4px); }

    .c-blue { border-left-color: #3b82f6; }
    .c-orange { border-left-color: #f59e0b; }
    .c-red { border-left-color: #ef4444; }
    .c-green { border-left-color: #10b981; }
    .c-purple { border-left-color: #8b5cf6; }
    .c-cyan { border-left-color: #06b6d4; }

    .card .name { font-weight: 800; margin-bottom: 6px; display: flex; align-items: center; gap: 8px; }
    .card .desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section {
      background: white;
      border-radius: 14px;
      padding: 24px;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      margin-top: 28px;
    }
    .section-title { text-align: center; font-size: 1.5rem; font-weight: 800; color: #1f2937; margin-bottom: 16px; }

    .flow {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
      gap: 16px;
    }
    .flow-step {
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      color: white;
      border-radius: 12px;
      padding: 16px;
      text-align: center;
    }
    .flow-step .num {
      width: 28px; height: 28px; border-radius: 50%;
      background: rgba(255,255,255,0.25);
      display: flex; align-items: center; justify-content: center;
      margin: 0 auto 8px; font-weight: 800;
    }
    .flow-step .title { font-weight: 700; margin-bottom: 6px; }
    .flow-step .desc { font-size: 0.9rem; opacity: 0.95; }

    .legend {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      margin-top: 18px;
    }
    .legend-item {
      background: #f8fafc; border: 1px dashed #cbd5e1; border-radius: 10px; padding: 12px;
    }
    .legend-item b { color: #111827; }

    @media (max-width: 1280px) { .architecture-grid { grid-template-columns: repeat(3, 1fr); } }
    @media (max-width: 640px)  { .architecture-grid { grid-template-columns: 1fr; } .diagram { padding: 20px; } .header h1 { font-size: 2rem; } }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Ingestion, Processing & Benchmark Library</h1>
      <p>From public/client sources to canonical models, benchmark distributions, and serving</p>
    </div>

    <div class="diagram">
      <div class="architecture-grid">

        <!-- 1. Sources -->
        <div class="lane sources-lane">
          <div class="lane-title">1) Sources</div>
          <div class="card c-blue">
            <div class="name">📄 Public Finance & Market</div>
            <div class="desc">Public filings and curated finance/market datasets</div>
          </div>
          <div class="card c-blue">
            <div class="name">🌍 Macro & Industry</div>
            <div class="desc">Official macroeconomic indicators and industry reports</div>
          </div>
          <div class="card c-blue">
            <div class="name">👥 Talent & ESG</div>
            <div class="desc">Labor statistics, professional network indices, ESG ratings, survey benchmarks</div>
          </div>
          <div class="card c-blue">
            <div class="name">🏢 Client Systems</div>
            <div class="desc">Object storage/SFTP uploads, CRM/ERP/HRIS APIs under contracts and auth</div>
          </div>
        </div>

        <!-- 2. Orchestration -->
        <div class="lane orchestration-lane">
          <div class="lane-title">2) Orchestration</div>
          <div class="card c-orange">
            <div class="name">⏱️ Schedules</div>
            <div class="desc">Daily news/market, weekly aggregates, monthly macro, quarterly earnings</div>
          </div>
          <div class="card c-orange">
            <div class="name">🧭 Executors</div>
            <div class="desc">Scheduler/cron now → workflow engine (state machine/DAG) later</div>
          </div>
          <div class="card c-orange">
            <div class="name">🧪 Smoke & Telemetry</div>
            <div class="desc">job_runs table, row counts, error capture, run logs, SLA alarms</div>
          </div>
        </div>

        <!-- 3. Contracts & Validation -->
        <div class="lane validation-lane">
          <div class="lane-title">3) Contracts & Validation</div>
          <div class="card c-red">
            <div class="name">📐 Data Contracts</div>
            <div class="desc">Zod/JSONSchema for each dataset; unit normalization; canonical mapping</div>
          </div>
          <div class="card c-red">
            <div class="name">🔎 Anomaly Checks</div>
            <div class="desc">Type/range checks, z-score vs prior, null/missing, dedupe strategies</div>
          </div>
          <div class="card c-red">
            <div class="name">🌫️ Fog Score</div>
            <div class="desc">Confidence from freshness, sample size, variance, source credibility</div>
          </div>
        </div>

        <!-- 4. Modeling -->
        <div class="lane modeling-lane">
          <div class="lane-title">4) Modeling</div>
          <div class="card c-green">
            <div class="name">🪣 Data Lake (Bronze/Silver/Gold)</div>
            <div class="desc"><b>Bronze</b> (raw) → <b>Silver</b> (standardized) → <b>Gold</b> (curated, lakehouse)</div>
          </div>
          <div class="card c-green">
            <div class="name">🧱 Transforms</div>
            <div class="desc">Canonical metrics, sector joins, time-series rollups, snapshots</div>
          </div>
          <div class="card c-green">
            <div class="name">🗄️ Operational DB + Views</div>
            <div class="desc">Operational database for app data + materialized views for aggregates</div>
          </div>
          <div class="card c-green">
            <div class="name">🏗️ Warehouse/Query Engine (Later)</div>
            <div class="desc">SQL warehouse or query engine over curated Parquet; data catalog</div>
          </div>
        </div>

        <!-- 5. Benchmark Library -->
        <div class="lane benchmarks-lane">
          <div class="lane-title">5) Benchmark Library</div>
          <div class="card c-purple">
            <div class="name">📚 Distributions</div>
            <div class="desc">Medians, quartiles (p25/p75/p90) per sector/period; size-bucket variants</div>
          </div>
          <div class="card c-purple">
            <div class="name">🧾 Provenance</div>
            <div class="desc">Sources, sample_size, refreshed_at, notes; confidence surfaced in UI</div>
          </div>
          <div class="card c-purple">
            <div class="name">📖 Metrics Catalog</div>
            <div class="desc">Definitions, units, dims, owners; data dictionary powering UI</div>
          </div>
        </div>

        <!-- 6. Serving -->
        <div class="lane serving-lane">
          <div class="lane-title">6) Serving</div>
          <div class="card c-cyan">
            <div class="name">📊 APIs</div>
            <div class="desc">APIs for benchmarks, catalog, KPIs, alerts; tenant-scoped</div>
          </div>
          <div class="card c-cyan">
            <div class="name">🔎 Joyce RAG</div>
            <div class="desc">Chunked benchmark summaries + KPI defs embedded in a vector store for citations</div>
          </div>
          <div class="card c-cyan">
            <div class="name">🔔 Alerting</div>
            <div class="desc">Rule evaluations on cadence + events; in-app/email/Slack digests</div>
          </div>
        </div>

      </div>

      <!-- Flow -->
      <div class="section">
        <div class="section-title">End-to-End Flow</div>
        <div class="flow">
          <div class="flow-step"><div class="num">1</div><div class="title">Ingest → Bronze</div><div class="desc">Fetch/scrape/upload → land in Bronze (raw) with metadata</div></div>
          <div class="flow-step"><div class="num">2</div><div class="title">Validate</div><div class="desc">Contracts + unit normalization + anomaly checks</div></div>
          <div class="flow-step"><div class="num">3</div><div class="title">Standardize → Silver</div><div class="desc">Transforms layer canonicalizes and standardizes; write to Silver (standardized)</div></div>
          <div class="flow-step"><div class="num">4</div><div class="title">Curate → Gold</div><div class="desc">Publish curated datasets to Gold (lakehouse) + update Benchmark Library & metrics catalog</div></div>
          <div class="flow-step"><div class="num">5</div><div class="title">Serve</div><div class="desc">APIs, dashboards, and Joyce RAG consume</div></div>
          <div class="flow-step"><div class="num">6</div><div class="title">Notify</div><div class="desc">Rules fire and digests delivered by role</div></div>
        </div>
        <div class="legend">
          <div class="legend-item"><b>Cadence:</b> Daily/weekly/monthly/quarterly recomputes per dataset SLAs</div>
          <div class="legend-item"><b>Tenancy:</b> Client data carries tenant_id; public benchmarks global</div>
          <div class="legend-item"><b>Governance:</b> Dictionary, provenance, Fog Score surfaced to users</div>
          <div class="legend-item"><b>Scalability:</b> Bronze/Silver/Gold lakehouse with warehouse/query over Gold</div>
        </div>
      </div>
      <!-- Examples (Chosen + Options) -->
      <div class="section">
        <div class="section-title">Examples: Chosen + Options</div>
        <div class="legend">
          <div class="legend-item"><b>Chosen Orchestration:</b> Scheduler/cron now; workflow engine (state machine/DAG) later</div>
          <div class="legend-item"><b>Chosen Retrieval:</b> Vector store using pgvector (or compatible)</div>
          <div class="legend-item"><b>Transforms (options):</b> dbt for canonical models and distributions</div>
          <div class="legend-item"><b>Contracts (options):</b> Zod/JSON Schema for dataset contracts</div>
          <div class="legend-item"><b>APIs (chosen):</b> REST API for benchmarks, catalog, KPIs, alerts</div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
