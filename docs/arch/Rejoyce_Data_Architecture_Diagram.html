<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Rejoyce Data Architecture</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }

    .diagram {
      padding: 36px;
      background: #f8fafc;
    }

    .architecture-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 24px;
      margin-bottom: 36px;
    }

    .layer {
      background: white;
      border-radius: 14px;
      padding: 20px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      transition: all 0.25s ease;
    }
    .layer:hover {
      transform: translateY(-4px);
      box-shadow: 0 14px 32px rgba(0, 0, 0, 0.12);
    }

    .layer-title {
      font-size: 1.2rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 16px;
      padding-bottom: 12px;
      border-bottom: 3px solid;
    }

    .sources-layer { border-color: #3b82f6; }
    .sources-layer .layer-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .ingest-layer { border-color: #f59e0b; }
    .ingest-layer .layer-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .storage-layer { border-color: #10b981; }
    .storage-layer .layer-title { color: #10b981; border-bottom-color: #10b981; }

    .serve-layer { border-color: #8b5cf6; }
    .serve-layer .layer-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .service-box {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 14px;
      margin-bottom: 12px;
      border-left: 4px solid;
      transition: all 0.2s ease;
    }
    .service-box:hover { transform: translateX(4px); }

    .sources-service { border-left-color: #3b82f6; }
    .ingest-service { border-left-color: #f59e0b; }
    .storage-service { border-left-color: #10b981; }
    .serve-service { border-left-color: #8b5cf6; }

    .service-name {
      font-weight: 700;
      font-size: 1.05rem;
      margin-bottom: 6px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .service-desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section {
      background: white;
      border-radius: 14px;
      padding: 24px;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
      margin-top: 28px;
    }
    .section-title {
      text-align: center;
      font-size: 1.5rem;
      font-weight: 800;
      color: #1f2937;
      margin-bottom: 16px;
    }

    .flow {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
    }
    .flow-step {
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      color: white;
      border-radius: 12px;
      padding: 18px;
      text-align: center;
      position: relative;
    }
    .flow-step .num {
      width: 28px; height: 28px; border-radius: 50%;
      background: rgba(255,255,255,0.25);
      display: flex; align-items: center; justify-content: center;
      margin: 0 auto 8px; font-weight: 800;
    }
    .flow-step .title { font-weight: 700; margin-bottom: 6px; }
    .flow-step .desc { font-size: 0.9rem; opacity: 0.95; }

    .legend {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
      gap: 16px;
      margin-top: 18px;
    }
    .legend-item {
      background: #f8fafc;
      border: 1px dashed #cbd5e1;
      border-radius: 10px;
      padding: 12px;
    }
    .legend-item b { color: #111827; }

    @media (max-width: 1024px) {
      .architecture-grid { grid-template-columns: repeat(2, 1fr); }
    }
    @media (max-width: 640px) {
      .architecture-grid { grid-template-columns: 1fr; }
      .diagram { padding: 20px; }
      .header h1 { font-size: 2rem; }
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Rejoyce Data Architecture</h1>
      <p>Canonical models, ingestion zones, benchmark library, and serving patterns</p>
    </div>

    <div class="diagram">
      <div class="architecture-grid">

        <!-- Data Sources -->
        <div class="layer sources-layer">
          <div class="layer-title">Data Sources</div>

          <div class="service-box sources-service">
            <div class="service-name">📄 Public Finance & Market</div>
            <div class="service-desc">Public filings and curated finance/market datasets</div>
          </div>

          <div class="service-box sources-service">
            <div class="service-name">🌍 Macro & Industry</div>
            <div class="service-desc">Official macroeconomic and industry indices</div>
          </div>

          <div class="service-box sources-service">
            <div class="service-name">👥 Talent & ESG</div>
            <div class="service-desc">Labor statistics, professional network indices, ESG ratings, surveys</div>
          </div>

          <div class="service-box sources-service">
            <div class="service-name">🏢 Client Systems</div>
            <div class="service-desc">CRM/ERP/HRIS via object storage/SFTP/API uploads under data contracts</div>
          </div>
        </div>

        <!-- Ingestion & Validation -->
        <div class="layer ingest-layer">
          <div class="layer-title">Ingestion & Validation</div>

          <div class="service-box ingest-service">
            <div class="service-name">🧲 Extractors</div>
            <div class="service-desc">Scheduled workers (cron/EventBridge) pull APIs, scrape filings, accept uploads</div>
          </div>

          <div class="service-box ingest-service">
            <div class="service-name">📐 Contracts & Validation</div>
            <div class="service-desc">Zod/JSONSchema checks, type/unit normalization, anomaly checks</div>
          </div>

          <div class="service-box ingest-service">
            <div class="service-name">🌫️ Fog Score</div>
            <div class="service-desc">Confidence from freshness, sample size, variance, source credibility</div>
          </div>

          <div class="service-box ingest-service">
            <div class="service-name">🧭 Orchestration</div>
            <div class="service-desc">Scheduler/cron now; workflow engine (state machine/DAG) later</div>
          </div>
        </div>

        <!-- Storage & Modeling -->
        <div class="layer storage-layer">
          <div class="layer-title">Storage & Modeling</div>

          <div class="service-box storage-service">
            <div class="service-name">🪣 Data Lake (Bronze/Silver/Gold)</div>
            <div class="service-desc"><b>Bronze</b> (raw) → <b>Silver</b> (standardized) → <b>Gold</b> (curated, lakehouse)</div>
          </div>

          <div class="service-box storage-service">
            <div class="service-name">🗄️ Relational OLTP</div>
            <div class="service-desc">Tenant-scoped product data, KPIs, alerts; optional vector store for embeddings</div>
          </div>

          <div class="service-box storage-service">
            <div class="service-name">🧱 Analytics & Modeling</div>
            <div class="service-desc">Transformation and modeling layer: canonical metrics, distributions, snapshots</div>
          </div>

          <div class="service-box storage-service">
            <div class="service-name">🏗️ Warehouse (Later)</div>
            <div class="service-desc">SQL warehouse or query engine over curated Parquet; data catalog</div>
          </div>
        </div>

        <!-- Serving & Governance -->
        <div class="layer serve-layer">
          <div class="layer-title">Serving & Governance</div>

          <div class="service-box serve-service">
            <div class="service-name">📚 Benchmark Library</div>
            <div class="service-desc">Sector medians, quartiles, size buckets, provenance, confidence</div>
          </div>

          <div class="service-box serve-service">
            <div class="service-name">🗂️ Metrics Catalog</div>
            <div class="service-desc">Definitions, units, dimensions, owners; data dictionary</div>
          </div>

          <div class="service-box serve-service">
            <div class="service-name">🔎 Joyce RAG</div>
            <div class="service-desc">Chunks + embeddings in a vector store; hybrid retrieval for citations</div>
          </div>

          <div class="service-box serve-service">
            <div class="service-name">🔔 Alerts & APIs</div>
            <div class="service-desc">REST v1 for KPIs/benchmarks; alert rules and digests</div>
          </div>
        </div>

      </div>

      <!-- Data Flow -->
      <div class="section">
        <div class="section-title">Data Flow</div>
        <div class="flow">
          <div class="flow-step">
            <div class="num">1</div>
            <div class="title">Ingest → Bronze</div>
            <div class="desc">Pull APIs/scrape/uploads → land artifacts in Bronze (raw) with metadata</div>
          </div>
          <div class="flow-step">
            <div class="num">2</div>
            <div class="title">Validate</div>
            <div class="desc">Contracts enforce schema; normalize units; compute Fog Score (ready for Silver)</div>
          </div>
          <div class="flow-step">
            <div class="num">3</div>
            <div class="title">Standardize → Silver</div>
            <div class="desc">Map to canonical columns; dedupe; write to Silver (standardized)</div>
          </div>
          <div class="flow-step">
            <div class="num">4</div>
            <div class="title">Model & Curate → Gold</div>
            <div class="desc">Transforms build canonical metrics/distributions/snapshots; publish to Gold (curated)</div>
          </div>
          <div class="flow-step">
            <div class="num">5</div>
            <div class="title">Benchmark</div>
            <div class="desc">Compute sector medians/quartiles; size-bucket variants</div>
          </div>
          <div class="flow-step">
            <div class="num">6</div>
            <div class="title">Serve</div>
            <div class="desc">APIs for KPIs/benchmarks; Joyce RAG uses indexed snippets</div>
          </div>
          <div class="flow-step">
            <div class="num">7</div>
            <div class="title">Govern</div>
            <div class="desc">Provenance, lineage, dictionary; freshness and SLA monitoring</div>
          </div>
        </div>
        <div class="legend">
          <div class="legend-item"><b>Tenancy:</b> tenant_id for client data; public benchmarks global with k-anonymity</div>
          <div class="legend-item"><b>Freshness:</b> daily/weekly/monthly/quarterly cadences drive recompute</div>
          <div class="legend-item"><b>Security:</b> encryption at rest/in transit; least-privilege access to buckets/DB</div>
          <div class="legend-item"><b>Scalability:</b> Bronze/Silver/Gold lakehouse with warehouse/query over Gold</div>
        </div>
      </div>

      <!-- Examples (Chosen + Options) -->
      <div class="section">
        <div class="section-title">Examples: Chosen + Options</div>
        <div class="legend">
          <div class="legend-item"><b>Chosen Operational DB:</b> PostgreSQL • <b>Data Access:</b> Drizzle ORM</div>
          <div class="legend-item"><b>Chosen Retrieval Store:</b> Vector store using pgvector (or compatible)</div>
          <div class="legend-item"><b>Object Storage (options):</b> Cloud object storage with raw/staged/curated zones</div>
          <div class="legend-item"><b>Transforms (options):</b> dbt for canonical models and distributions</div>
          <div class="legend-item"><b>Warehouse/Query (options):</b> Serverless SQL warehouse or query engine over Parquet</div>
          <div class="legend-item"><b>Catalog/Lineage (options):</b> Data catalog and lineage capture for governance</div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
