<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title><PERSON><PERSON><PERSON> Agent - RAG & AI Architecture</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%);
      min-height: 100vh;
      padding: 20px;
    }
    .container {
      max-width: 1500px;
      margin: 0 auto;
      background: white;
      border-radius: 20px;
      box-shadow: 0 20px 60px rgba(0,0,0,0.12);
      overflow: hidden;
    }
    .header {
      background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
      color: white;
      padding: 30px;
      text-align: center;
    }
    .header h1 { font-size: 2.4rem; font-weight: 800; margin-bottom: 8px; }
    .header p { font-size: 1.05rem; opacity: 0.9; }

    .diagram { padding: 36px; background: #f8fafc; }
    .grid { display: grid; grid-template-columns: repeat(5, 1fr); gap: 18px; }

    .lane {
      background: white;
      border-radius: 14px;
      padding: 18px;
      border: 2px solid transparent;
      box-shadow: 0 8px 24px rgba(0,0,0,0.08);
      transition: all 0.25s ease;
      min-height: 420px;
    }
    .lane:hover { transform: translateY(-4px); box-shadow: 0 14px 32px rgba(0,0,0,0.12); }

    .lane-title {
      font-size: 1.05rem;
      font-weight: 800;
      text-align: center;
      margin-bottom: 12px;
      padding-bottom: 10px;
      border-bottom: 3px solid;
    }

    .sources { border-color: #3b82f6; }
    .sources .lane-title { color: #3b82f6; border-bottom-color: #3b82f6; }

    .indexing { border-color: #10b981; }
    .indexing .lane-title { color: #10b981; border-bottom-color: #10b981; }

    .retrieval { border-color: #f59e0b; }
    .retrieval .lane-title { color: #f59e0b; border-bottom-color: #f59e0b; }

    .inference { border-color: #8b5cf6; }
    .inference .lane-title { color: #8b5cf6; border-bottom-color: #8b5cf6; }

    .safety { border-color: #ef4444; }
    .safety .lane-title { color: #ef4444; border-bottom-color: #ef4444; }

    .card {
      background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
      border-radius: 10px;
      padding: 12px;
      margin-bottom: 10px;
      border-left: 4px solid;
      transition: all 0.2s ease;
    }
    .card:hover { transform: translateX(4px); }

    .c-blue { border-left-color: #3b82f6; }
    .c-green { border-left-color: #10b981; }
    .c-orange { border-left-color: #f59e0b; }
    .c-purple { border-left-color: #8b5cf6; }
    .c-red { border-left-color: #ef4444; }

    .name { font-weight: 800; margin-bottom: 6px; display: flex; align-items: center; gap: 8px; }
    .desc { font-size: 0.9rem; color: #475569; line-height: 1.35; }

    .section { background: white; border-radius: 14px; padding: 24px; box-shadow: 0 8px 24px rgba(0,0,0,0.08); margin: 24px 0; }
    .section-title { text-align: center; font-size: 1.5rem; font-weight: 800; color: #1f2937; margin-bottom: 16px; }

    .flow { display: grid; grid-template-columns: repeat(auto-fit, minmax(220px, 1fr)); gap: 16px; }
    .flow-step { background: linear-gradient(135deg, #0ea5e9 0%, #7c3aed 100%); color: white; border-radius: 12px; padding: 16px; text-align: center; }
    .flow-step .num { width: 28px; height: 28px; border-radius: 50%; background: rgba(255,255,255,0.25); display: flex; align-items: center; justify-content: center; margin: 0 auto 8px; font-weight: 800; }

    .legend { display: grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap: 16px; margin-top: 18px; }
    .legend-item { background: #f8fafc; border: 1px dashed #cbd5e1; border-radius: 10px; padding: 12px; }
    .legend-item b { color: #111827; }

    @media (max-width: 1280px) { .grid { grid-template-columns: repeat(2, 1fr); } }
    @media (max-width: 640px)  { .grid { grid-template-columns: 1fr; } .diagram { padding: 20px; } .header h1 { font-size: 2rem; } }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Joyce Agent — RAG & AI Architecture</h1>
      <p>From knowledge sources to retrieval, prompt assembly, inference, citations, and safety</p>
    </div>

    <div class="diagram">
      <div class="grid">

        <!-- Knowledge Sources -->
        <div class="lane sources">
          <div class="lane-title">1) Knowledge Sources</div>
          <div class="card c-blue">
            <div class="name">📚 Benchmark Summaries</div>
            <div class="desc">Sector medians/quartiles with provenance, notes, and confidence (from <b>Gold</b> curated datasets)</div>
          </div>
          <div class="card c-blue">
            <div class="name">📖 KPI Definitions</div>
            <div class="desc">Metrics catalog definitions, units, dimensions for canonical KPIs</div>
          </div>
          <div class="card c-blue">
            <div class="name">🏢 Tenant Context</div>
            <div class="desc">Tenant-scoped insights, priorities, KPIs and ERA/SEI excerpts</div>
          </div>
          <div class="card c-blue">
            <div class="name">📄 Source Documents</div>
            <div class="desc">Public filings, industry briefs, macro notes as allowed</div>
          </div>
        </div>

        <!-- Indexing -->
        <div class="lane indexing">
          <div class="lane-title">2) Indexing</div>
          <div class="card c-green">
            <div class="name">🧩 Chunking</div>
            <div class="desc">512–1024 token chunks with metadata: tenant_id, visibility, sector, metric_keys</div>
          </div>
          <div class="card c-green">
            <div class="name">🧠 Embeddings (Vector Store)</div>
            <div class="desc">Generate embeddings for chunks; store vector + text + attributes</div>
          </div>
          <div class="card c-green">
            <div class="name">🧹 Dedup & Versioning</div>
            <div class="desc">Hash-based dedupe, versioned prompts/templates and corpora</div>
          </div>
          <div class="card c-green">
            <div class="name">🔎 Lexical Support</div>
            <div class="desc">Optional lexical ranking to complement vector retrieval (hybrid search)</div>
          </div>
        </div>

        <!-- Retrieval -->
        <div class="lane retrieval">
          <div class="lane-title">3) Retrieval</div>
          <div class="card c-orange">
            <div class="name">🎯 Query Rewrite</div>
            <div class="desc">Clarify user intent, inject tenant/sector context, avoid leakage</div>
          </div>
          <div class="card c-orange">
            <div class="name">🧲 Hybrid Search</div>
            <div class="desc">ANN vector search + lexical ranking; MMR diversity; top‑k selection</div>
          </div>
          <div class="card c-orange">
            <div class="name">📎 Citations</div>
            <div class="desc">Attach source URIs, sectors, metric_keys, sample_size, Fog Score</div>
          </div>
          <div class="card c-orange">
            <div class="name">🔐 Tenancy Filter</div>
            <div class="desc">WHERE tenant_id = ? OR visibility = 'public'; ABAC-ready</div>
          </div>
        </div>

        <!-- Inference -->
        <div class="lane inference">
          <div class="lane-title">4) Inference</div>
          <div class="card c-purple">
            <div class="name">🧱 Prompt Assembly</div>
            <div class="desc">System prompt (joyce-context) + retrieved snippets + KPI values from <b>Gold</b> (curated)</div>
          </div>
          <div class="card c-purple">
            <div class="name">🤖 LLM Provider</div>
            <div class="desc">Hosted LLM with streamed responses to UI</div>
          </div>
          <div class="card c-purple">
            <div class="name">📤 Structured Output</div>
            <div class="desc">JSON blocks for charts, bullets, and recommended actions</div>
          </div>
          <div class="card c-purple">
            <div class="name">📌 Grounded Answers</div>
            <div class="desc">Citations required; refusal if insufficient evidence</div>
          </div>
        </div>

        <!-- Safety & Observability -->
        <div class="lane safety">
          <div class="lane-title">5) Safety & Observability</div>
          <div class="card c-red">
            <div class="name">🧯 Guardrails</div>
            <div class="desc">PII redaction; no cross‑tenant data; policy constraints in prompt</div>
          </div>
          <div class="card c-red">
            <div class="name">🧾 Logging</div>
            <div class="desc">Requests, latency, tokens, retrieval hits, citation coverage</div>
          </div>
          <div class="card c-red">
            <div class="name">🧪 Evaluation</div>
            <div class="desc">Prompt versions, offline tests; A/B prompts later</div>
          </div>
          <div class="card c-red">
            <div class="name">💸 Cost & Rate Limits</div>
            <div class="desc">Per‑tenant/user budgets, rate limiting, caching of retrievals</div>
          </div>
        </div>

      </div>

      <div class="section">
        <div class="section-title">End-to-End Q&A Flow</div>
        <div class="flow">
          <div class="flow-step"><div class="num">1</div><div class="title">Ask</div><div class="desc">User queries Joyce in context of selected company/sector</div></div>
          <div class="flow-step"><div class="num">2</div><div class="title">Retrieve</div><div class="desc">Hybrid search fetches benchmark + tenant snippets</div></div>
          <div class="flow-step"><div class="num">3</div><div class="title">Assemble</div><div class="desc">Prompt built with citations, KPIs, sector info</div></div>
          <div class="flow-step"><div class="num">4</div><div class="title">Answer</div><div class="desc">LLM streams grounded response with sources</div></div>
          <div class="flow-step"><div class="num">5</div><div class="title">Log</div><div class="desc">Usage + quality metrics; redacted safe logs</div></div>
        </div>
        <div class="legend">
          <div class="legend-item"><b>Tenancy:</b> Retrieval restricted to tenant_id or public visibility</div>
          <div class="legend-item"><b>Citations:</b> Answers must include sources; refusal if none</div>
          <div class="legend-item"><b>Embeddings:</b> Vector store with ANN index; chunk metadata preserved</div>
          <div class="legend-item"><b>Data Source:</b> KPI and benchmark values are read from <b>Gold</b> (curated) lakehouse datasets</div>
          <div class="legend-item"><b>Extensibility:</b> Pluggable embedding and LLM providers</div>
        </div>
      </div>
      <div class="section">
        <div class="section-title">Examples: Chosen + Options</div>
        <div class="legend">
          <div class="legend-item"><b>Chosen LLM:</b> Anthropic Claude via AI SDK (streaming responses)</div>
          <div class="legend-item"><b>Chosen Retrieval:</b> Vector store using pgvector (or compatible); hybrid lexical + vector</div>
          <div class="legend-item"><b>Chosen App Stack:</b> React SPA UI; Node.js + Express API (streaming)</div>
          <div class="legend-item"><b>Prompt/Eval (options):</b> Prompt templates with offline evaluation/A-B testing</div>
          <div class="legend-item"><b>Observability (options):</b> Central logs/metrics; token/latency/citation coverage</div>
        </div>
      </div>

    </div>
  </div>
</body>
</html>
