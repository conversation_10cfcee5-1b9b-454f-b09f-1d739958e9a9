# Rejoyce Technical Data and Product Architecture Blueprint
Version: 0.2 (2025-08-30)

Audience: CT<PERSON>, CDAO, Product, Architecture, Data/AI leaders

Purpose: Provide an architecture-only blueprint for how Rejoyce operates as a data- and AI-powered product. This document intentionally avoids implementation details and focuses on logical components, architectural views, operating cadences, and governance. Example tools are mentioned only as non-binding illustrations.

Diagram Index (open in browser):
- High-level System Overview: ./arch/Rejoyce_System_Overview_Diagram.html
- Data Architecture & Storage Layers: ./arch/Rejoyce_Data_Architecture_Diagram.html
- Ingestion, Processing & Benchmark Library: ./arch/Rejoyce_Ingestion_Processing_Diagram.html
- Joyce Agent RAG & AI Architecture: ./arch/Rejoyce_Joyce_RAG_Architecture_Diagram.html
- Cloud Reference Architecture (Vendor-Agnostic): ./arch/Rejoyce_Cloud_Reference_Vendor_Agnostic.html

---------------------------------------------------------------------

1) Context and Product Capabilities (What Rejoyce Delivers)

- Digital Mirror
  - Role-aware dashboards for executives and operators
  - KPI tiles, trends, distributions, and sector context

- Joyce Agent
  - Conversational analysis grounded in tenant context + benchmark library
  - Answers with citations and confidence indicators

- Benchmark Library
  - Sector distributions (medians, quartiles, ranges), size buckets
  - Provenance, sample size, and confidence (“Fog Score”)

- Alerting & Cadences
  - Event-driven and scheduled updates (daily/weekly/monthly/quarterly/annual)
  - Role-based digests and in-app notifications

---------------------------------------------------------------------

2) Logical Domain Model (Key Entities)

- Company Profile: symbol, name, sector, size, geography
- Metrics: financial, customer/market, operational, innovation, macro, competitive, talent/ESG
- Benchmark Distributions: per sector/period; medians/quartiles; size buckets
- Knowledge Corpus: documents/snippets used to ground Joyce (definitions, summaries, notes)
- Alerts & Rules: rule definitions, evaluations, notifications, acknowledgements
- Governance: data sources, data contracts, lineage, data dictionary, Fog Score
- Access & Tenancy: tenants, user roles, entitlements; tenant-scoped data isolation

---------------------------------------------------------------------

3) End-to-End Data Flow (Operating Model)

- Ingest
  - Public filings and curated datasets; client uploads and connector feeds

- Validate
  - Data contracts; unit/definition normalization; anomaly checks
  - Confidence assessment (Fog Score) based on freshness, sample size, variability, provenance

- Store
  - Operational data store for product entities and metrics
  - Data Lake: Bronze (raw) → Silver (standardized) → Gold (curated, lakehouse) for artifacts, time travel, and published datasets

- Transform
  - ETL/ELT: canonical metrics; sector joins; distribution calculations; snapshots progressing data from Bronze → Silver → Gold

- Publish
  - Benchmark Library and Metrics Catalog are refreshed and versioned for reference

- Serve
  - APIs for KPIs/benchmarks/alerts; dashboards and Joyce Agent consume

- Govern
  - Lineage capture; dictionary maintenance; freshness SLAs; auditability

---------------------------------------------------------------------

4) Platform Architecture (Cloud-Agnostic View)

- Edge & Identity
  - DNS/CDN/TLS/WAF; OIDC identity provider; role/attribute-based access

- Compute & Ingress
  - Static hosting for SPA; API runtime for product and background workers
  - API gateway/ingress with health checks and gradual rollout

- Data Services
  - Operational relational database (tenant-scoped)
  - Data Lake (Bronze/Raw, Silver/Standardized, Gold/Curated) with Lakehouse pattern
  - Analytical warehouse or query engine over Gold (curated) data

- Orchestration
  - Scheduler/cron for cadences; workflow engine (state machine/DAG) for multi-step jobs

- Observability & Security
  - Central logs/metrics; SLO alerts; error monitoring
  - Secrets management; KMS-backed encryption; least-privilege access

---------------------------------------------------------------------

5) Multitenancy and Access Control (Isolation)

- Tenant Isolation
  - Tenant-scoped data domain; public datasets remain global
  - Tenant resolution from identity context; enforced at service/data layers

- Access Control
  - Simple roles (e.g., Admin/User) at minimum
  - Attribute-based extensions for function/region/data domain as needed

---------------------------------------------------------------------

6) Joyce Agent (AI Architecture)

- Sources
  - Benchmark summaries, KPI definitions, sector notes, tenant context

- Indexing
  - Chunking of relevant text; embeddings with metadata (tenant/visibility/sector)

- Retrieval
  - Hybrid search (lexical + vector) with diversity; top‑k with citations
  - Tenant-aware filtering; public visibility only where appropriate

- Inference
  - Prompt assembly with retrieved snippets and data inserts
  - Grounded answers with citations; refusal when evidence is insufficient

- Safety & Oversight
  - Redaction of sensitive information in logs
  - Evaluation harness for prompt quality and correctness (maturity path)

---------------------------------------------------------------------

7) Benchmark Library (Design Principles)

- Distributions
  - Per sector/period; medians, quartiles, p90/top/bottom ranges
  - Optional size buckets for improved comparability (e.g., small/mid/large)

- Provenance & Confidence
  - Source registry and sample sizes; notes; refresh dates
  - Fog Score to signal confidence based on freshness, sample size, variability, credibility

- Versioning
  - As-of versions; snapshots for regressions and longitudinal analysis

---------------------------------------------------------------------

8) Operating Cadences (Refresh Rhythm)

- Daily: news, market signals, macro rates; urgent alerts
- Weekly: aggregate changes, market shifts, analyst movements
- Monthly: macro and industry indices; rolling metrics
- Quarterly: earnings cycle; benchmark rebuild; ERA/SEI refresh
- Annual: strategy recalibration; metric catalog review; new sources

---------------------------------------------------------------------

9) Governance, Privacy, and Risk

- Data Dictionary
  - Standardized definitions, units, and dimensions; visible in product UI

- Lineage & Audit
  - Source → staged → curated → published paths; auditable transformations

- Privacy & Anonymization
  - Contracts governing client data contribution to benchmarks
  - Aggregation thresholds (e.g., suppress if sample too small)

- Risk Controls
  - Access reviews; least-privilege enforcement; monitoring of cost and quotas

---------------------------------------------------------------------

10) Maturity Path (Architecture-Only View)

- Phase A: Foundation
  - Single-tenant-aware operational store; object storage zones
  - Ingestion on cadences; first distributions; Joyce grounded with citations

- Phase B: Scale
  - More domains/metrics; broader sector coverage; enriched governance views
  - Optimization of storage/query layers and workflow orchestration

- Phase C: Moat
  - Anonymized client contributions mature benchmark relevance
  - Predictive indicators; stronger evaluation of Joyce and alert precision

---------------------------------------------------------------------

Selected Baseline Technologies (Chosen)
- Frontend UI: React (SPA), TypeScript, Vite, Tailwind CSS
- Routing: Wouter
- Backend API: Node.js with Express (REST), TypeScript
- Data access: Drizzle ORM
- Operational database: PostgreSQL
- AI integration: AI SDK with Anthropic Claude (streaming responses)
- Retrieval: Vector store using pgvector (or compatible)

Appendix: Possible Tools (Examples Only — Non-Binding)

- Identity & Access:
  - Any OIDC/OAuth provider; role/attribute-based access control

- Operational Data Store:
  - Relational database with tenant-isolation patterns

- Data Lake & Warehouse:
  - Cloud data lake (Bronze/Silver/Gold) and SQL warehouse or query engine over Gold (Parquet)

- Orchestration:
  - Scheduler/cron; workflow/state machine or DAG engine

- Retrieval & AI:
  - Vector store; embedding model provider; LLM provider
  - Prompt/evaluation framework for grounded responses and quality checks

- Observability & Security:
  - Centralized logging/metrics; error monitoring
  - Secrets manager; KMS-backed encryption; least-privilege IAM/permissions

This blueprint intentionally omits implementation specifics to remain architecture-only. The diagrams in ./arch provide aligned visual abstractions across product, data, AI, and cloud-neutral platform layers.

Appendix: PDF Alignment to Data Management & Benchmarking Strategy

- Communication Artifacts & Cadences
  - Weekly Digest
    - Purpose: summarize notable market/news signals, short-term metric changes, sector highlights.
    - Channel: email and in-app digest; role-filtered subscriptions.
    - Source of truth: weekly aggregation job materializes a curated “insights_weekly” dataset in Gold.
  - Monthly Narrative Report
    - Purpose: narrative interpretation of monthly macro/industry indicators and any new benchmarks.
    - Channel: in-app report + downloadable PDF; optional client call.
    - Source of truth: curated “macro_monthly”, “industry_index_monthly”, and rolling KPI tables in Gold.
  - Quarterly Digital Mirror (Business Review)
    - Purpose: cornerstone refresh aligned to earnings cycle; full benchmark rebuild, KPI rebase, proprietary indices (Joy Score, SVI).
    - Channel: in-app dashboard update and executive deck; briefing offered.
    - Source of truth: quarterly rebuild of benchmark distributions and snapshots in Gold; versioned as-of datasets.
  - Annual Benchmarking & Strategy Review
    - Purpose: long-horizon perspective, metric catalog recalibration, sector outlooks.
    - Channel: executive presentation + updated planning artifacts.
    - Source of truth: annual benchmark snapshots, 5-year trend views from Gold; catalog changes approved by Council.
  - Ad-hoc Thought Leadership
    - Purpose: insights from public benchmark analyses or topical events.
    - Channel: whitepapers/notes distributed via app and email; citation-ready with provenance from Gold.

- Internal Alerting & Triage Flow
  1) Signal capture: market/macro feeds, filings, M&A, sector indices, client metric deltas land to Bronze.
  2) Rules evaluation: normalization/validation in Silver; rule engine evaluates trigger conditions.
  3) Internal dashboard flag: alert object created with severity, impacted entities, and Fog Score; owner auto-assigned by domain.
  4) Analyst triage: verify data quality, add short commentary, decide disposition.
  5) Disposition:
     - Hold and include in Weekly/Monthly digest; or
     - Immediate client alert for high-impact events (per severity matrix).
  6) External communication: role-filtered, tenant-scoped; includes short “Actionable Insight” and citations.
  7) Post-event tagging: feedback loop updates rule thresholds, source weights, and playbooks; lineage recorded.

- Role-Based Customization Map
  - CEO/C-suite: strategic indicators, major financial/industry shifts, quarterly/annual perspectives; immediate alerts on disruptive events.
  - CFO/Finance: financial KPIs, valuation/comps, cost of capital changes, leverage/coverage risk; monthly dashboard + alerts on material shifts.
  - COO/Operations: operational benchmarks, supply chain/PMI, process stability; weekly/monthly trend views + risk deterioration alerts.
  - CHRO/People: turnover/engagement, labor availability, compensation benchmarks; annual/quarterly surveys with interim trends.
  - CMO/Customer: churn/retention/NPS, market share/sentiment; quarterly benchmarks with notable interim updates.
  - All roles access the unified in-app news & data feed with role-based filters.

- KPI Catalog Stewardship (IP & Data Council)
  - Council: cross-functional leads (Product, Data, Domain) govern KPI definitions, dimensions, sources, and change control.
  - Cadence: quarterly review; annual strategic refresh; emergency addendum for material market changes.
  - Artifacts: data dictionary, data contracts, transformation/lineage specs, source registry with confidence ratings.
  - Privacy guardrails: anonymization thresholds (e.g., suppress small-N buckets), tenant consent for contribution to benchmarks.
  - Versioning: controlled evolution of KPI schemas and benchmark methodologies; backward-compatible transitions where feasible.

- Public Data Benchmark Initiative (Short-Term) Sequence
  1) Identify metrics & sectors: ERA baseline KPIs plus lens-specific metrics per target industry.
  2) Select representative public peers: anchor companies and indices per sector/size.
  3) Gather history: filings, investor materials, industry datasets, macro feeds; land raw to Bronze with provenance.
  4) Normalize & transform: unit/definition alignment, ratio/percent calculations, size buckets in Silver.
  5) Build benchmark library: compute medians/quartiles/ranges and distributions; publish to Gold with sample size, source, freshness.
  6) Early analysis: correlations, outliers, best-in-class exemplars; produce insights with citations and Fog Scores.
  7) Functional sources: incorporate NPS/CSAT, engagement, PMI, etc. to enrich non-financial views.
  8) Validate & iterate: pilot comparisons, fill gaps with proxies, document assumptions; refine quarterly.

- Client Communications Calendar (Per-Client Template)
  - Weekly: Friday digest (opt-in), role-filtered.
  - Monthly: 1st week narrative report; optional 30-minute readout.
  - Quarterly: 2 weeks post quarter-end, Digital Mirror update and executive briefing.
  - Annual: strategy-aligned benchmarking review and catalog recalibration.
  - High-impact: immediate alert with short analysis and recommended next steps.

- Lakehouse Alignment Notes
  - KPI and benchmark values are read from Gold (curated) datasets; Joyce Agent also consumes curated knowledge derived from Gold.
  - Bronze: raw ingestion with immutable storage and provenance; Silver: standardized, validated, rule-ready; Gold: curated, versioned, query-optimized (warehouse/query over Gold).
  - Fog Score accompanies datasets to signal confidence (freshness, sample size, variability, credibility).
  - Anonymization and aggregation thresholds enforced before any client-contributed data enters benchmark Gold.
  - Freshness SLAs map to cadences (daily/weekly/monthly/quarterly/annual) and drive alert severity.
  - Full lineage from source → Bronze → Silver → Gold → published artifacts is captured for auditability.
