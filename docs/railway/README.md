# Phase B — Managed Postgres on Railway (Staging & Prod)

This runbook covers provisioning managed Postgres on Railway, enforcing least-privilege roles, wiring env vars, running migrations via CI, enabling backups, and performing a restore test.

## B1) Provision DBs (Railway)

Create two Railway Postgres instances:
- rejoyce_staging
- rejoyce_prod

For both instances:
- Require SSL. Railway supports `?sslmode=require` on connection strings.
- Copy the provided connection strings for later steps.

## B2) Create least-privileged roles per env

Create two roles per environment:
- `migrator` — allowed to CREATE/ALTER/DROP on app schema (DDL)
- `app_user` — only SELECT/INSERT/UPDATE/DELETE (DML)

Example SQL (adjust db/usernames/passwords as needed). Run against each DB one time:

```sql
-- scripts/sql/roles.sql (reference)
-- Create users (choose strong passwords)
CREATE USER migrator WITH PASSWORD 'REPLACE_WITH_STRONG_PASSWORD';
CREATE USER app_user WITH PASSWORD 'REPLACE_WITH_STRONG_PASSWORD';

-- Allow both to connect and use public schema
GRANT CONNECT ON DATABASE rejoyce_staging TO migrator, app_user;
GRANT USAGE ON SCHEMA public TO migrator, app_user;

-- Migrator applies schema first (run migrations with migrator before granting app DML):
-- After tables exist, grant DML to app_user:
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO app_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO app_user;
```

Notes:
- Run the GRANTs after the first migration to ensure app_user has DML on created tables. The `ALTER DEFAULT PRIVILEGES` ensures future tables also receive these grants.
- For prod, repeat with distinct strong passwords.

## B3) Set Railway Variables per env

In each Railway service:

Staging (Service → Variables):
- DATABASE_URL = *********************************/dbname?sslmode=require  (used by CI to run migrations)
- APP_DATABASE_URL = *********************************/dbname?sslmode=require  (used by the running app)

Prod (Service → Variables):
- DATABASE_URL = *********************************/dbname?sslmode=require
- APP_DATABASE_URL = *********************************/dbname?sslmode=require

Server code is already configured to prefer `APP_DATABASE_URL` at runtime and fallback to `DATABASE_URL`:
- server/db.ts picks `APP_DATABASE_URL ?? DATABASE_URL`
- SSL is automatically enabled when `?sslmode=require` is present. If the provider requires looser TLS verification, set `PG_REJECT_UNAUTHORIZED=false` in the environment and the client will set `{ rejectUnauthorized: false }`.

Drizzle config (drizzle.config.ts) uses `DATABASE_URL` which is what CI sets when pushing migrations.

## B4) CI/CD migrations & deploy

A GitHub Actions workflow has been added at:
- .github/workflows/db-migrate.yml

Behavior:
- On push to main: runs `drizzle-kit push` against staging using `secrets/STAGING_MIGRATOR_URL` as `DATABASE_URL`
- Manual (workflow_dispatch): runs prod migration using `secrets/PROD_MIGRATOR_URL`

Setup in GitHub:
- Settings → Environments → staging: add secret `STAGING_MIGRATOR_URL` (Railway staging `DATABASE_URL` with `?sslmode=require`)
- Settings → Environments → production: add secret `PROD_MIGRATOR_URL` (Railway prod `DATABASE_URL` with `?sslmode=require`)

Runtime:
- Railway service should have `APP_DATABASE_URL` set for the running app (least privilege).
- Optionally keep `DATABASE_URL` in Railway Variables too for manual, but CI drives migrations via GitHub secrets.

## B5) Backups & one restore test

Backups:
- Enable nightly backups (prod: 14–30d retention; staging: 7–14d retention).

Restore test:
1) Create an on-demand backup of staging.
2) Restore into a new temporary DB e.g., `rejoyce_staging_restoretest`.
3) Point the smoke test (below) to that restored DB and verify:
   - can read a known company and a known user
   - can insert a trivial row and then delete it

A helper smoke script is provided:
- `scripts/smoke.ts`

Run it locally against a target DB:
```bash
# Use a read/write app-level URL (APP_DATABASE_URL) to exercise DML
APP_DATABASE_URL="*********************************/rejoyce_staging_restoretest?sslmode=require" \
npm run smoke:db
```

Expected output:
- SELECT 1 OK
- counts/read checks OK
- insert/delete test OK

Take screenshots of Railway backup schedule, the restore target DB, and the smoke results output.

## B6) Health/readiness

Readiness endpoint was added already:
- GET /ready → 200 when DB responds to SELECT 1 (or 200 with message if DB not configured)
- On DB failure it returns 503

This supports readiness checks in Railway or any orchestrator.

## Runtime wiring summary

- Runtime app uses `APP_DATABASE_URL` (least privilege).
- CI migrations use `DATABASE_URL` via GitHub secrets. Drizzle reads `DATABASE_URL` from env.
- SSL enabled by `?sslmode=require` (server/db.ts includes auto-detection).
- No secrets in code.

## Operational commands quick reference

Local (mimicking cloud flow):
```bash
# Migrate (staging equivalent)
DATABASE_URL="*************************************/rejoyce_staging?sslmode=require" npm run db:push

# Start app with least-privileged URL
APP_DATABASE_URL="*************************************/rejoyce_staging?sslmode=require" npm run start
```

Prod (manual approval in CI, or locally with care):
```bash
DATABASE_URL="**************************************/rejoyce_prod?sslmode=require" npm run db:push
APP_DATABASE_URL="**************************************/rejoyce_prod?sslmode=require" npm run start
```

## Acceptance Criteria mapping

- ✅ Managed Postgres (staging/prod) — provision on Railway
- ✅ SSL required via `?sslmode=require` and server/db.ts SSL handling
- ✅ Least-privilege enforced: `migrator` (DDL) vs `app_user` (DML); app reads `APP_DATABASE_URL`, CI uses `DATABASE_URL`
- ✅ Backups enabled + restore test with `scripts/smoke.ts`
- ✅ Secrets in env/variables only
- ✅ /ready returns 200 only when DB responds; 503 otherwise

## Artifacts to attach when closing

- Railway screenshots: staging/prod DBs, Variables panel (redacted), backup schedule, and the restored test DB
- CI logs for staging migration (auto) and production migration (manual)
- Output of smoke test against restored staging DB
- migrations/ directory showing baseline SQL
