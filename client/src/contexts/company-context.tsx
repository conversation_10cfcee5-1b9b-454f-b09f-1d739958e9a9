import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface CompanyContextType {
  selectedCompany: string;
  setSelectedCompany: (company: string) => void;
  availableCompanies: string[];
  setAvailableCompanies: (companies: string[]) => void;
  companyDisplayName: string;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

interface CompanyProviderProps {
  children: ReactNode;
}

export function CompanyProvider({ children }: CompanyProviderProps) {
  const [selectedCompany, setSelectedCompany] = useState<string>('vz');
  const [availableCompanies, setAvailableCompanies] = useState<string[]>(['acn', 'cvs', 'ma', 'vz']);

  // Convert company code to display name
  const getCompanyDisplayName = (company: string) => {
    const companyMap: { [key: string]: string } = {
      'acn': 'Accenture',
      'cvs': 'CVS Health',
      'ma': 'Mastercard',
      'vz': 'Verizon',
      'aapl': 'AAPL',
      'msft': 'MSFT',
      'googl': 'GOOGL',
      'amzn': 'AMZN',
      'tsla': 'TSLA',
      'nvda': 'NVDA',
      'meta': 'META'
    };
    return companyMap[company.toLowerCase()] || company.toUpperCase();
  };

  const companyDisplayName = getCompanyDisplayName(selectedCompany);

  const value = {
    selectedCompany,
    setSelectedCompany,
    availableCompanies,
    setAvailableCompanies,
    companyDisplayName
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
}

export function useCompany() {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
}