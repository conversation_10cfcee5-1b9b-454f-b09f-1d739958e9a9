import { useState, useEffect } from 'react';
import { useCompany } from '@/contexts/company-context';

export interface Alert {
  id: string;
  title: string;
  description: string;
  severity: 'critical' | 'high' | 'medium' | 'low';
  type: 'era' | 'sei';
  category: string;
  source: string;
  sourceUrl?: string;
  timestamp: string;
  isRead: boolean;
  deepLink: string;
  [key: string]: any; // Allow for additional alert-specific data
}

export interface AlertsData {
  company: string;
  lastUpdated: string;
  totalUnreadCount: number;
  alerts: Alert[];
}

export function useAlerts() {
  const [alertsData, setAlertsData] = useState<AlertsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { selectedCompany } = useCompany();

  // Get read alert IDs from localStorage
  const getReadAlertIds = (company: string): Set<string> => {
    try {
      const stored = localStorage.getItem(`readAlerts_${company}`);
      return new Set(stored ? JSON.parse(stored) : []);
    } catch {
      return new Set();
    }
  };

  // Save read alert IDs to localStorage
  const saveReadAlertIds = (company: string, readIds: Set<string>) => {
    try {
      localStorage.setItem(`readAlerts_${company}`, JSON.stringify(Array.from(readIds)));
    } catch (err) {
      console.warn('Failed to save read alert state:', err);
    }
  };

  useEffect(() => {
    const fetchAlerts = async () => {
      if (!selectedCompany) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const response = await fetch(`/data/companies/${selectedCompany}/alerts.json`);
        if (!response.ok) {
          throw new Error(`Failed to fetch alerts: ${response.statusText}`);
        }
        
        const data: AlertsData = await response.json();
        
        // Apply persisted read state from localStorage
        const readAlertIds = getReadAlertIds(selectedCompany);
        const alertsWithPersistedState = data.alerts.map(alert => ({
          ...alert,
          isRead: alert.isRead || readAlertIds.has(alert.id)
        }));
        
        // Recalculate unread count based on current state
        const unreadCount = alertsWithPersistedState.filter(alert => !alert.isRead).length;
        
        const finalData = {
          ...data,
          alerts: alertsWithPersistedState,
          totalUnreadCount: unreadCount
        };
        
        setAlertsData(finalData);
      } catch (err) {
        console.error('Error fetching alerts:', err);
        setError(err instanceof Error ? err.message : 'Failed to load alerts');
        // Set empty alerts data as fallback
        setAlertsData({
          company: selectedCompany,
          lastUpdated: new Date().toISOString(),
          totalUnreadCount: 0,
          alerts: []
        });
      } finally {
        setLoading(false);
      }
    };

    fetchAlerts();
  }, [selectedCompany]);

  const markAsRead = (alertId: string) => {
    if (!alertsData || !selectedCompany) return;
    
    // Update the read alert IDs in localStorage
    const readAlertIds = getReadAlertIds(selectedCompany);
    readAlertIds.add(alertId);
    saveReadAlertIds(selectedCompany, readAlertIds);
    
    const updatedAlerts = alertsData.alerts.map(alert => 
      alert.id === alertId ? { ...alert, isRead: true } : alert
    );
    
    const unreadCount = updatedAlerts.filter(alert => !alert.isRead).length;
    
    const updatedData = {
      ...alertsData,
      alerts: updatedAlerts,
      totalUnreadCount: unreadCount
    };
    
    setAlertsData(updatedData);
  };

  const markAllAsRead = () => {
    if (!alertsData || !selectedCompany) return;
    
    // Update all alert IDs as read in localStorage
    const allAlertIds = new Set(alertsData.alerts.map(alert => alert.id));
    saveReadAlertIds(selectedCompany, allAlertIds);
    
    const updatedAlerts = alertsData.alerts.map(alert => ({ ...alert, isRead: true }));
    
    const updatedData = {
      ...alertsData,
      alerts: updatedAlerts,
      totalUnreadCount: 0
    };
    
    setAlertsData(updatedData);
  };

  const getUnreadAlerts = () => {
    return alertsData?.alerts.filter(alert => !alert.isRead) || [];
  };

  const getAlertsBySeverity = (severity: Alert['severity']) => {
    return alertsData?.alerts.filter(alert => alert.severity === severity) || [];
  };

  return {
    alertsData,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    getUnreadAlerts,
    getAlertsBySeverity,
    unreadCount: alertsData?.totalUnreadCount || 0
  };
}