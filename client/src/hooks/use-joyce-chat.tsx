import { useState, useEffect } from 'react';
import { useChat } from 'ai/react';
import { useCompany } from '@/contexts/company-context';
import type { Message } from 'ai';

export interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
}

const STORAGE_KEY = 'joyce-chat-messages';

export function useJoyceChat() {
  const [isThinking, setIsThinking] = useState(false);
  const { selectedCompany } = useCompany();
  
  // Load persisted messages from localStorage
  const getStoredMessages = (): Message[] => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.warn('Failed to load stored messages:', error);
    }
    
    // Default initial message
    return [
      {
        id: '1',
        role: 'assistant',
        content: "Hello! I'm <PERSON>, your Strategic Intelligence Assistant. How can I help you analyze the data you're viewing?",
      }
    ];
  };
  
  // Use the AI SDK's useChat hook for proper streaming support
  const { messages: aiMessages, input, handleInputChange, handleSubmit, isLoading, append } = useChat({
    api: '/api/chat',
    body: {
      companySymbol: selectedCompany,
    },
    initialMessages: getStoredMessages(),
    onResponse: () => {
      // Clear thinking state as soon as response starts
      setIsThinking(false);
    },
    onFinish: () => {
      setIsThinking(false);
    }
  });

  // Persist messages to localStorage whenever they change
  useEffect(() => {
    if (aiMessages.length > 0) {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(aiMessages));
    }
  }, [aiMessages]);

  // Convert AI SDK messages to our ChatMessage format
  const messages: ChatMessage[] = aiMessages.map(msg => ({
    id: msg.id,
    type: msg.role === 'user' ? 'user' : 'ai',
    content: msg.content,
    timestamp: new Date(), // AI SDK doesn't provide timestamps, so we use current time
  }));

  // Custom sendMessage function that works with our interface
  const sendMessage = async (messageContent: string, customCompanySymbol?: string) => {
    setIsThinking(true);
    // Use the append function to add a user message and trigger AI response
    await append({
      role: 'user',
      content: messageContent,
    });
  };

  // Enhanced handleSubmit that triggers thinking state
  const enhancedHandleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (input.trim()) {
      setIsThinking(true);
      handleSubmit(e);
    }
  };

  // Clear chat history function
  const clearHistory = () => {
    localStorage.removeItem(STORAGE_KEY);
    window.location.reload(); // Reload to reset the useChat hook
  };

  return {
    messages,
    sendMessage,
    isLoading: isLoading || isThinking,
    isThinking,
    input,
    handleInputChange,
    handleSubmit: enhancedHandleSubmit,
    clearHistory,
  };
}