import { createContext, useCallback, useContext, useEffect, useState } from "react";
import type { User } from "@shared/schema";

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  login: () => void;
  logout: () => void;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

async function fetchMe(): Promise<User | null> {
  try {
    const res = await fetch("/api/auth/me", {
      method: "GET",
      credentials: "include",
    });
    if (res.status === 401) return null;
    if (!res.ok) return null;

    const data: {
      sub?: string;
      email?: string;
      role?: string;
      ["https://rejoyce.ai/role"]?: string;
      ["https://rejoyce.ai/company_id"]?: string;
    } = await res.json();

    const email = data.email ?? "";
    const username = email ? email.split("@")[0] : "user";
    const role =
      (data["https://rejoyce.ai/role"] as string | undefined) ??
      data.role ??
      "analyst";

    const user: User = {
      id: 1, // backend session-based auth doesn't expose numeric ID; using sentinel
      username,
      password: "", // unused in OIDC flow
      email,
      role,
      createdAt: new Date(),
    };
    return user;
  } catch {
    return null;
  }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshUser = useCallback(async () => {
    setIsLoading(true);
    const u = await fetchMe();
    setUser(u);
    setIsLoading(false);
  }, []);

  useEffect(() => {
    // Initial load
    refreshUser();

    // Refresh on window focus to keep session fresh
    const onFocus = () => {
      void refreshUser();
    };
    window.addEventListener("focus", onFocus);
    return () => {
      window.removeEventListener("focus", onFocus);
    };
  }, [refreshUser]);

  const login = () => {
    window.location.href = "/api/auth/login";
  };

  const logout = () => {
    window.location.href = "/api/auth/logout";
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        login,
        logout,
        refreshUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const ctx = useContext(AuthContext);
  if (!ctx) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return ctx;
}
