import { useState, useEffect } from 'react';

export interface ERAData {
  companySymbol: string;
  companyName: string;
  readinessSnapshot: {
    joyScore: {
      value: number;
      maxValue: number;
      level: string;
      description: string;
      status: string;
      industryAverage: number;
      industryLeaders: number;
    };
    agilityLevel: {
      value: string;
      score: number;
      description: string;
      status: string;
      responseTime: string;
      benchmarkComparison: string;
    };
    overallReadiness: {
      value: string;
      description: string;
      status: string;
    };
  };
  valueLeakageOverview: {
    totalLeakage: {
      value: string;
      percentage: string;
      description: string;
      status: string;
    };
  };
}

export interface ValueLeakageData {
  companySymbol: string;
  companyName: string;
  valueLeakageOverview: {
    totalLeakage: {
      amount: string;
      percentage: string;
      description: string;
    };
    recoverablePortion: {
      amount: string;
      percentage: string;
      timeframe: string;
    };
  };
}

export interface CompanyContextData {
  companySymbol: string;
  companyName: string;
  companyProfile: {
    overview: {
      marketCap: string;
      revenue2024: string;
    };
  };
  competitorAnalysis: {
    competitiveMetrics: Array<{
      metric: string;
      cvs?: string;
      [key: string]: any;
    }>;
  };
}

export function useERAData(companySymbol: string) {
  const [executiveDashboard, setExecutiveDashboard] = useState<ERAData | null>(null);
  const [valueLeakage, setValueLeakage] = useState<ValueLeakageData | null>(null);
  const [companyContext, setCompanyContext] = useState<CompanyContextData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadERAData = async () => {
      if (!companySymbol) return;
      
      try {
        setLoading(true);
        setError(null);
        
        const [dashboardRes, leakageRes, contextRes] = await Promise.all([
          fetch(`/data/companies/${companySymbol.toLowerCase()}/era-report/executive-summary-dashboard.json`),
          fetch(`/data/companies/${companySymbol.toLowerCase()}/era-report/value-leakage-financial-impact.json`),
          fetch(`/data/companies/${companySymbol.toLowerCase()}/era-report/company-context-benchmark-explorer.json`)
        ]);

        if (dashboardRes.ok) {
          const dashboardData = await dashboardRes.json();
          setExecutiveDashboard(dashboardData);
        }

        if (leakageRes.ok) {
          const leakageData = await leakageRes.json();
          setValueLeakage(leakageData);
        }

        if (contextRes.ok) {
          const contextData = await contextRes.json();
          setCompanyContext(contextData);
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load ERA data');
        console.error('Error loading ERA data:', err);
      } finally {
        setLoading(false);
      }
    };

    loadERAData();
  }, [companySymbol]);

  return { 
    executiveDashboard, 
    valueLeakage, 
    companyContext,
    loading, 
    error 
  };
}