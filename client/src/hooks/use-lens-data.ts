import { useMemo } from 'react';
import { useCompanyData } from './use-company-data';
import { applyStrategicLens, generateLensSpecificInsights } from '@/lib/strategic-lens-config';

type LayerType = 'enterprise' | 'productService' | 'customer';

interface UseLensDataOptions {
  company: string;
  dataType: string;
  selectedLens: string;
  layer: LayerType;
}

export function useLensData({ company, dataType, selectedLens, layer }: UseLensDataOptions) {
  const { data: rawData, loading, error } = useCompanyData(company, dataType);

  const transformedData = useMemo(() => {
    if (!rawData || !selectedLens) return rawData;
    
    // Apply strategic lens transformation
    const lensAwareData = applyStrategicLens(rawData, selectedLens, layer);
    
    // Add lens-specific insights
    lensAwareData.lensSpecificInsights = generateLensSpecificInsights(lensAwareData, selectedLens);
    
    return lensAwareData;
  }, [rawData, selectedLens, layer]);

  return {
    data: transformedData,
    rawData,
    loading,
    error,
    isLensApplied: !!selectedLens && selectedLens !== 'default'
  };
}

// Convenience hooks for specific layers
export function useEnterpriseLensData(company: string, selectedLens: string) {
  return useLensData({
    company,
    dataType: 'enterprise-layer',
    selectedLens,
    layer: 'enterprise'
  });
}

export function useProductServiceLensData(company: string, selectedLens: string) {
  return useLensData({
    company,
    dataType: 'products-services',
    selectedLens,
    layer: 'productService'
  });
}

export function useCustomerLensData(company: string, selectedLens: string) {
  return useLensData({
    company,
    dataType: 'customer-engagement',
    selectedLens,
    layer: 'customer'
  });
}