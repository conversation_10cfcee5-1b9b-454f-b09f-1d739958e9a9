import { useAuth } from "@/hooks/use-auth";
import { Avatar, AvatarFallback, AvatarImage } from "@/ui/avatar";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";

const Profile = () => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return <div>Loading ...</div>;
  }

  if (!user) {
    return null;
  }

  const initial = user.email?.charAt(0)?.toUpperCase() || "U";

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle>Profile</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-4">
          <Avatar>
            <AvatarImage src={undefined} alt={user.username} />
            <AvatarFallback>{initial}</AvatarFallback>
          </Avatar>
          <div>
            <h2 className="text-lg font-semibold">{user.username}</h2>
            <p className="text-sm text-muted-foreground">{user.email}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default Profile;
