import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/ui/button";
import { Input } from "@/ui/input";
import { X, Send, Bot, HelpCircle } from "lucide-react";
import { useJoyceChat } from "@/hooks/use-joyce-chat";
import { MarkdownText } from "@/components/ui/markdown-text";

interface JoyceSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  suggestedQuestions?: string[];
  onSendMessage?: (message: string) => void;
  autoSendQuestion?: string;
}

export function JoyceSidebar({ isOpen, onClose, suggestedQuestions = [], onSendMessage, autoSendQuestion }: JoyceSidebarProps) {
  const [message, setMessage] = useState("");
  const { messages, sendMessage, isLoading } = useJoyceChat();

  // Auto-send question when provided
  useEffect(() => {
    if (autoSendQuestion && isOpen) {
      handleSendMessage(autoSendQuestion);
    }
  }, [autoSendQuestion, isOpen]);

  const handleSendMessage = async (messageContent?: string) => {
    const msgToSend = messageContent || message.trim();
    if (msgToSend && !isLoading) {
      if (onSendMessage) {
        onSendMessage(msgToSend);
      } else {
        await sendMessage(msgToSend);
      }
      setMessage("");
    }
  };

  const handleQuestionClick = (question: string) => {
    handleSendMessage(question);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div
      className="h-full bg-white border-l border-gray-200 flex flex-col"
      style={{ width: '400px' }}
    >
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="flex items-center space-x-2">
          <Bot className="w-6 h-6" />
          <div>
            <h3 className="font-semibold">Joyce AI</h3>
            <p className="text-xs opacity-90">Strategic Intelligence Assistant</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onClose}
          className="text-white hover:bg-white/10"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Suggested Questions */}
      {suggestedQuestions.length > 0 && (
        <div className="p-4 border-b border-gray-200 bg-blue-50">
          <h4 className="text-sm font-medium text-blue-900 mb-3">Suggested Questions</h4>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {suggestedQuestions.map((question, idx) => (
              <Button
                key={idx}
                variant="ghost"
                size="sm"
                onClick={() => handleQuestionClick(question)}
                className="w-full justify-start text-left h-auto p-2 text-xs text-blue-700 hover:bg-blue-100"
              >
                <HelpCircle className="w-3 h-3 mr-2 flex-shrink-0" />
                <span className="truncate">{question}</span>
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((msg) => (
          <div
            key={msg.id}
            className={`flex ${msg.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] rounded-lg px-3 py-2 ${
                msg.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : msg.type === 'ai'
                  ? 'bg-gray-100 text-gray-900'
                  : 'bg-blue-50 text-blue-800 border border-blue-200'
              }`}
            >
              {msg.type === 'ai' && (
                <div className="flex items-center space-x-1 mb-1">
                  <Bot className="w-3 h-3" />
                  <span className="text-xs font-medium">Joyce</span>
                </div>
              )}
              {msg.type === 'ai' ? (
                <div className="text-sm">
                  <MarkdownText>{msg.content}</MarkdownText>
                </div>
              ) : (
                <p className="text-sm">{msg.content}</p>
              )}
            </div>
          </div>
        ))}
        
        {/* Joyce thinking bubble */}
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-[80%] rounded-lg px-3 py-2 bg-blue-50 text-blue-800 border border-blue-200">
              <div className="flex items-center space-x-1 mb-1">
                <Bot className="w-3 h-3" />
                <span className="text-xs font-medium">Joyce</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">thinking</span>
                <div className="flex gap-1">
                  <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
                  <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '150ms' }}></div>
                  <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200">
        <div className="flex space-x-2">
          <Input
            placeholder="Ask Joyce about this page..."
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            className="flex-1"
            disabled={isLoading}
          />
          <Button onClick={handleSendMessage} size="sm" disabled={isLoading}>
            <Send className="w-4 h-4" />
          </Button>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Ask about the data you're viewing, strategic insights, or get recommendations.
        </p>
      </div>
    </div>
  );
}