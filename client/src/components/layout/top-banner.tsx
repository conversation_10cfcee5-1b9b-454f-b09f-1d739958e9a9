import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Set<PERSON>s } from "lucide-react";
import { But<PERSON> } from "@/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/ui/dialog";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/ui/tooltip";
import { Link } from "wouter";
import { useAlerts } from "@/hooks/use-alerts";
import { cn } from "@/lib/utils";

interface TopBannerProps {
  onJoyceToggle?: () => void;
  isJoyceOpen?: boolean;
}

export function TopBanner({ onJoyceToggle, isJoyceOpen = false }: TopBannerProps) {
  const { unreadCount, getUnreadAlerts } = useAlerts();
  const unreadAlerts = getUnreadAlerts();

  return (
    <TooltipProvider>
    <header className="bg-white border-b border-gray-200 px-6 py-4 w-full">
      <div className="flex items-center justify-between">
        {/* Rejoyce Logo */}
        <div className="flex items-center">
          <img 
            src="/images/rejoyce-logo--black.png" 
            alt="Rejoyce" 
            className="h-8 w-auto"
          />
        </div>
        
        {/* Utility Menu */}
        <div className="flex items-center space-x-4">
          {/* Help Modal */}
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="icon">
                <HelpCircle className="h-4 w-4 text-gray-600" />
                <span className="sr-only">Help</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <HelpCircle className="h-5 w-5 text-blue-600" />
                  Rejoyce Platform Help
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4 mt-4">
                <div>
                  <h3 className="font-semibold text-lg mb-2">What is Rejoyce?</h3>
                  <p className="text-gray-600 mb-4">
                    Rejoyce is a comprehensive enterprise analytics platform that provides strategic insights, 
                    KPI monitoring, and executive dashboards for telecommunications companies. Built specifically 
                    for analyzing Verizon data and industry benchmarks.
                  </p>
                </div>
                
                <div>
                  <h3 className="font-semibold text-lg mb-2">Platform Sections</h3>
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-blue-600">Digital Mirror</h4>
                      <p className="text-sm text-gray-600">Real-time executive dashboard with key performance indicators and strategic insights.</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-600">Intelligence (SEI Report)</h4>
                      <p className="text-sm text-gray-600">Strategic Enterprise Intelligence reports covering enterprise layer, products & services, customer engagement, and organizational maturity.</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-600">Execution Readiness</h4>
                      <p className="text-sm text-gray-600">Assessment and tracking of organizational readiness for strategic initiatives.</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-600">Action Dashboard</h4>
                      <p className="text-sm text-gray-600">Actionable insights and recommendations for strategic decision-making.</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-blue-600">Joyce AI Assistant</h4>
                      <p className="text-sm text-gray-600">Conversational AI agent that helps analyze data and provides contextual insights.</p>
                    </div>
                  </div>
                </div>
              </div>
            </DialogContent>
          </Dialog>
          
          {/* Alerts Page */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Link href="/alerts">
                <Button variant="outline" size="icon" className="relative">
                  <Bell className="h-4 w-4 text-gray-600" />
                  {unreadCount > 0 && (
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                      {unreadCount > 9 ? '9+' : unreadCount}
                    </span>
                  )}
                  <span className="sr-only">Alerts</span>
                </Button>
              </Link>
            </TooltipTrigger>
            <TooltipContent className="max-w-sm p-4">
              <div className="space-y-3">
                <p className="font-semibold text-sm">
                  {unreadCount === 0 ? 'No new alerts' : `${unreadCount} new alert${unreadCount > 1 ? 's' : ''}`}
                </p>
                {unreadAlerts.slice(0, 3).map(alert => (
                  <Link key={alert.id} href="/alerts">
                    <div className="border-t pt-2 cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded transition-colors">
                      <p className={cn(
                        "font-medium text-xs leading-tight mb-1",
                        alert.severity === 'critical' && "text-red-600",
                        alert.severity === 'high' && "text-orange-600",
                        alert.severity === 'medium' && "text-yellow-600",
                        alert.severity === 'low' && "text-blue-600"
                      )}>
                        {alert.title}
                      </p>
                      <p className="text-gray-600 text-xs leading-tight">{alert.description.length > 80 ? `${alert.description.slice(0, 80)}...` : alert.description}</p>
                      <p className="text-gray-400 text-xs mt-1">{new Date(alert.timestamp).toLocaleDateString()}</p>
                    </div>
                  </Link>
                ))}
                {unreadAlerts.length > 3 && (
                  <Link href="/alerts">
                    <div className="border-t pt-2 text-center cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded transition-colors">
                      <p className="text-xs text-blue-600 font-medium">
                        +{unreadAlerts.length - 3} more alerts - View all
                      </p>
                    </div>
                  </Link>
                )}
                {unreadCount === 0 && (
                  <Link href="/alerts">
                    <div className="text-center cursor-pointer hover:bg-gray-50 -mx-2 px-2 py-1 rounded transition-colors">
                      <p className="text-xs text-blue-600 font-medium">View all alerts</p>
                    </div>
                  </Link>
                )}
              </div>
            </TooltipContent>
          </Tooltip>
          
          {/* Settings Page */}
          <Link href="/settings">
            <Button variant="outline" size="icon">
              <Settings className="h-4 w-4 text-gray-600" />
              <span className="sr-only">Settings</span>
            </Button>
          </Link>
        </div>
      </div>
    </header>
    </TooltipProvider>
  );
}