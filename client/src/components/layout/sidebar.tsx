import { Home, Settings, Brain, Rocket, User, X, Search, Check, Menu, ChevronLeft, ChevronDown, ChevronRight, Co<PERSON>, Clock, TestTube, Wrench } from "lucide-react";
import { Link, useLocation } from "wouter";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { useAuth } from "@/hooks/use-auth";
import { useCompany } from "@/contexts/company-context";
import { useCompanyData } from "@/hooks/use-company-data";
import { CompanyService, CompanyInfo } from "@/services/company-service";
import { useState, useEffect } from "react";

interface SidebarProps {
  isJoyceOpen?: boolean;
}

export function Sidebar({ isJoyceOpen = false }: SidebarProps = {}) {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  const { selectedCompany, setSelectedCompany, companyDisplayName } = useCompany();
  const { data: productsServicesData } = useCompanyData(selectedCompany, "products-services");
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<CompanyInfo[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [selectedCompanyFullName, setSelectedCompanyFullName] = useState<string>('');
  const [isCollapsed, setIsCollapsed] = useState(() => {
    // Load collapsed state from localStorage
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });
  const [executionExpanded, setExecutionExpanded] = useState(() => {
    // Auto-expand Execution if we're on an Execution page
    return location.startsWith("/execution");
  });
  const [strategyExpanded, setStrategyExpanded] = useState(() => {
    // Auto-expand Strategy if we're on a Strategy page
    return location.startsWith("/strategy");
  });
  const [seiLabExpanded, setSeiLabExpanded] = useState(() => {
    // Auto-expand SEI Lab if we're on an SEI Lab page
    return location.startsWith("/sei-lab");
  });
  const [seiToolboxExpanded, setSeiToolboxExpanded] = useState(() => {
    // Auto-expand SEI Toolbox if we're on an SEI Toolbox page
    return location.startsWith("/sei-toolbox");
  });
  const [productsExpanded, setProductsExpanded] = useState(false);
  const companyService = CompanyService.getInstance();

  // Save collapsed state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('sidebar-collapsed', JSON.stringify(isCollapsed));
    // Emit event for same-tab updates
    window.dispatchEvent(new Event('sidebar-collapsed-changed'));
  }, [isCollapsed]);

  // Auto-expand Execution section when on Execution pages
  useEffect(() => {
    if (location.startsWith("/execution")) {
      setExecutionExpanded(true);
    }
  }, [location]);

  // Auto-expand Strategy section when on Strategy pages
  useEffect(() => {
    if (location.startsWith("/strategy")) {
      setStrategyExpanded(true);
    }
  }, [location]);

  // Auto-expand SEI Lab section when on SEI Lab pages
  useEffect(() => {
    if (location.startsWith("/sei-lab")) {
      setSeiLabExpanded(true);
    }
  }, [location]);

  // Auto-expand SEI Toolbox section when on SEI Toolbox pages
  useEffect(() => {
    if (location.startsWith("/sei-toolbox")) {
      setSeiToolboxExpanded(true);
    }
  }, [location]);


  // Auto-collapse left sidebar when Joyce opens
  useEffect(() => {
    if (isJoyceOpen) {
      setIsCollapsed(true);
    }
  }, [isJoyceOpen]);

  useEffect(() => {
    const searchCompanies = async () => {
      if (showSearch) {
        const results = await companyService.searchCompanies(searchQuery);
        setSearchResults(results);
      }
    };
    searchCompanies();
  }, [searchQuery, showSearch]);


  useEffect(() => {
    // Fetch full company name when selected company changes
    const fetchCompanyFullName = async () => {
      if (selectedCompany) {
        try {
          const fullName = await companyService.getCompanyFullName(selectedCompany);
          setSelectedCompanyFullName(fullName);
        } catch (error) {
          console.error('Error fetching company full name:', error);
          setSelectedCompanyFullName(selectedCompany.toUpperCase());
        }
      }
    };

    fetchCompanyFullName();
  }, [selectedCompany, companyService]);

  const handleCompanySelect = (companyCode: string) => {
    setSelectedCompany(companyCode);
    setShowSearch(false);
    setSearchQuery('');
  };

  const handleClearCompany = () => {
    setShowSearch(true);
    setSearchQuery('');
  };

  const handleToggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleNavClick = () => {
    setIsCollapsed(true);
  };

  const navigation = [
    { name: "Home", href: "/", icon: Home },
    { name: "Digital Mirror", href: "/digital-mirror", icon: Copy },
  ];

  const executionTabs = [
    { id: "executive-summary", label: "Executive Summary" },
    { id: "enterprise-layer", label: "Enterprise Layer" },
    { id: "products-services", label: "Products & Services" },
    { id: "customer-engagement", label: "Customer Engagement" },
    { id: "organizational-maturity", label: "Organizational Maturity" },
    { id: "execution-roadmap", label: "Execution Roadmap" },
  ];

  const strategyTabs = [
    { id: "executive-summary-dashboard", label: "Executive Summary Dashboard" },
    { id: "company-context-benchmark-explorer", label: "Company Context & Benchmark Explorer" },
    { id: "execution-maturity-joy-score-deep-dive", label: "Execution Maturity (Joy Score) Deep Dive" },
    { id: "agility-simulator", label: "Agility Simulator" },
    { id: "value-leakage-financial-impact", label: "Value Leakage & Financial Impact" },
    { id: "strategic-lens-engine", label: "Strategic Lens Engine" },
    { id: "recommendations-roadmap", label: "Recommendations & Roadmap" },
  ];

  const seiLabTabs = [
    { id: "interactive-qa-scenario-planner", label: "Interactive Q&A and Scenario Planner" },
  ];

  const seiToolboxTabs = [
    { id: "best-practice-library", label: "Best Practice Library" },
  ];

  const seiReportTabs = [
    { id: "executive-summary", label: "Executive Summary" },
    { id: "enterprise-layer", label: "Enterprise Layer" },
    { id: "products-services", label: "Products & Services" },
    { id: "customer-engagement", label: "Customer Engagement" },
    { id: "organizational-maturity", label: "Organizational Maturity" },
    { id: "execution-roadmap", label: "Execution Roadmap" },
  ];

  const eraReportTabs = [
    { id: "executive-summary-dashboard", label: "Executive Summary Dashboard" },
    { id: "company-context-benchmark-explorer", label: "Company Context & Benchmark Explorer" },
    { id: "execution-maturity-joy-score-deep-dive", label: "Execution Maturity (Joy Score) Deep Dive" },
    { id: "agility-simulator", label: "Agility Simulator" },
    { id: "value-leakage-financial-impact", label: "Value Leakage & Financial Impact" },
    { id: "strategic-lens-engine", label: "Strategic Lens Engine" },
    { id: "best-practice-library", label: "Best Practice Library" },
    { id: "recommendations-roadmap", label: "Recommendations & Roadmap" },
    { id: "interactive-qa-scenario-planner", label: "Interactive Q&A and Scenario Planner" },
  ];

  return (
    <div className={`${isCollapsed ? 'w-16' : 'w-64'} bg-white border-r border-gray-200 flex flex-col h-full transition-all duration-300 relative`}>
      {/* Collapse Button */}
      <div className={`flex ${isCollapsed ? 'justify-center' : 'justify-end'} p-2`}>
        <button
          onClick={handleToggleCollapse}
          className="bg-gray-100 hover:bg-gray-200 rounded p-1 transition-colors duration-200"
        >
          {isCollapsed ? (
            <Menu className="w-4 h-4 text-gray-600" />
          ) : (
            <ChevronLeft className="w-4 h-4 text-gray-600" />
          )}
        </button>
      </div>
      {/* Company Selector - Always visible */}
      <div className={`${isCollapsed ? 'px-2 pb-2' : 'px-4 pb-4'} border-b border-gray-200`}>
        <div className="space-y-2">
          <div className="text-xs font-medium text-gray-700">
            {isCollapsed ? 'Company' : 'Focus Company'}
          </div>
          
          {isCollapsed ? (
            <div className="flex justify-center">
              <div className="bg-gray-100 rounded px-2 py-1 text-center">
                <div className="font-medium text-xs">{selectedCompany}</div>
              </div>
            </div>
          ) : (
            <>
            {!showSearch ? (
            <div className="flex items-center justify-between bg-gray-100 rounded px-3 py-2">
              <div className="flex-1">
                <div className="font-medium">{companyDisplayName}</div>
                <div className="text-xs text-gray-500">{selectedCompanyFullName}</div>
              </div>
              <X className="w-3 h-3 text-gray-400 cursor-pointer" onClick={handleClearCompany} />
            </div>
          ) : (
            <div className="space-y-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <Input
                  type="text"
                  placeholder="Search companies..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 text-sm"
                  autoFocus
                />
              </div>
              
              {searchResults.length > 0 && (
                <div className="max-h-40 overflow-y-auto bg-white border border-gray-200 rounded-md shadow-sm">
                  {searchResults.map((company) => (
                    <div
                      key={company.code}
                      className="px-3 py-2 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-b-0"
                      onClick={() => handleCompanySelect(company.code)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{company.displayName}</div>
                          <div className="text-xs text-gray-500">{company.fullName}</div>
                        </div>
                        {selectedCompany === company.code && (
                          <Check className="w-4 h-4 text-green-600" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {searchResults.length === 0 && searchQuery && (
                <div className="text-sm text-gray-500 px-3 py-2 bg-gray-50 rounded">
                  No companies found matching "{searchQuery}"
                </div>
              )}
            </div>
          )}
            </>
          )}
        </div>
      </div>


      {/* Collapsible Navigation Section */}
      <div className="flex flex-col flex-1 relative">
        
        {/* Navigation Menu */}
        <nav className={`flex-1 ${isCollapsed ? 'p-2 pt-4' : 'p-4 pt-4'}`}>
          <ul className="space-y-2">
            {/* Regular Navigation Items */}
            {navigation.map((item) => {
              const Icon = item.icon;
              const isActive = item.href === "/" 
                ? location === "/" 
                : location.startsWith(item.href);
              
              return (
                <li key={item.name}>
                  <Link href={item.href}>
                    <div 
                      className={`flex items-center ${isCollapsed ? 'justify-center p-2' : 'space-x-3 px-3 py-2'} rounded-lg ${
                        isActive
                          ? "bg-rejoyce-green text-white" 
                          : "text-gray-700 hover:bg-gray-100"
                      }`}
                      onClick={handleNavClick}
                      title={isCollapsed ? item.name : undefined}
                    >
                      <Icon className="w-5 h-5" />
                      {!isCollapsed && <span>{item.name}</span>}
                    </div>
                  </Link>
                </li>
              );
            })}

            {/* Strategy - Expandable */}
            <li>
              {isCollapsed ? (
                <Link href="/strategy">
                  <div 
                    className={`flex items-center justify-center p-2 rounded-lg ${
                      location.startsWith("/strategy")
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={handleNavClick}
                    title="Strategy"
                  >
                    <Clock className="w-5 h-5" />
                  </div>
                </Link>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <Link href="/strategy/executive-summary-dashboard">
                      <div className="flex items-center space-x-3 text-gray-700 px-3 py-2 hover:bg-gray-100 rounded-lg flex-1">
                        <Clock className="w-5 h-5" />
                        <span>Strategy</span>
                      </div>
                    </Link>
                    <button
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => setStrategyExpanded(!strategyExpanded)}
                    >
                      {strategyExpanded ? 
                        <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      }
                    </button>
                  </div>
                  {strategyExpanded && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {strategyTabs.map((tab) => (
                        <li key={tab.id}>
                          <Link href={`/strategy/${tab.id}`}>
                            <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                              location === `/strategy/${tab.id}` 
                                ? "bg-rejoyce-green text-white" 
                                : "text-gray-600 hover:bg-gray-100"
                            }`}>
                              <span>{tab.label}</span>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>

            {/* Execution - Expandable */}
            <li>
              {isCollapsed ? (
                <Link href="/execution">
                  <div 
                    className={`flex items-center justify-center p-2 rounded-lg ${
                      location.startsWith("/execution")
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={handleNavClick}
                    title="Execution"
                  >
                    <Brain className="w-5 h-5" />
                  </div>
                </Link>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <Link href="/execution/executive-summary">
                      <div className="flex items-center space-x-3 text-gray-700 px-3 py-2 hover:bg-gray-100 rounded-lg flex-1">
                        <Brain className="w-5 h-5" />
                        <span>Execution</span>
                      </div>
                    </Link>
                    <button
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => setExecutionExpanded(!executionExpanded)}
                    >
                      {executionExpanded ? 
                        <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      }
                    </button>
                  </div>
                  {executionExpanded && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {executionTabs.map((tab) => (
                        <li key={tab.id}>
                          {tab.id === "products-services" ? (
                            <>
                              <div 
                                className="flex items-center justify-between cursor-pointer"
                                onClick={() => setProductsExpanded(!productsExpanded)}
                              >
                                <Link href={`/execution/${tab.id}`}>
                                  <div className={`flex-1 rounded-lg px-3 py-2 text-sm ${
                                    location === `/execution/${tab.id}` 
                                      ? "bg-rejoyce-green text-white" 
                                      : "text-gray-600 hover:bg-gray-100"
                                  }`}>
                                    {tab.label}
                                  </div>
                                </Link>
                                {productsExpanded ? 
                                  <ChevronDown className="w-3 h-3 text-gray-400 mr-2" /> : 
                                  <ChevronRight className="w-3 h-3 text-gray-400 mr-2" />
                                }
                              </div>
                              {productsExpanded && (productsServicesData as any)?.journeyStages && (
                                <ul className="ml-6 mt-1 space-y-1">
                                  {(productsServicesData as any).journeyStages.map((stage: any) => (
                                    <li key={stage.id}>
                                      <button
                                        className="w-full text-left px-3 py-1 text-xs text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded"
                                        onClick={() => {
                                          // Handle journey stage navigation
                                          // This would need to be coordinated with the main page
                                        }}
                                      >
                                        {stage.label}
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </>
                          ) : (
                            <Link href={`/execution/${tab.id}`}>
                              <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                                location === `/execution/${tab.id}` 
                                  ? "bg-rejoyce-green text-white" 
                                  : "text-gray-600 hover:bg-gray-100"
                              }`}>
                                <span>{tab.label}</span>
                              </div>
                            </Link>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>

            {/* SEI Lab - Expandable */}
            <li>
              {isCollapsed ? (
                <Link href="/sei-lab">
                  <div 
                    className={`flex items-center justify-center p-2 rounded-lg ${
                      location.startsWith("/sei-lab")
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={handleNavClick}
                    title="SEI Lab"
                  >
                    <TestTube className="w-5 h-5" />
                  </div>
                </Link>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <Link href="/sei-lab/interactive-qa-scenario-planner">
                      <div className="flex items-center space-x-3 text-gray-700 px-3 py-2 hover:bg-gray-100 rounded-lg flex-1">
                        <TestTube className="w-5 h-5" />
                        <span>SEI Lab</span>
                      </div>
                    </Link>
                    <button
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => setSeiLabExpanded(!seiLabExpanded)}
                    >
                      {seiLabExpanded ? 
                        <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      }
                    </button>
                  </div>
                  {seiLabExpanded && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {seiLabTabs.map((tab) => (
                        <li key={tab.id}>
                          <Link href={`/sei-lab/${tab.id}`}>
                            <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                              location === `/sei-lab/${tab.id}` 
                                ? "bg-rejoyce-green text-white" 
                                : "text-gray-600 hover:bg-gray-100"
                            }`}>
                              <span>{tab.label}</span>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>

            {/* SEI Toolbox - Expandable */}
            <li>
              {isCollapsed ? (
                <Link href="/sei-toolbox">
                  <div 
                    className={`flex items-center justify-center p-2 rounded-lg ${
                      location.startsWith("/sei-toolbox")
                        ? "bg-rejoyce-green text-white" 
                        : "text-gray-700 hover:bg-gray-100"
                    }`}
                    onClick={handleNavClick}
                    title="SEI Toolbox"
                  >
                    <Wrench className="w-5 h-5" />
                  </div>
                </Link>
              ) : (
                <>
                  <div className="flex items-center justify-between">
                    <Link href="/sei-toolbox/best-practice-library">
                      <div className="flex items-center space-x-3 text-gray-700 px-3 py-2 hover:bg-gray-100 rounded-lg flex-1">
                        <Wrench className="w-5 h-5" />
                        <span>SEI Toolbox</span>
                      </div>
                    </Link>
                    <button
                      className="p-1 hover:bg-gray-100 rounded"
                      onClick={() => setSeiToolboxExpanded(!seiToolboxExpanded)}
                    >
                      {seiToolboxExpanded ? 
                        <ChevronDown className="w-4 h-4 text-gray-400" /> : 
                        <ChevronRight className="w-4 h-4 text-gray-400" />
                      }
                    </button>
                  </div>
                  {seiToolboxExpanded && (
                    <ul className="ml-8 mt-1 space-y-1">
                      {seiToolboxTabs.map((tab) => (
                        <li key={tab.id}>
                          <Link href={`/sei-toolbox/${tab.id}`}>
                            <div className={`flex items-center space-x-2 rounded-lg px-3 py-2 text-sm ${
                              location === `/sei-toolbox/${tab.id}` 
                                ? "bg-rejoyce-green text-white" 
                                : "text-gray-600 hover:bg-gray-100"
                            }`}>
                              <span>{tab.label}</span>
                            </div>
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </>
              )}
            </li>

          </ul>
        </nav>
      </div>

      {/* User Section */}
        <div className={`${isCollapsed ? 'p-2' : 'p-4'} border-t border-gray-200`}>
          {isCollapsed ? (
            <div className="flex justify-center">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={logout}
                className="h-8 w-8 p-0"
                title="Logout"
              >
                <User className="w-4 h-4" />
              </Button>
            </div>
          ) : (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2 text-gray-700">
                <User className="w-4 h-4" />
                <span className="text-sm">{user?.username || "Analyst"}</span>
              </div>
              <Button variant="ghost" size="sm" onClick={logout}>
                Logout
              </Button>
            </div>
          )}
        </div>
    </div>
  );
}
