import { useState, useEffect } from "react";
import { Bo<PERSON>, ChevronRight } from "lucide-react";
import { TopBanner } from "./top-banner";
import { Sidebar } from "./sidebar";
import { JoyceSidebar } from "./joyce-sidebar";

interface MainLayoutProps {
  children: React.ReactNode;
  joyceSuggestedQuestions?: string[];
  onJoyceMessage?: (message: string) => void;
}

// Create a context to allow children to control Joyce sidebar
import { createContext, useContext } from "react";

interface JoyceContextType {
  openJoyce: (questions?: string[]) => void;
  askJoyce: (question: string) => void;
  isJoyceOpen: boolean;
}

const JoyceContext = createContext<JoyceContextType | undefined>(undefined);

export const useJoyce = () => {
  const context = useContext(JoyceContext);
  if (!context) {
    throw new Error('useJoyce must be used within MainLayout');
  }
  return context;
};

export function MainLayout({ children, joyceSuggestedQuestions, onJoyceMessage }: MainLayoutProps) {
  const [isJoyceOpen, setIsJoyceOpen] = useState(false);
  const [currentSuggestedQuestions, setCurrentSuggestedQuestions] = useState<string[]>([]);
  const [autoSendQuestion, setAutoSendQuestion] = useState<string | undefined>();
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(() => {
    // Load collapsed state from localStorage
    const saved = localStorage.getItem('sidebar-collapsed');
    return saved ? JSON.parse(saved) : false;
  });

  // Update suggested questions when they change from parent components
  useEffect(() => {
    if (joyceSuggestedQuestions && joyceSuggestedQuestions.length > 0) {
      setCurrentSuggestedQuestions(joyceSuggestedQuestions);
    }
  }, [joyceSuggestedQuestions]);

  // Sync sidebar collapsed state with localStorage
  useEffect(() => {
    const handleStorageChange = () => {
      const saved = localStorage.getItem('sidebar-collapsed');
      if (saved) {
        setIsSidebarCollapsed(JSON.parse(saved));
      }
    };

    window.addEventListener('storage', handleStorageChange);
    // Also listen for custom events for same-tab updates
    window.addEventListener('sidebar-collapsed-changed', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('sidebar-collapsed-changed', handleStorageChange);
    };
  }, []);

  const handleJoyceToggle = () => {
    setIsJoyceOpen(!isJoyceOpen);
  };

  const handleJoyceClose = () => {
    setIsJoyceOpen(false);
  };

  const openJoyce = (questions?: string[]) => {
    if (questions) {
      setCurrentSuggestedQuestions(questions);
    }
    setIsJoyceOpen(true);
  };

  const askJoyce = (question: string) => {
    setAutoSendQuestion(question);
    setIsJoyceOpen(true);
    // Clear auto-send after a brief delay to allow the sidebar to process it
    setTimeout(() => setAutoSendQuestion(undefined), 100);
  };

  const joyceContextValue: JoyceContextType = {
    openJoyce,
    askJoyce,
    isJoyceOpen
  };

  return (
    <JoyceContext.Provider value={joyceContextValue}>
      <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Top Banner - Full Width */}
      <TopBanner onJoyceToggle={handleJoyceToggle} isJoyceOpen={isJoyceOpen} />
      
      {/* 3-Column Layout Below Banner */}
      <div className="flex flex-1 h-[calc(100vh-73px)]">
        {/* Left Sidebar Column */}
        <div className={`transition-all duration-300 ${isSidebarCollapsed ? 'w-16' : 'w-64'} relative overflow-visible`}>
          <Sidebar isJoyceOpen={isJoyceOpen} />
        </div>
        
        {/* Main Content Column */}
        <div className="flex-1 overflow-auto relative">
          {children}
          
          {/* Joyce Chat Toggle Tab - Only show when chat is closed */}
          {!isJoyceOpen && (
            <div className="fixed z-30 right-0" style={{ top: 'calc(73px + 4.5%)' }}>
              <button
                onClick={handleJoyceToggle}
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 border-r-0 rounded-l-md px-3 py-2 shadow-md transition-all duration-200 group flex items-center justify-center"
                style={{ 
                  marginTop: '-1px',
                  minWidth: '32px',
                  minHeight: '40px'
                }}
              >
                <Bot className="w-4 h-4 text-white" />
              </button>
            </div>
          )}
        </div>

        {/* Joyce Chat Column - Fixed positioned */}
        <div className={`fixed top-[73px] right-0 h-[calc(100vh-73px)] z-20 transition-all duration-300 overflow-hidden ${isJoyceOpen ? 'w-[400px]' : 'w-0'}`}>
          {/* Collapse Toggle Button - outside of Joyce sidebar so it stays visible */}
          {isJoyceOpen && (
            <div className="absolute left-[-40px] z-30" style={{ top: '4.5%' }}>
              <button
                onClick={handleJoyceToggle}
                className="bg-blue-600 hover:bg-blue-700 border-r-0 rounded-l-md px-3 py-2 shadow-md transition-all duration-200 flex items-center justify-center"
                style={{ 
                  marginTop: '-1px',
                  minWidth: '32px',
                  minHeight: '40px'
                }}
              >
                <ChevronRight className="w-4 h-4 text-white" />
              </button>
            </div>
          )}
          
          <JoyceSidebar 
            isOpen={isJoyceOpen} 
            onClose={handleJoyceClose}
            suggestedQuestions={currentSuggestedQuestions}
            onSendMessage={onJoyceMessage}
            autoSendQuestion={autoSendQuestion}
          />
        </div>
      </div>
    </div>
    </JoyceContext.Provider>
  );
}