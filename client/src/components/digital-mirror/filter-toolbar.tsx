import { useState } from "react";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { Badge } from "@/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/ui/toggle-group";
import { Search, Filter, X, SortAsc } from "lucide-react";

export interface FilterState {
  layer: "all" | "enterprise" | "product" | "customer";
  category: "all" | "financial" | "customer" | "operational" | "maturity";
  status: "all" | "on-track" | "at-risk" | "off-track";
  persona: "investor" | "executive" | "operator";
  fogScore: "all" | "high" | "low";
  search: string;
  sortBy: "gap" | "upside" | "benchmark" | "trend";
}

interface FilterToolbarProps {
  filters: FilterState;
  onFiltersChange: (filters: FilterState) => void;
  activeFilterCount: number;
}

export function FilterToolbar({ filters, onFiltersChange, activeFilterCount }: FilterToolbarProps) {
  const updateFilter = (key: keyof FilterState, value: string) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      layer: "all",
      category: "all", 
      status: "all",
      persona: "executive",
      fogScore: "all",
      search: "",
      sortBy: "gap"
    });
  };

  return (
    <div className="bg-white border-b border-gray-200 p-4 space-y-4">
      {/* Primary Filters Row */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Search */}
        <div className="relative flex-1 min-w-[200px] max-w-[300px]">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Search KPIs..."
            value={filters.search}
            onChange={(e) => updateFilter("search", e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Layer Filter */}
        <Select value={filters.layer} onValueChange={(value) => updateFilter("layer", value)}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Layer" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Layers</SelectItem>
            <SelectItem value="enterprise">Enterprise</SelectItem>
            <SelectItem value="product">Product/Service</SelectItem>
            <SelectItem value="customer">Customer Journey</SelectItem>
          </SelectContent>
        </Select>

        {/* Category Filter */}
        <Select value={filters.category} onValueChange={(value) => updateFilter("category", value)}>
          <SelectTrigger className="w-[130px]">
            <SelectValue placeholder="Category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            <SelectItem value="financial">Financial</SelectItem>
            <SelectItem value="customer">Customer</SelectItem>
            <SelectItem value="operational">Operational</SelectItem>
            <SelectItem value="maturity">Maturity</SelectItem>
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select value={filters.status} onValueChange={(value) => updateFilter("status", value)}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="on-track">On Track</SelectItem>
            <SelectItem value="at-risk">At Risk</SelectItem>
            <SelectItem value="off-track">Off Track</SelectItem>
          </SelectContent>
        </Select>

        {/* Sort */}
        <Select value={filters.sortBy} onValueChange={(value) => updateFilter("sortBy", value)}>
          <SelectTrigger className="w-[140px]">
            <SortAsc className="w-4 h-4 mr-2" />
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="gap">Largest Gap</SelectItem>
            <SelectItem value="upside">Highest Upside</SelectItem>
            <SelectItem value="benchmark">Benchmark Delta</SelectItem>
            <SelectItem value="trend">Worst Trend</SelectItem>
          </SelectContent>
        </Select>

        {/* Clear Filters */}
        {activeFilterCount > 0 && (
          <Button 
            variant="ghost" 
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4 mr-1" />
            Clear ({activeFilterCount})
          </Button>
        )}
      </div>

      {/* Secondary Filters Row */}
      <div className="flex flex-wrap items-center gap-3">
        {/* Persona Lens */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Persona:</span>
          <ToggleGroup 
            type="single" 
            value={filters.persona} 
            onValueChange={(value) => value && updateFilter("persona", value)}
            className="h-8"
          >
            <ToggleGroupItem value="investor" className="text-xs px-3">
              Investor
            </ToggleGroupItem>
            <ToggleGroupItem value="executive" className="text-xs px-3">
              Executive
            </ToggleGroupItem>
            <ToggleGroupItem value="operator" className="text-xs px-3">
              Operator
            </ToggleGroupItem>
          </ToggleGroup>
        </div>

        {/* Fog Score Filter */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">Confidence:</span>
          <ToggleGroup 
            type="single" 
            value={filters.fogScore} 
            onValueChange={(value) => value && updateFilter("fogScore", value)}
            className="h-8"
          >
            <ToggleGroupItem value="all" className="text-xs px-3">
              All
            </ToggleGroupItem>
            <ToggleGroupItem value="high" className="text-xs px-3">
              High (&gt;70%)
            </ToggleGroupItem>
            <ToggleGroupItem value="low" className="text-xs px-3">
              Low (&lt;70%)
            </ToggleGroupItem>
          </ToggleGroup>
        </div>
      </div>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="flex flex-wrap items-center gap-2">
          <span className="text-xs text-gray-500">Active filters:</span>
          {filters.layer !== "all" && (
            <Badge variant="secondary" className="text-xs">
              Layer: {filters.layer}
            </Badge>
          )}
          {filters.category !== "all" && (
            <Badge variant="secondary" className="text-xs">
              Category: {filters.category}
            </Badge>
          )}
          {filters.status !== "all" && (
            <Badge variant="secondary" className="text-xs">
              Status: {filters.status}
            </Badge>
          )}
          {filters.fogScore !== "all" && (
            <Badge variant="secondary" className="text-xs">
              Confidence: {filters.fogScore}
            </Badge>
          )}
          {filters.search && (
            <Badge variant="secondary" className="text-xs">
              Search: "{filters.search}"
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}