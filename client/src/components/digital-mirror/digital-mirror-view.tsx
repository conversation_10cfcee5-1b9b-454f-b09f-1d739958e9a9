import { useState, useMemo, useEffect } from "react";
import { useLocation } from "wouter";
import { MirrorKPICard, MirrorKPIData } from "./mirror-kpi-card";
import { SankeyKPIDiagram } from "./sankey-kpi-diagram";
import { FilterToolbar, FilterState } from "./filter-toolbar";
import { Button } from "@/ui/button";
import { Badge } from "@/ui/badge";
import { ToggleGroup, ToggleGroupItem } from "@/ui/toggle-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/ui/collapsible";
import { ChevronDown, Download, Grid, BarChart3, X, GitBranch } from "lucide-react";
import { ResponsiveContainer, LineChart, Line, CartesianGrid, XAxis, YAxis, Tooltip as RechartsTooltip } from "recharts";
interface DigitalMirrorViewProps {
  kpis: MirrorKPIData[];
  onJoyceOpen?: (questions: string[]) => void;
}

export function DigitalMirrorView({ kpis, onJoyceOpen }: DigitalMirrorViewProps) {
  const [location, setLocation] = useLocation();
  
  // Parse URL parameters
  const searchParams = new URLSearchParams(location.split('?')[1] || '');
  const urlMode = searchParams.get('mode') as "era" | "sei" | null;
  const urlViewMode = searchParams.get('view') as "cards" | "graph" | "sankey" | null;
  
  const [mode, setMode] = useState<"era" | "sei">(urlMode || "sei");
  const [viewMode, setViewMode] = useState<"cards" | "graph" | "sankey">(urlViewMode || "cards");
  
  // Update URL when mode or viewMode changes
  useEffect(() => {
    const params = new URLSearchParams();
    if (mode !== "sei") params.set('mode', mode);
    if (viewMode !== "cards") params.set('view', viewMode);
    
    const newSearch = params.toString();
    const currentPath = location.split('?')[0];
    const newLocation = newSearch ? `${currentPath}?${newSearch}` : currentPath;
    
    if (newLocation !== location) {
      setLocation(newLocation);
    }
  }, [mode, viewMode, location, setLocation]);
  const [selectedKPIs, setSelectedKPIs] = useState<string[]>([]);
  const [expandedKPI, setExpandedKPI] = useState<MirrorKPIData | null>(null);
  const [expandedLayers, setExpandedLayers] = useState({
    enterprise: true,
    product: true,
    customer: true
  });

  const [filters, setFilters] = useState<FilterState>({
    layer: "all",
    category: "all",
    status: "all",
    persona: "executive",
    fogScore: "all",
    search: "",
    sortBy: "gap"
  });

  const filteredKPIs = useMemo(() => {
    let filtered = kpis.filter(kpi => {
      if (filters.layer !== "all" && kpi.layer !== filters.layer) return false;
      if (filters.category !== "all" && kpi.category !== filters.category) return false;
      if (filters.status !== "all" && kpi.status !== filters.status) return false;
      if (filters.fogScore === "high" && kpi.fogScore < 70) return false;
      if (filters.fogScore === "low" && kpi.fogScore >= 70) return false;
      if (filters.search && !kpi.name.toLowerCase().includes(filters.search.toLowerCase())) return false;
      return true;
    });

    // Sort
    switch (filters.sortBy) {
      case "gap":
        filtered.sort((a, b) => {
          const aGap = a.target ? Math.abs(a.currentValue - a.target) : 0;
          const bGap = b.target ? Math.abs(b.currentValue - b.target) : 0;
          return bGap - aGap;
        });
        break;
      case "upside":
        filtered.sort((a, b) => (b.executionAlphaImpact || 0) - (a.executionAlphaImpact || 0));
        break;
      case "benchmark":
        filtered.sort((a, b) => Math.abs(b.benchmarkDelta) - Math.abs(a.benchmarkDelta));
        break;
      case "trend":
        filtered.sort((a, b) => {
          const aTrend = a.trend[a.trend.length - 1] - a.trend[a.trend.length - 2];
          const bTrend = b.trend[b.trend.length - 1] - b.trend[b.trend.length - 2];
          return aTrend - bTrend; // Worst trend first
        });
        break;
    }

    return filtered;
  }, [kpis, filters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.layer !== "all") count++;
    if (filters.category !== "all") count++;
    if (filters.status !== "all") count++;
    if (filters.fogScore !== "all") count++;
    if (filters.search) count++;
    return count;
  }, [filters]);

  const kpisByLayer = useMemo(() => {
    return {
      enterprise: filteredKPIs.filter(kpi => kpi.layer === "enterprise"),
      product: filteredKPIs.filter(kpi => kpi.layer === "product"),
      customer: filteredKPIs.filter(kpi => kpi.layer === "customer")
    };
  }, [filteredKPIs]);

  const handleKPIClick = (kpi: MirrorKPIData) => {
    setExpandedKPI(kpi);
  };

  const handleJoyceAsk = (kpi: MirrorKPIData) => {
    const question = `Analyze the ${kpi.name} KPI performance. Current value is ${kpi.currentValue}${kpi.unit}, target is ${kpi.target}${kpi.unit}. What are the key drivers and recommended actions?`;
    onJoyceOpen?.([question]);
  };

  const handleBulkJoyceAsk = () => {
    const selectedKPIData = kpis.filter(kpi => selectedKPIs.includes(kpi.id));
    const questions = selectedKPIData.map(kpi => 
      `${kpi.name}: ${kpi.currentValue}${kpi.unit} (target: ${kpi.target}${kpi.unit})`
    );
    const bulkQuestion = `Analyze these KPIs together and identify systemic issues: ${questions.join(", ")}`;
    onJoyceOpen?.([bulkQuestion]);
  };

  const toggleLayerExpansion = (layer: keyof typeof expandedLayers) => {
    setExpandedLayers(prev => ({ ...prev, [layer]: !prev[layer] }));
  };

  const FlowDiagramView = () => {
    const enterpriseKPIs = kpisByLayer.enterprise;
    const productKPIs = kpisByLayer.product;
    const customerKPIs = kpisByLayer.customer;

    // Build causal connections for rendering flow paths
    const buildCausalConnections = () => {
      const connections: Array<{
        from: { x: number; y: number; kpi: MirrorKPIData };
        to: { x: number; y: number; kpi: MirrorKPIData };
        strength: number;
        impact: string;
      }> = [];

      // Generic function to find KPI matches across layers using flexible string matching
      const findKPIMatch = (targetName: string, targetLayer: string, allKPIs: MirrorKPIData[]) => {
        const layerKPIs = allKPIs.filter(k => k.layer === targetLayer);
        
        // Try multiple matching strategies
        const matchStrategies = [
          // Exact match
          (kpi: MirrorKPIData) => kpi.name.toLowerCase() === targetName.toLowerCase(),
          // Contains match
          (kpi: MirrorKPIData) => kpi.name.toLowerCase().includes(targetName.toLowerCase()),
          // Reverse contains
          (kpi: MirrorKPIData) => targetName.toLowerCase().includes(kpi.name.toLowerCase()),
          // Keywords match (remove common words and match on key terms)
          (kpi: MirrorKPIData) => {
            const getKeywords = (str: string) => str.toLowerCase()
              .replace(/[^a-z0-9\s]/g, '')
              .split(/\s+/)
              .filter(w => !['the', 'and', 'or', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(w));
            
            const targetKeywords = getKeywords(targetName);
            const kpiKeywords = getKeywords(kpi.name);
            
            return targetKeywords.some(tk => kpiKeywords.some(kk => kk.includes(tk) || tk.includes(kk)));
          }
        ];

        // Try each strategy until we find a match
        for (const strategy of matchStrategies) {
          const match = layerKPIs.find(strategy);
          if (match) return match;
        }

        return null;
      };

      // Build connections from all KPIs that have causal outputs
      filteredKPIs.forEach((sourceKPI, sourceIndex) => {
        if (sourceKPI.causalOutputs && sourceKPI.causalOutputs.length > 0) {
          sourceKPI.causalOutputs.forEach(output => {
            const targetKPI = findKPIMatch(output.targetKpi || "", output.layer, filteredKPIs);
            
            if (targetKPI) {
              const sourceLayerKPIs = filteredKPIs.filter(k => k.layer === sourceKPI.layer);
              const targetLayerKPIs = filteredKPIs.filter(k => k.layer === targetKPI.layer);
              
              const sourceIndexInLayer = sourceLayerKPIs.indexOf(sourceKPI);
              const targetIndexInLayer = targetLayerKPIs.indexOf(targetKPI);

              // Calculate positions based on layer
              const getColumnX = (layer: string) => {
                switch (layer) {
                  case "enterprise": return 280;
                  case "product": return 580; 
                  case "customer": return 880;
                  default: return 280;
                }
              };

              connections.push({
                from: { 
                  x: getColumnX(sourceKPI.layer), 
                  y: 150 + sourceIndexInLayer * 140, 
                  kpi: sourceKPI 
                },
                to: { 
                  x: getColumnX(targetKPI.layer), 
                  y: 150 + targetIndexInLayer * 140, 
                  kpi: targetKPI 
                },
                strength: Math.abs(output.coefficient),
                impact: output.impact
              });
            }
          });
        }
      });

      return connections;
    };

    const connections = buildCausalConnections();
    
    // Debug: log connections
    console.log('Enterprise KPIs:', enterpriseKPIs.map(k => ({ name: k.name, causalOutputs: k.causalOutputs })));
    console.log('Product KPIs:', productKPIs.map(k => ({ name: k.name, causalInputs: k.causalInputs, causalOutputs: k.causalOutputs })));
    console.log('Customer KPIs:', customerKPIs.map(k => ({ name: k.name, causalInputs: k.causalInputs })));
    console.log('Found connections:', connections);

    const KPINode = ({ kpi, onClick }: { kpi: MirrorKPIData; onClick: () => void }) => {
      const getStatusColor = (status: string) => {
        switch (status) {
          case "on-track": return "border-green-500 bg-green-50";
          case "at-risk": return "border-yellow-500 bg-yellow-50";
          case "off-track": return "border-red-500 bg-red-50";
          default: return "border-gray-300 bg-white";
        }
      };

      const getStatusBadge = (status: string) => {
        switch (status) {
          case "on-track": return "bg-green-500 text-white";
          case "at-risk": return "bg-yellow-500 text-white";
          case "off-track": return "bg-red-500 text-white";
          default: return "bg-gray-500 text-white";
        }
      };

      return (
        <div 
          className={`relative p-4 rounded-lg border-2 cursor-pointer hover:shadow-lg transition-all ${getStatusColor(kpi.status)} min-h-[120px] w-64`}
          onClick={onClick}
        >
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-sm text-gray-900 leading-tight">{kpi.name}</h4>
            <div className={`px-2 py-1 rounded text-xs font-medium ${getStatusBadge(kpi.status)}`}>
              {kpi.status.replace('-', ' ').toUpperCase()}
            </div>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-baseline gap-2">
              <span className="text-2xl font-bold text-gray-900">
                {kpi.currentValue}{kpi.unit}
              </span>
              {kpi.target && (
                <span className="text-xs text-gray-600">
                  Target: {kpi.target}{kpi.unit}
                </span>
              )}
            </div>
            
            {kpi.benchmark && (
              <div className="text-xs text-gray-600">
                Benchmark: {kpi.benchmark}{kpi.unit}
              </div>
            )}

            {(kpi.impactUSD && kpi.impactUSD > 0) ? (
              <div className="text-xs font-medium text-blue-600">
                Impact: ${(kpi.impactUSD / 1000000).toFixed(0)}M upside
              </div>
            ) : (kpi.advantageVsPeerUSD && kpi.advantageVsPeerUSD > 0) ? (
              <div className="text-xs font-medium text-green-600">
                Advantage vs peer: ${(kpi.advantageVsPeerUSD / 1000000).toFixed(0)}M
              </div>
            ) : (
              <div className="text-xs text-gray-600">
                {kpi.status === "on-track" ? "At/above target" : "On par with peers"}
              </div>
            )}
          </div>
        </div>
      );
    };

    return (
      <div className="relative min-h-[600px] bg-gradient-to-br from-gray-900 to-black p-8">
        {/* Flow Background SVG */}
        <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
          <defs>
            <linearGradient id="flowGradientHigh" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.8" />
              <stop offset="100%" stopColor="#1D4ED8" stopOpacity="0.6" />
            </linearGradient>
            <linearGradient id="flowGradientMedium" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#F59E0B" stopOpacity="0.7" />
              <stop offset="100%" stopColor="#D97706" stopOpacity="0.5" />
            </linearGradient>
            <linearGradient id="flowGradientLow" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="#6B7280" stopOpacity="0.5" />
              <stop offset="100%" stopColor="#4B5563" stopOpacity="0.3" />
            </linearGradient>
          </defs>
          
          {/* Dynamic flow paths based on actual causal relationships */}
          {connections.map((conn, index) => {
            const midX = (conn.from.x + conn.to.x) / 2;
            const gradientId = conn.impact === "high" ? "flowGradientHigh" : 
                             conn.impact === "medium" ? "flowGradientMedium" : "flowGradientLow";
            const strokeWidth = Math.max(8, conn.strength * 25);
            
            return (
              <path
                key={index}
                d={`M ${conn.from.x} ${conn.from.y} Q ${midX} ${conn.from.y} ${conn.to.x} ${conn.to.y}`}
                stroke={`url(#${gradientId})`}
                strokeWidth={strokeWidth}
                fill="none"
                opacity="0.7"
              />
            );
          })}
        </svg>

        {/* Column Headers */}
        <div className="relative z-10 grid grid-cols-3 gap-8 mb-8">
          <div className="text-center">
            <h2 className="text-xl font-bold text-white mb-2">Enterprise</h2>
          </div>
          <div className="text-center">
            <h2 className="text-xl font-bold text-white mb-2">Product & Service</h2>
          </div>
          <div className="text-center">
            <h2 className="text-xl font-bold text-white mb-2">Customer Journey</h2>
          </div>
        </div>

        {/* KPI Nodes arranged in flow */}
        <div className="relative z-10 grid grid-cols-3 gap-8">
          {/* Enterprise Column */}
          <div className="space-y-6 flex flex-col items-center">
            {enterpriseKPIs.slice(0, 4).map((kpi, index) => (
              <KPINode key={kpi.id} kpi={kpi} onClick={() => handleKPIClick(kpi)} />
            ))}
          </div>

          {/* Product Column */}
          <div className="space-y-6 flex flex-col items-center">
            {productKPIs.slice(0, 4).map((kpi, index) => (
              <KPINode key={kpi.id} kpi={kpi} onClick={() => handleKPIClick(kpi)} />
            ))}
          </div>

          {/* Customer Column */}
          <div className="space-y-6 flex flex-col items-center">
            {customerKPIs.slice(0, 4).map((kpi, index) => (
              <KPINode key={kpi.id} kpi={kpi} onClick={() => handleKPIClick(kpi)} />
            ))}
          </div>
        </div>

        {/* Operating Margin section at bottom */}
        {enterpriseKPIs.find(kpi => kpi.name.toLowerCase().includes('margin')) && (
          <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-10">
            <div className="bg-gray-800 border border-gray-600 rounded-lg p-4 text-center min-w-[200px]">
              <h3 className="text-white font-semibold mb-2">Operating Margin</h3>
              <div className="text-2xl font-bold text-white">53%</div>
              <div className="text-sm text-gray-300">Target 55% | Benchmark 54%</div>
              <div className="text-sm text-blue-400 mt-1">$310M Impact</div>
              <div className="text-xs text-gray-400 mt-1">65% of gap in SMB Payments</div>
              <div className="text-xs text-gray-400">Root cause: Onboarding (14 days vs. 5 days)</div>
            </div>
          </div>
        )}
      </div>
    );
  };

  const LayerSection = ({ 
    title, 
    layer, 
    kpis: layerKPIs 
  }: { 
    title: string; 
    layer: keyof typeof expandedLayers; 
    kpis: MirrorKPIData[] 
  }) => (
    <div className="space-y-4">
      <Collapsible 
        open={expandedLayers[layer]} 
        onOpenChange={() => toggleLayerExpansion(layer)}
      >
        <CollapsibleTrigger className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
          <h3 className="font-semibold text-gray-900">{title}</h3>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {layerKPIs.length} KPIs
            </Badge>
            <ChevronDown className={`w-4 h-4 transition-transform ${expandedLayers[layer] ? 'rotate-180' : ''}`} />
          </div>
        </CollapsibleTrigger>
        <CollapsibleContent className="space-y-3 pt-2">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {layerKPIs.map(kpi => (
              <MirrorKPICard
                key={kpi.id}
                kpi={kpi}
                mode={mode}
                onClick={handleKPIClick}
                onJoyceAsk={handleJoyceAsk}
                isSelected={selectedKPIs.includes(kpi.id)}
              />
            ))}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );

  return (
      <div className="flex h-full">
        {/* Main KPI Grid */}
        <div className={`flex-1 overflow-auto transition-all duration-300 ${expandedKPI ? 'mr-96' : ''}`}>
          {/* Header */}
          <div className="bg-white border-b border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Digital Mirror</h1>
                <p className="text-gray-600 mt-1">
                  Enterprise-wide KPI monitoring with causal linkages
                </p>
              </div>
              
              <div className="flex items-center gap-4">
                {/* ERA vs SEI Toggle */}
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Mode:</span>
                  <ToggleGroup 
                    type="single" 
                    value={mode} 
                    onValueChange={(value) => value && setMode(value as "era" | "sei")}
                    className="h-9"
                  >
                    <ToggleGroupItem value="era" className="text-sm px-4">
                      ERA
                    </ToggleGroupItem>
                    <ToggleGroupItem value="sei" className="text-sm px-4">
                      SEI
                    </ToggleGroupItem>
                  </ToggleGroup>
                </div>

                {/* Bulk Actions */}
                {selectedKPIs.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={handleBulkJoyceAsk}
                    >
                      Ask Joyce ({selectedKPIs.length})
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-1" />
                      Export
                    </Button>
                  </div>
                )}

                {/* View Toggle */}
                <ToggleGroup 
                  type="single" 
                  value={viewMode} 
                  onValueChange={(value) => value && setViewMode(value as "cards" | "graph" | "sankey")}
                  className="h-9"
                >
                  <ToggleGroupItem value="cards">
                    <Grid className="w-4 h-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem value="graph">
                    <BarChart3 className="w-4 h-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem value="sankey">
                    <GitBranch className="w-4 h-4" />
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            </div>
          </div>

          {viewMode === "cards" && (
            <>
              {/* Filter Toolbar */}
              <FilterToolbar 
                filters={filters}
                onFiltersChange={setFilters}
                activeFilterCount={activeFilterCount}
              />

              {/* KPI Layers with gradient connections background */}
              <div className="relative">
                {/* Background SVG for gradient connections */}
                <svg className="absolute inset-0 w-full h-full pointer-events-none" style={{ zIndex: 1 }}>
                  <defs>
                    <linearGradient id="cardFlowHigh" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#1D4ED8" stopOpacity="0.1" />
                    </linearGradient>
                    <linearGradient id="cardFlowMedium" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#F59E0B" stopOpacity="0.3" />
                      <stop offset="100%" stopColor="#D97706" stopOpacity="0.1" />
                    </linearGradient>
                    <linearGradient id="cardFlowLow" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="#6B7280" stopOpacity="0.2" />
                      <stop offset="100%" stopColor="#4B5563" stopOpacity="0.05" />
                    </linearGradient>
                  </defs>
                  
                  {/* Flowing connections between layers */}
                  <path
                    d="M 33% 25% Q 50% 20% 66% 30%"
                    stroke="url(#cardFlowHigh)"
                    strokeWidth="8"
                    fill="none"
                    vectorEffect="non-scaling-stroke"
                  />
                  <path
                    d="M 33% 45% Q 50% 40% 66% 50%"
                    stroke="url(#cardFlowMedium)"
                    strokeWidth="6"
                    fill="none"
                    vectorEffect="non-scaling-stroke"
                  />
                  <path
                    d="M 66% 35% Q 83% 30% 100% 40%"
                    stroke="url(#cardFlowHigh)"
                    strokeWidth="7"
                    fill="none"
                    vectorEffect="non-scaling-stroke"
                  />
                  <path
                    d="M 66% 55% Q 83% 50% 100% 60%"
                    stroke="url(#cardFlowMedium)"
                    strokeWidth="5"
                    fill="none"
                    vectorEffect="non-scaling-stroke"
                  />
                </svg>

                <div className="relative z-10 p-6 space-y-6">
                  <LayerSection 
                    title="Enterprise Layer" 
                    layer="enterprise" 
                    kpis={kpisByLayer.enterprise} 
                  />
                  <LayerSection 
                    title="Product/Service Layer" 
                    layer="product" 
                    kpis={kpisByLayer.product} 
                  />
                  <LayerSection 
                    title="Customer Journey Layer" 
                    layer="customer" 
                    kpis={kpisByLayer.customer} 
                  />
                </div>
              </div>
            </>
          )}

          {viewMode === "graph" && <FlowDiagramView />}
          
          {viewMode === "sankey" && (
            <div className="p-6">
              <SankeyKPIDiagram 
                kpis={filteredKPIs} 
                onKPIClick={handleKPIClick}
              />
            </div>
          )}
        </div>

        {/* Right Panel - KPI Details */}
        {expandedKPI && (
          <div className="fixed right-0 top-[73px] w-96 h-[calc(100vh-73px)] bg-white border-l border-gray-200 shadow-lg z-10 overflow-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-gray-900">KPI Details</h3>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setExpandedKPI(null)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">{expandedKPI.name}</h4>
                  <div className="text-3xl font-bold text-gray-900">
                    {expandedKPI.currentValue}{expandedKPI.unit}
                  </div>
                </div>

                {/* Historical Chart Placeholder */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">12-Month Trend</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {expandedKPI.trendData && expandedKPI.trendData.length > 0 ? (
                      <div className="h-40">
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart data={expandedKPI.trendData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="period" tick={{ fontSize: 10 }} />
                            <YAxis
                              width={40}
                              tick={{ fontSize: 10 }}
                              tickCount={5}
                              allowDecimals
                              domain={["dataMin - 0.5", "dataMax + 0.5"]}
                              tickFormatter={(v) =>
                                expandedKPI.unit === "%" ? `${v}%` : `${v}`
                              }
                            />
                            <RechartsTooltip
                              formatter={(value: any) =>
                                expandedKPI.unit === "%" ? `${value}%` : value
                              }
                            />
                            <Line
                              type="monotone"
                              dataKey="value"
                              stroke="#3B82F6"
                              strokeWidth={2}
                              dot={false}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      </div>
                    ) : (
                      <div className="h-32 bg-gray-50 rounded flex items-center justify-center text-gray-500 text-sm">
                        Historical chart visualization
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Peer Comparisons */}
                {expandedKPI.benchmark && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Peer Comparison</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Current</span>
                          <span className="font-medium">{expandedKPI.currentValue}{expandedKPI.unit}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Peer Average</span>
                          <span className="font-medium">{expandedKPI.benchmark}{expandedKPI.unit}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Delta</span>
                          <span className={`font-medium ${expandedKPI.benchmarkDelta > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {expandedKPI.benchmarkDelta > 0 ? '+' : ''}{expandedKPI.benchmarkDelta.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Causal Drivers */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Key Drivers</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {expandedKPI.drivers.map((driver, index) => (
                        <div key={index} className="text-sm text-gray-700">
                          • {driver}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Recommended Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Joyce Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {expandedKPI.joyceInsight && (
                        <p className="text-sm text-gray-700">{expandedKPI.joyceInsight}</p>
                      )}
                      <Button 
                        className="w-full"
                        onClick={() => handleJoyceAsk(expandedKPI)}
                      >
                        Get Detailed Analysis
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        )}
      </div>
  );
}
