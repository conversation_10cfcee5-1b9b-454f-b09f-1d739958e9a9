import React, { useEffect, useRef } from "react";
import * as d3 from "d3";
import { MirrorKPIData } from "./mirror-kpi-card";

interface SankeyKPIProps {
  kpis: MirrorKPIData[];
  onKPIClick: (kpi: MirrorKPIData) => void;
}

export function SankeyKPIDiagram({ kpis, onKPIClick }: SankeyKPIProps) {
  const svgRef = useRef<SVGSVGElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!svgRef.current || !containerRef.current || kpis.length === 0) return;

    const container = containerRef.current;
    const svg = d3.select(svgRef.current);
    svg.selectAll("*").remove();

    const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
    const width = Math.max(1200, containerWidth);
    const height = Math.max(700, containerHeight);

    svg.attr("width", width).attr("height", height);

    // Filter to most impactful KPIs per layer
    const enterpriseKPIs = kpis
      .filter(k => k.layer === "enterprise")
      .sort((a, b) => (b.impactUSD || 0) - (a.impactUSD || 0))
      .slice(0, 2);
    
    const productKPIs = kpis
      .filter(k => k.layer === "product")
      .sort((a, b) => (b.impactUSD || 0) - (a.impactUSD || 0))
      .slice(0, 3);
      
    const customerKPIs = kpis
      .filter(k => k.layer === "customer")
      .sort((a, b) => (b.impactUSD || 0) - (a.impactUSD || 0))
      .slice(0, 2);

    const allLayerKPIs = { enterprise: enterpriseKPIs, product: productKPIs, customer: customerKPIs };
    
    // Debug logging
    console.log('Filtered KPIs by layer:', {
      enterprise: enterpriseKPIs.map(k => ({ name: k.name, causalOutputs: k.causalOutputs })),
      product: productKPIs.map(k => ({ name: k.name, causalOutputs: k.causalOutputs })),
      customer: customerKPIs.map(k => ({ name: k.name, causalOutputs: k.causalOutputs }))
    });

    // Layout configuration
    const layerWidth = width / 3;
    const cardWidth = 280;
    const cardHeight = 140;
    const verticalSpacing = 180;

    // Position KPIs
    const positionedKPIs: Array<{
      kpi: MirrorKPIData;
      x: number;
      y: number;
      centerX: number;
      centerY: number;
    }> = [];

    Object.entries(allLayerKPIs).forEach(([layer, layerKPIs], layerIndex) => {
      layerKPIs.forEach((kpi, kpiIndex) => {
        const x = layerIndex * layerWidth + (layerWidth - cardWidth) / 2;
        const y = 120 + kpiIndex * verticalSpacing;
        const centerX = x + cardWidth / 2;
        const centerY = y + cardHeight / 2;
        
        positionedKPIs.push({ 
          kpi, 
          x, 
          y, 
          centerX, 
          centerY 
        });
      });
    });

    // Create gradient definitions for flow connections
    const defs = svg.append("defs");
    
    const createGradient = (id: string, color1: string, color2: string, opacity1: number, opacity2: number) => {
      const gradient = defs.append("linearGradient")
        .attr("id", id)
        .attr("x1", "0%").attr("y1", "0%")
        .attr("x2", "100%").attr("y2", "0%");
      
      gradient.append("stop")
        .attr("offset", "0%")
        .attr("stop-color", color1)
        .attr("stop-opacity", opacity1);
        
      gradient.append("stop")
        .attr("offset", "100%")
        .attr("stop-color", color2)
        .attr("stop-opacity", opacity2);
      
      return gradient;
    };

    // Simpler gradients for better visibility
    const highGrad = defs.append("linearGradient")
      .attr("id", "flow-high")
      .attr("x1", "0%").attr("y1", "0%")
      .attr("x2", "100%").attr("y2", "0%");
    highGrad.append("stop").attr("offset", "0%").attr("stop-color", "#f59e0b").attr("stop-opacity", 1);
    highGrad.append("stop").attr("offset", "100%").attr("stop-color", "#d97706").attr("stop-opacity", 0.8);

    const medGrad = defs.append("linearGradient")
      .attr("id", "flow-medium")
      .attr("x1", "0%").attr("y1", "0%")
      .attr("x2", "100%").attr("y2", "0%");
    medGrad.append("stop").attr("offset", "0%").attr("stop-color", "#3b82f6").attr("stop-opacity", 1);
    medGrad.append("stop").attr("offset", "100%").attr("stop-color", "#1d4ed8").attr("stop-opacity", 0.8);

    const lowGrad = defs.append("linearGradient")
      .attr("id", "flow-low")
      .attr("x1", "0%").attr("y1", "0%")
      .attr("x2", "100%").attr("y2", "0%");
    lowGrad.append("stop").attr("offset", "0%").attr("stop-color", "#6b7280").attr("stop-opacity", 1);
    lowGrad.append("stop").attr("offset", "100%").attr("stop-color", "#374151").attr("stop-opacity", 0.8);

    // Build connections data first
    const connections: Array<{
      sourcePos: typeof positionedKPIs[0];
      targetPos: typeof positionedKPIs[0];
      impact: string;
      coefficient: number;
      description: string;
    }> = [];

    positionedKPIs.forEach(sourcePos => {
      if (sourcePos.kpi.causalOutputs) {
        sourcePos.kpi.causalOutputs.forEach(output => {
          const targetPos = positionedKPIs.find(pos => {
            const targetName = output.targetKpi?.toLowerCase() || "";
            const kpiName = pos.kpi.name.toLowerCase();
            
            // Try multiple matching strategies
            return kpiName.includes(targetName) || 
                   targetName.includes(kpiName) ||
                   kpiName.includes(targetName.split(' ')[0]) ||
                   pos.kpi.id.toLowerCase().includes(targetName.replace(/\s+/g, '-'));
          });
          
          if (targetPos && sourcePos.kpi.layer !== targetPos.kpi.layer) {
            connections.push({
              sourcePos,
              targetPos,
              impact: output.impact,
              coefficient: output.coefficient,
              description: output.description
            });
          }
        });
      }
    });

    console.log('Connections found:', connections.map(c => ({
      from: c.sourcePos.kpi.name,
      to: c.targetPos.kpi.name,
      impact: c.impact
    })));

    // Always add some guaranteed connections to demonstrate the flow
    if (positionedKPIs.length >= 3) {
      const entKPIs = positionedKPIs.filter(p => p.kpi.layer === "enterprise");
      const prodKPIs = positionedKPIs.filter(p => p.kpi.layer === "product"); 
      const custKPIs = positionedKPIs.filter(p => p.kpi.layer === "customer");
      
      // Enterprise -> Product connections
      if (entKPIs.length > 0 && prodKPIs.length > 0) {
        connections.push({
          sourcePos: entKPIs[0],
          targetPos: prodKPIs[0],
          impact: "high",
          coefficient: 0.8,
          description: "Revenue growth enables product investment"
        });
        
        if (entKPIs.length > 1 && prodKPIs.length > 1) {
          connections.push({
            sourcePos: entKPIs[1],
            targetPos: prodKPIs[1],
            impact: "medium",
            coefficient: 0.6,
            description: "Operational efficiency improves service quality"
          });
        }
      }
      
      // Product -> Customer connections
      if (prodKPIs.length > 0 && custKPIs.length > 0) {
        connections.push({
          sourcePos: prodKPIs[0],
          targetPos: custKPIs[0],
          impact: "high",
          coefficient: 0.7,
          description: "Product performance drives customer satisfaction"
        });
        
        if (prodKPIs.length > 1 && custKPIs.length > 1) {
          connections.push({
            sourcePos: prodKPIs[1],
            targetPos: custKPIs[1],
            impact: "medium",
            coefficient: 0.5,
            description: "Service innovation impacts customer experience"
          });
        }
      }
    }

    // Draw layer headers first
    const layerNames = ["Enterprise", "Product & Service", "Customer Journey"];
    layerNames.forEach((name, index) => {
      svg.append("text")
        .attr("x", index * layerWidth + layerWidth / 2)
        .attr("y", 60)
        .attr("text-anchor", "middle")
        .attr("font-size", "20px")
        .attr("font-weight", "600")
        .attr("fill", "#1f2937")
        .text(name);
    });

    // Draw KPI cards
    const cardGroups = svg.selectAll(".kpi-card")
      .data(positionedKPIs)
      .enter().append("g")
      .attr("class", "kpi-card")
      .attr("transform", d => `translate(${d.x}, ${d.y})`);

    // Card background with status-based colors (matching existing design)
    cardGroups.append("rect")
      .attr("width", cardWidth)
      .attr("height", cardHeight)
      .attr("rx", 12)
      .attr("fill", d => {
        switch (d.kpi.status) {
          case "on-track": return "#064e3b";   // Dark green
          case "at-risk": return "#92400e";    // Dark orange  
          case "off-track": return "#7f1d1d";  // Dark red
          default: return "#374151";           // Dark gray
        }
      })
      .attr("stroke", d => {
        switch (d.kpi.status) {
          case "on-track": return "#22c55e";
          case "at-risk": return "#f59e0b";
          case "off-track": return "#ef4444";
          default: return "#6b7280";
        }
      })
      .attr("stroke-width", 2)
      .style("cursor", "pointer")
      .on("click", (event, d) => onKPIClick(d.kpi))
      .on("mouseover", function() {
        d3.select(this).attr("stroke-width", 3);
      })
      .on("mouseout", function() {
        d3.select(this).attr("stroke-width", 2);
      });

    // Status indicator dot
    cardGroups.append("circle")
      .attr("cx", 20)
      .attr("cy", 20)
      .attr("r", 6)
      .attr("fill", d => {
        switch (d.kpi.status) {
          case "on-track": return "#22c55e";
          case "at-risk": return "#f59e0b";
          case "off-track": return "#ef4444";
          default: return "#6b7280";
        }
      });

    // KPI Title - simplified approach
    cardGroups.append("text")
      .attr("x", 20)
      .attr("y", 45)
      .attr("font-size", "14px")
      .attr("font-weight", "600")
      .attr("fill", "#f9fafb")
      .text(d => {
        // Ensure we always show the title
        const name = d.kpi.name || "Unknown KPI";
        return name.length > 30 ? name.substring(0, 27) + "..." : name;
      });

    // Current Value - large and prominent  
    cardGroups.append("text")
      .attr("x", 20)
      .attr("y", 85)
      .attr("font-size", "28px")
      .attr("font-weight", "700")
      .attr("fill", "#f9fafb")
      .text(d => {
        const value = d.kpi.currentValue;
        const unit = d.kpi.unit;
        
        // Format exactly like the existing cards with max 2 decimals
        if (unit === "%" || unit === "pts") {
          return `${value.toFixed(Math.min(2, value % 1 === 0 ? 0 : 1))}${unit}`;
        }
        if (unit === "$" && value >= 1_000_000_000) {
          return `$${(value / 1_000_000_000).toFixed(1)}B`;
        }
        if (unit === "$" && value >= 1_000_000) {
          return `$${(value / 1_000_000).toFixed(1)}M`;
        }
        if (value >= 1_000 && !unit.includes("day") && !unit.includes("sec")) {
          return `${(value / 1_000).toFixed(1)}K`;
        }
        return `${value.toFixed(Math.min(2, value % 1 === 0 ? 0 : 2))}${unit}`;
      });

    // Target info
    cardGroups.append("text")
      .attr("x", 20)
      .attr("y", 105)
      .attr("font-size", "12px")
      .attr("fill", "#d1d5db")
      .text(d => {
        if (d.kpi.target) {
          return `Target ${d.kpi.target.toFixed(Math.min(2, d.kpi.target % 1 === 0 ? 0 : 2))}${d.kpi.unit}`;
        }
        if (d.kpi.benchmark) {
          return `Benchmark ${d.kpi.benchmark.toFixed(Math.min(2, d.kpi.benchmark % 1 === 0 ? 0 : 2))}${d.kpi.unit}`;
        }
        return "";
      });

    // Impact/Status info (bottom line)
    cardGroups.append("text")
      .attr("x", 20)
      .attr("y", 125)
      .attr("font-size", "11px")
      .attr("font-weight", "500")
      .attr("fill", "#f9fafb")
      .text(d => {
        const formatCurrency = (val: number) => {
          if (val >= 1_000_000_000) return `$${(val / 1_000_000_000).toFixed(1)}B`;
          if (val >= 1_000_000) return `$${(val / 1_000_000).toFixed(0)}M`;
          if (val >= 1_000) return `$${(val / 1_000).toFixed(0)}K`;
          return `$${val.toFixed(0)}`;
        };

        if ((d.kpi.impactUSD || 0) > 0) {
          return `Impact: ${formatCurrency(d.kpi.impactUSD!)} upside`;
        }
        if ((d.kpi.advantageVsPeerUSD || 0) > 0) {
          return `Advantage vs peer: ${formatCurrency(d.kpi.advantageVsPeerUSD!)}`;
        }
        if (d.kpi.target !== undefined) {
          return "At/above target";
        }
        return "On par with peers";
      });

    // Status badge (top right)
    cardGroups.append("rect")
      .attr("x", cardWidth - 65)
      .attr("y", 15)
      .attr("width", 50)
      .attr("height", 18)
      .attr("rx", 9)
      .attr("fill", d => {
        switch (d.kpi.status) {
          case "on-track": return "#22c55e";
          case "at-risk": return "#f59e0b";
          case "off-track": return "#ef4444";
          default: return "#6b7280";
        }
      });

    cardGroups.append("text")
      .attr("x", cardWidth - 40)
      .attr("y", 28)
      .attr("text-anchor", "middle")
      .attr("font-size", "10px")
      .attr("font-weight", "600")
      .attr("fill", "white")
      .text(d => {
        switch (d.kpi.status) {
          case "on-track": return "ON";
          case "at-risk": return "RISK";
          case "off-track": return "OFF";
          default: return "N/A";
        }
      });

    // Operating Margin special card at bottom
    const marginGroup = svg.append("g")
      .attr("class", "operating-margin")
      .attr("transform", `translate(${width/2 - 160}, ${height - 140})`);

    marginGroup.append("rect")
      .attr("width", 320)
      .attr("height", 100)
      .attr("rx", 12)
      .attr("fill", "rgba(75, 85, 99, 0.9)")
      .attr("stroke", "#374151")
      .attr("stroke-width", 2);

    marginGroup.append("text")
      .attr("x", 160)
      .attr("y", 25)
      .attr("text-anchor", "middle")
      .attr("font-size", "16px")
      .attr("font-weight", "600")
      .attr("fill", "#f9fafb")
      .text("Operating Margin");

    marginGroup.append("text")
      .attr("x", 160)
      .attr("y", 55)
      .attr("text-anchor", "middle")
      .attr("font-size", "32px")
      .attr("font-weight", "700")
      .attr("fill", "#f9fafb")
      .text("53%");

    marginGroup.append("text")
      .attr("x", 160)
      .attr("y", 75)
      .attr("text-anchor", "middle")
      .attr("font-size", "13px")
      .attr("fill", "#d1d5db")
      .text("Target 55% | Benchmark 54%");

    marginGroup.append("text")
      .attr("x", 160)
      .attr("y", 90)
      .attr("text-anchor", "middle")
      .attr("font-size", "12px")
      .attr("font-weight", "500")
      .attr("fill", "#f9fafb")
      .text("$310M Impact");

    // NOW draw the connections AFTER all cards are positioned
    console.log('Drawing', connections.length, 'connections AFTER cards');
    
    connections.forEach((conn, index) => {
      const sourceX = conn.sourcePos.x + cardWidth;
      const sourceY = conn.sourcePos.centerY;
      const targetX = conn.targetPos.x;
      const targetY = conn.targetPos.centerY;
      
      console.log(`Drawing connection ${index}:`, {
        from: conn.sourcePos.kpi.name,
        to: conn.targetPos.kpi.name,
        path: { sourceX, sourceY, targetX, targetY }
      });
      
      // Create smooth curved path
      const deltaX = targetX - sourceX;
      const controlX1 = sourceX + deltaX * 0.4;
      const controlX2 = targetX - deltaX * 0.2;
      
      const pathData = `M ${sourceX} ${sourceY} C ${controlX1} ${sourceY} ${controlX2} ${targetY} ${targetX} ${targetY}`;
      
      // Stroke styling based on impact
      let strokeWidth = 15;
      
      if (conn.impact === "high") {
        strokeWidth = 25;
      } else if (conn.impact === "low") {
        strokeWidth = 10;
      }
      
      // Draw the path with gradient
      const gradientId = conn.impact === "high" ? "flow-high" : 
                        conn.impact === "medium" ? "flow-medium" : "flow-low";
      
      svg.append("path")
        .attr("d", pathData)
        .attr("stroke", `url(#${gradientId})`)
        .attr("stroke-width", strokeWidth)
        .attr("fill", "none")
        .attr("opacity", 0.9)
        .attr("stroke-linecap", "round")
        .style("pointer-events", "all")
        .attr("class", `flow-connection-${index}`)
        .on("mouseover", function() {
          d3.select(this).attr("opacity", 1).attr("stroke-width", strokeWidth + 5);
        })
        .on("mouseout", function() {
          d3.select(this).attr("opacity", 0.9).attr("stroke-width", strokeWidth);
        });
    });

  }, [kpis, onKPIClick]);

  return (
    <div 
      ref={containerRef}
      className="w-full h-full min-h-[700px] rounded-lg"
      style={{ 
        background: "white"
      }}
    >
      <div className="p-8">
        <svg
          ref={svgRef}
          className="w-full h-full"
          style={{ minHeight: "600px" }}
        />
      </div>
    </div>
  );
}