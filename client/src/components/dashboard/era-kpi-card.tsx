import { Card, CardContent } from "@/ui/card";
import { TrendingUp, TrendingDown, AlertTriangle, Target, Eye, CheckCircle, MessageCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/ui/tooltip";
import { cn } from "@/lib/utils";
import { useJoyce } from "@/components/layout/main-layout";

function getValueColor(value: string, unit?: string): string {
  if (!unit || !value || value === "N/A") return "text-gray-900";
  
  // Extract max value from unit (e.g., "/ 5", "/ 100")
  const maxMatch = unit.match(/\/ (\d+(?:\.\d+)?)/); 
  if (!maxMatch) return "text-gray-900";
  
  const maxValue = parseFloat(maxMatch[1]);
  const currentValue = parseFloat(value.replace(/[^0-9.]/g, ''));
  
  if (isNaN(currentValue) || isNaN(maxValue) || maxValue === 0) {
    return "text-gray-900";
  }
  
  const percentage = (currentValue / maxValue) * 100;
  
  if (percentage >= 70) return "text-green-600";
  if (percentage >= 40) return "text-orange-500";
  return "text-red-600";
}

export type ERAKPIType = 
  | "enterprise-value"
  | "execution-alpha" 
  | "joy-score"
  | "strategic-velocity"
  | "investability-score"
  | "value-at-risk";

export type ContextualLabel = "strength" | "risk" | "opportunity" | "watchlist";

export interface ERAKPICardProps {
  type: ERAKPIType;
  title: string;
  value: string;
  unit?: string;
  contextualLabel: ContextualLabel;
  trend?: {
    direction: "up" | "down" | "flat";
    value?: string;
    period?: string;
  };
  benchmark?: {
    text: string;
    status?: "above" | "below" | "at";
  };
  fogScore?: {
    level: "high" | "medium" | "low";
    description: string;
  };
  definition: string;
  onClick?: () => void;
}

const contextualLabelConfig = {
  strength: {
    icon: CheckCircle,
    color: "text-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200",
    label: "Strength"
  },
  risk: {
    icon: AlertTriangle,
    color: "text-red-600", 
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
    label: "Risk"
  },
  opportunity: {
    icon: Target,
    color: "text-blue-600",
    bgColor: "bg-blue-50", 
    borderColor: "border-blue-200",
    label: "Opportunity"
  },
  watchlist: {
    icon: Eye,
    color: "text-gray-600",
    bgColor: "bg-gray-50",
    borderColor: "border-gray-200", 
    label: "Watchlist"
  }
};

const fogScoreConfig = {
  high: { color: "text-green-600", opacity: "opacity-100" },
  medium: { color: "text-yellow-600", opacity: "opacity-70" },
  low: { color: "text-red-600", opacity: "opacity-50" }
};

export function ERAKPICard({
  type,
  title,
  value,
  unit,
  contextualLabel,
  trend,
  benchmark,
  fogScore,
  definition,
  onClick
}: ERAKPICardProps) {
  const { askJoyce } = useJoyce();
  const labelConfig = contextualLabelConfig[contextualLabel];
  const LabelIcon = labelConfig.icon;
  
  const handleClick = () => {
    if (onClick) {
      onClick();
    }
  };

  const renderTrendIcon = () => {
    if (!trend) return null;
    
    if (trend.direction === "up") {
      return <TrendingUp className="w-3 h-3 text-green-600" />;
    } else if (trend.direction === "down") {
      return <TrendingDown className="w-3 h-3 text-red-600" />;
    }
    return <div className="w-3 h-3 rounded-full bg-gray-400" />;
  };

  const renderFogIndicator = () => {
    if (!fogScore) return null;
    
    const config = fogScoreConfig[fogScore.level];
    return (
      <div className={cn("w-2 h-2 rounded-full", config.opacity)} 
           style={{ backgroundColor: config.color.includes('green') ? '#10B981' : 
                                   config.color.includes('yellow') ? '#F59E0B' : '#EF4444' }} />
    );
  };

  const handleJoyceClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    const question = `Explain ${title} and what ${value}${unit || ""} means for our company.`;
    askJoyce(question);
  };

  const tooltipContent = (
    <div className="space-y-2 max-w-sm">
      <div className="font-medium">{title}</div>
      <div className="text-sm text-gray-300">{definition}</div>
      
      {trend && (
        <div className="flex items-center space-x-1 text-sm">
          {renderTrendIcon()}
          <span>
            {trend.value && `${trend.value} `}
            {trend.period && `vs ${trend.period}`}
          </span>
        </div>
      )}
      
      {benchmark && (
        <div className="text-sm">
          <span className="text-gray-400">Benchmark: </span>
          <span className={cn(
            benchmark.status === "above" ? "text-green-400" :
            benchmark.status === "below" ? "text-red-400" : "text-gray-300"
          )}>
            {benchmark.text}
          </span>
        </div>
      )}
      
      {fogScore && (
        <div className="flex items-center space-x-2 text-sm">
          {renderFogIndicator()}
          <span className="text-gray-400">
            Data Confidence: <span className="text-gray-300">{fogScore.level}</span>
          </span>
        </div>
      )}

      <div className="border-t border-gray-600 pt-2">
        <button
          onClick={handleJoyceClick}
          className="flex items-center space-x-1 text-xs text-blue-400 hover:text-blue-300 transition-colors"
        >
          <MessageCircle className="w-3 h-3" />
          <span>Ask Joyce</span>
        </button>
      </div>
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card 
            className={cn(
              "transition-all duration-200 hover:shadow-md cursor-pointer",
              labelConfig.borderColor,
              onClick && "hover:scale-[1.02]"
            )}
            onClick={handleClick}
          >
            <CardContent className="pt-4 pb-4">
              {/* Header with contextual label */}
              <div className="flex items-center justify-between mb-3">
                <div className={cn(
                  "flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium",
                  labelConfig.bgColor,
                  labelConfig.color
                )}>
                  <LabelIcon className="w-3 h-3" />
                  <span>{labelConfig.label}</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  {trend && renderTrendIcon()}
                  {fogScore && renderFogIndicator()}
                </div>
              </div>

              {/* Title */}
              <div className="text-sm text-gray-600 mb-2 font-medium">
                {title}
              </div>

              {/* Value */}
              <div className="text-3xl font-bold mb-2">
                <span className={getValueColor(value, unit)}>{value}</span>
                {unit && <span className="text-lg font-normal text-gray-600 ml-1">{unit}</span>}
              </div>

              {/* Benchmark or trend info */}
              {benchmark && (
                <div className="text-xs text-gray-500">
                  {benchmark.text}
                </div>
              )}
            </CardContent>
          </Card>
        </TooltipTrigger>
        <TooltipContent side="top" className="bg-gray-900 text-white border-gray-700">
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}