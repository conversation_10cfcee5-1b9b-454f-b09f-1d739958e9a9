import { Card, CardContent } from "@/ui/card";
import { MessageCircle, Sparkles } from "lucide-react";
import { use<PERSON><PERSON><PERSON> } from "@/components/layout/main-layout";

interface JoyceSummaryProps {
  companyName: string;
  summary: string;
  highlights: Array<{
    text: string;
    type: "positive" | "negative" | "neutral";
  }>;
  suggestedQuestions?: string[];
  onQuestionClick?: (question: string) => void;
}

export function JoyceSummary({ 
  companyName,
  summary, 
  highlights, 
  suggestedQuestions = [],
  onQuestionClick 
}: JoyceSummaryProps) {
  const { askJoyce } = useJoyce();
  const getHighlightColor = (type: "positive" | "negative" | "neutral") => {
    switch (type) {
      case "positive": return "text-green-700";
      case "negative": return "text-red-700";
      default: return "text-gray-700";
    }
  };

  return (
    <Card className="mb-8 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
      <CardContent className="pt-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
          </div>
          
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-3">
              <h3 className="text-lg font-semibold text-gray-900">Since You've Been Gone</h3>
              <span className="text-sm text-gray-500">— Joyce</span>
            </div>
            
            <div className="text-gray-700 mb-4 leading-relaxed">
              {summary}
            </div>
            
            {highlights.length > 0 && (
              <div className="space-y-2 mb-4">
                {highlights.map((highlight, index) => (
                  <div key={index} className={cn("text-sm", getHighlightColor(highlight.type))}>
                    • {highlight.text}
                  </div>
                ))}
              </div>
            )}
            
            {suggestedQuestions.length > 0 && (
              <div className="border-t border-blue-200 pt-4">
                <div className="flex items-center space-x-2 mb-3">
                  <MessageCircle className="w-4 h-4 text-blue-600" />
                  <span className="text-sm font-medium text-gray-700">Ask Joyce:</span>
                </div>
                <div className="flex flex-wrap gap-2">
                  {suggestedQuestions.slice(0, 3).map((question, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        askJoyce(question);
                        onQuestionClick?.(question);
                      }}
                      className="text-xs px-3 py-1 bg-white border border-blue-200 rounded-full hover:bg-blue-50 hover:border-blue-300 transition-colors text-blue-700"
                    >
                      {question}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

function cn(...classes: string[]) {
  return classes.filter(Boolean).join(' ');
}