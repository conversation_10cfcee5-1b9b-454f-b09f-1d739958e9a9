import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";

interface ChartDataPoint {
  period: string;
  [key: string]: any;
}

interface PerformanceChartUnifiedProps {
  title: string;
  data: ChartDataPoint[];
  type?: "line" | "bar";
  dataKey: string;
  color?: string;
  height?: number;
}

export function PerformanceChartUnified({ 
  title, 
  data, 
  type = "line", 
  dataKey, 
  color = "#3B82F6",
  height = 300 
}: PerformanceChartUnifiedProps) {
  // Generate chart config for the data key
  const config = generateChartConfig(
    data,
    [dataKey],
    { [dataKey]: title.replace(" Trend", "") },
    { [dataKey]: color }
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <DataVisualization
          type={type}
          data={data}
          config={config}
          xKey="period"
          yKey={dataKey}
          height={height}
          tooltip={{ 
            enabled: true,
            formatter: (value: any, name: string) => {
              // Format based on data type
              if (name.toLowerCase().includes('rate')) {
                return [`${value}%`, name];
              }
              if (name.toLowerCase().includes('revenue') || name.toLowerCase().includes('cash')) {
                return [`$${value}B`, name];
              }
              if (name.toLowerCase().includes('count') || name.toLowerCase().includes('customer')) {
                return [`${value}K`, name];
              }
              return [value.toLocaleString(), name];
            }
          }}
        />
      </CardContent>
    </Card>
  );
}