import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>3, Clock, TrendingUp, Target, Sliders, Calculator, Award, AlertTriangle, CheckCircle } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";
import { Slider } from "@/ui/slider";
import { Label } from "@/ui/label";

interface AgilitySimulatorProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface AgilityData {
  companySymbol: string;
  companyName: string;
  agilityOverview: any;
  scenarioSimulations: any[];
  agilityFactors: any;
  interactiveSimulator: any;
  casestudyComparisons: any[];
  joyceAgentPrompts: any;
}

export function AgilitySimulator({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: AgilitySimulatorProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<AgilityData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [simulatorParams, setSimulatorParams] = useState<{[key: string]: number}>({});
  const [simulationResults, setSimulationResults] = useState<any>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/agility-simulator.json`);
        const jsonData = await response.json();
        setData(jsonData);
        
        // Initialize simulator parameters with current values
        if (jsonData.interactiveSimulator?.parameters) {
          const initialParams: {[key: string]: number} = {};
          jsonData.interactiveSimulator.parameters.forEach((param: any) => {
            initialParams[param.parameter] = param.currentValue;
          });
          setSimulatorParams(initialParams);
        }
      } catch (error) {
        console.error('Error loading agility data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  useEffect(() => {
    // Run simulation when parameters change
    if (data && Object.keys(simulatorParams).length > 0) {
      runSimulation();
    }
  }, [simulatorParams, data]);

  const runSimulation = () => {
    if (!data) return;
    
    // Calculate impact based on parameter changes
    const baseline = {
      responseTime: 10, // months
      decisionSpeed: 2.5,
      coordinationSpeed: 3.0,
      executionSpeed: 3.2,
      innovationSpeed: 2.8
    };

    const approvalReduction = (8 - simulatorParams['Approval Layers']) / 8;
    const collaborationIncrease = (simulatorParams['Cross-functional Collaboration'] - 40) / 100;
    const automationIncrease = (simulatorParams['AI/Automation Level'] - 35) / 100;
    const riskToleranceIncrease = (simulatorParams['Risk Tolerance'] - 30) / 100;

    const newResponseTime = baseline.responseTime * (1 - (approvalReduction * 0.2 + collaborationIncrease * 0.15 + automationIncrease * 0.25 + riskToleranceIncrease * 0.1));
    const improvementPercent = ((baseline.responseTime - newResponseTime) / baseline.responseTime) * 100;
    const financialImpact = improvementPercent * 50; // $50M per 1% improvement

    setSimulationResults({
      newResponseTime: Math.max(newResponseTime, 3), // Minimum 3 months
      improvementPercent: Math.max(improvementPercent, 0),
      financialImpact: Math.max(financialImpact, 0),
      newAgilityScore: Math.min(data.agilityOverview.currentAgilityScore + (improvementPercent * 0.02), 5)
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading agility simulator...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Zap className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load agility simulator data</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'slow': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderAgilityOverview = () => (
    <div className="space-y-6">
      {/* Current Agility Score */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-orange-600" />
              <span>Current Agility Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">{data.agilityOverview.currentAgilityScore}</div>
                <div className="text-lg font-semibold">{data.agilityOverview.agilityLevel}</div>
                <p className="text-sm text-gray-600 mt-2">{data.agilityOverview.description}</p>
              </div>
              <Separator />
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Avg Response Time:</span>
                  <p className="text-gray-600">{data.agilityOverview.averageResponseTime}</p>
                </div>
                <div>
                  <span className="font-medium">Industry Leader:</span>
                  <p className="text-gray-600">{data.agilityOverview.industryLeaderResponseTime}</p>
                </div>
                <div>
                  <span className="font-medium">Percentile Rank:</span>
                  <p className="text-gray-600">{data.agilityOverview.percentileRank}th</p>
                </div>
                <div>
                  <span className="font-medium">Gap to Leader:</span>
                  <p className="text-red-600 font-medium">+4 months</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              <span>Agility Factor Breakdown</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(data.agilityFactors).map(([key, factor]: [string, any]) => (
              <div key={key} className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="font-medium capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</span>
                  <span className="font-bold">{factor.currentScore}/5</span>
                </div>
                <Progress value={(factor.currentScore / 5) * 100} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderScenarioAnalysis = () => (
    <div className="space-y-6">
      {/* Scenario Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {data.scenarioSimulations.map((scenario: any, index: number) => (
          <Card 
            key={index}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedScenario === scenario.scenarioId ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedScenario(selectedScenario === scenario.scenarioId ? null : scenario.scenarioId)}
          >
            <CardHeader>
              <CardTitle className="text-lg">{scenario.title}</CardTitle>
              <Badge variant="outline">{scenario.category}</Badge>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">{scenario.description}</p>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Response Time:</span>
                  <span className="font-bold">{scenario.actualPerformance.responseTime}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Outcome:</span>
                  <CheckCircle className="w-4 h-4 text-green-500" />
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Financial Impact:</span>
                  <span className="font-bold text-blue-600">{scenario.actualPerformance.financialImpact}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Scenario Detail */}
      {selectedScenario && (
        <Card>
          <CardHeader>
            <CardTitle>Scenario Deep Dive: {data.scenarioSimulations.find((s: any) => s.scenarioId === selectedScenario)?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const scenario = data.scenarioSimulations.find((s: any) => s.scenarioId === selectedScenario);
              return (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Actual Performance */}
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center space-x-2">
                        <Clock className="w-4 h-4" />
                        <span>Actual Performance</span>
                      </h4>
                      <div className="space-y-3">
                        {scenario?.actualPerformance.phases.map((phase: any, i: number) => (
                          <div key={i} className="p-3 bg-gray-50 rounded-lg">
                            <div className="flex justify-between items-center mb-1">
                              <span className="font-medium">{phase.phase}</span>
                              <Badge className={getStatusColor(phase.status)}>
                                {phase.duration}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600">{phase.description}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Benchmark Comparison */}
                    <div>
                      <h4 className="font-semibold mb-3 flex items-center space-x-2">
                        <Target className="w-4 h-4" />
                        <span>Industry Benchmark</span>
                      </h4>
                      <div className="space-y-3">
                        <div className="p-3 bg-green-50 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium">Industry Leader</span>
                            <Badge className="bg-green-100 text-green-800">
                              {scenario?.benchmarkPerformance.industryLeader.responseTime}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">
                            <strong>{scenario?.benchmarkPerformance.industryLeader.company}:</strong> {scenario?.benchmarkPerformance.industryLeader.approach}
                          </p>
                          <div className="text-sm">
                            <span className="font-medium">Industry Average: </span>
                            <span className="text-gray-600">{scenario?.benchmarkPerformance.industryAverage}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Improvement Simulation */}
                  <div>
                    <h4 className="font-semibold mb-3 flex items-center space-x-2">
                      <TrendingUp className="w-4 h-4" />
                      <span>Improvement Potential</span>
                    </h4>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">{scenario?.improvementSimulation.targetResponseTime}</div>
                          <div className="text-sm text-gray-600">Target Response Time</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{scenario?.improvementSimulation.potentialOutcomes.timeReduction}</div>
                          <div className="text-sm text-gray-600">Time Reduction</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{scenario?.improvementSimulation.potentialOutcomes.financialSaving}</div>
                          <div className="text-sm text-gray-600">Financial Saving</div>
                        </div>
                      </div>
                      <div>
                        <span className="font-medium">Key Improvements:</span>
                        <ul className="text-sm text-gray-700 mt-2 space-y-1">
                          {scenario?.improvementSimulation.keyImprovements.map((improvement: string, i: number) => (
                            <li key={i} className="flex items-start space-x-1">
                              <span>•</span>
                              <span>{improvement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderInteractiveSimulator = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Parameter Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Sliders className="w-5 h-5" />
              <span>Agility Parameters</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {data.interactiveSimulator.parameters.map((param: any, index: number) => (
              <div key={index} className="space-y-3">
                <Label className="font-medium">{param.parameter}</Label>
                <div className="px-2">
                  <Slider
                    value={[simulatorParams[param.parameter] || param.currentValue]}
                    onValueChange={(value) => {
                      setSimulatorParams(prev => ({
                        ...prev,
                        [param.parameter]: value[0]
                      }));
                    }}
                    min={param.range[0]}
                    max={param.range[1]}
                    step={1}
                    className="w-full"
                  />
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-500">{param.range[0]}</span>
                  <span className="font-bold text-blue-600">
                    {simulatorParams[param.parameter] || param.currentValue}
                  </span>
                  <span className="text-gray-500">{param.range[1]}</span>
                </div>
                <p className="text-xs text-gray-600">{param.description}</p>
              </div>
            ))}
            <Button 
              onClick={runSimulation} 
              className="w-full"
              disabled={!simulatorParams || Object.keys(simulatorParams).length === 0}
            >
              <Play className="w-4 h-4 mr-2" />
              Run Simulation
            </Button>
          </CardContent>
        </Card>

        {/* Simulation Results */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calculator className="w-5 h-5" />
              <span>Simulation Results</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {simulationResults ? (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">
                      {simulationResults.newResponseTime.toFixed(1)}m
                    </div>
                    <div className="text-sm text-gray-600">New Response Time</div>
                    <div className="text-xs text-green-600 font-medium">
                      -{simulationResults.improvementPercent.toFixed(1)}% improvement
                    </div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {simulationResults.newAgilityScore.toFixed(1)}
                    </div>
                    <div className="text-sm text-gray-600">New Agility Score</div>
                    <div className="text-xs text-green-600 font-medium">
                      +{(simulationResults.newAgilityScore - data.agilityOverview.currentAgilityScore).toFixed(1)} improvement
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-yellow-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      ${simulationResults.financialImpact.toFixed(0)}M
                    </div>
                    <div className="text-sm text-gray-600">Annual Financial Impact</div>
                    <p className="text-xs text-gray-500 mt-2">
                      Based on faster decision-making and execution cycles
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">Key Improvements:</h4>
                  {simulationResults.improvementPercent > 20 && (
                    <div className="flex items-center space-x-2 text-sm text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span>Significant agility improvement achieved</span>
                    </div>
                  )}
                  {simulationResults.newResponseTime <= 6 && (
                    <div className="flex items-center space-x-2 text-sm text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span>Matches industry leader response times</span>
                    </div>
                  )}
                  {simulationResults.financialImpact > 500 && (
                    <div className="flex items-center space-x-2 text-sm text-green-600">
                      <CheckCircle className="w-4 h-4" />
                      <span>High ROI transformation opportunity</span>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                <Calculator className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <p>Adjust parameters and run simulation to see results</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderBestPractices = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {data.casestudyComparisons.map((company: any, index: number) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{company.company}</span>
                <Badge className="bg-green-100 text-green-800">
                  {company.agilityScore}/5
                </Badge>
              </CardTitle>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{company.responseTime}</span>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Key Practices:</h4>
                  <ul className="text-sm text-gray-600 space-y-1">
                    {company.keyPractices.map((practice: string, i: number) => (
                      <li key={i} className="flex items-start space-x-1">
                        <span className="text-blue-500">•</span>
                        <span>{practice}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <Separator />
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Applicability to {selectedCompany?.toUpperCase()}:</span>
                  <Badge className={company.applicability?.includes('High') ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                    {company.applicability?.split(' - ')[0] || 'N/A'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <Zap className="w-6 h-6 text-orange-600" />
            <h1 className="text-2xl font-bold text-gray-900">Agility Simulator</h1>
          </div>
          <p className="text-gray-600">
            Interactive tool to simulate {data.companyName}'s organizational agility improvements and their impact
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Agility Overview</TabsTrigger>
            <TabsTrigger value="scenarios">Scenario Analysis</TabsTrigger>
            <TabsTrigger value="simulator">Interactive Simulator</TabsTrigger>
            <TabsTrigger value="practices">Best Practices</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderAgilityOverview()}
          </TabsContent>

          <TabsContent value="scenarios">
            {renderScenarioAnalysis()}
          </TabsContent>

          <TabsContent value="simulator">
            {renderInteractiveSimulator()}
          </TabsContent>

          <TabsContent value="practices">
            {renderBestPractices()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Execution Maturity (Joy Score) Deep Dive
        </Button>
        <Button onClick={onNextSection}>
          Next: Value Leakage & Financial Impact
        </Button>
      </div>
    </div>
  );
}