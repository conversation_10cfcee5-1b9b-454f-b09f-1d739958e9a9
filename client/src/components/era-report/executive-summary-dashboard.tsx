import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Button } from "@/ui/button";
import { Progress } from "@/ui/progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/ui/tabs";
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  Target, 
  DollarSign,
  Activity,
  Users,
  Zap,
  Brain,
  ChevronRight,
  Info,
  HelpCircle,
  ChevronDown,
  ChevronUp,
  Clock,
  TrendingDown
} from "lucide-react";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { useJoyce } from "@/components/layout/main-layout";

interface ExecutiveSummaryDashboardProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

export function ExecutiveSummaryDashboard({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: ExecutiveSummaryDashboardProps) {
  const { selectedCompany } = useCompany();
  const { data, isLoading, error } = useCompanyData(selectedCompany, "era-report/executive-summary-dashboard");
  const { openJoyce } = useJoyce();
  const [selectedValueCategory, setSelectedValueCategory] = useState<string | null>(null);
  const [detailedLeakageData, setDetailedLeakageData] = useState<any>(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  useEffect(() => {
    const loadDetailedData = async () => {
      if (!selectedValueCategory) {
        setDetailedLeakageData(null);
        return;
      }
      
      setLoadingDetails(true);
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/value-leakage-financial-impact.json`);
        const detailedData = await response.json();
        const categoryData = detailedData.leakageCategories.find(
          (cat: any) => cat.category === selectedValueCategory
        );
        setDetailedLeakageData(categoryData);
      } catch (error) {
        console.error('Error loading detailed leakage data:', error);
      } finally {
        setLoadingDetails(false);
      }
    };

    loadDetailedData();
  }, [selectedValueCategory, selectedCompany]);

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="w-16 h-16 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Data Loading Error</h2>
          <p className="text-gray-600">Unable to load ERA dashboard data</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressColor = (status: string) => {
    switch (status) {
      case 'positive': return 'bg-green-500';
      case 'warning': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      default: return 'bg-blue-500';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Executive Summary Dashboard</h1>
            <p className="text-lg text-gray-600 mt-2">
              Enterprise Readiness Assessment for {data.companyName}
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => openJoyce()}
              className="flex items-center space-x-2"
            >
              <Brain className="w-4 h-4" />
              <span>Ask Joyce</span>
            </Button>
            <Badge variant="secondary">Last Updated: {new Date(data.lastUpdated).toLocaleDateString()}</Badge>
          </div>
        </div>


        {/* Readiness Snapshot */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Joy Score */}
          <Card className="relative">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <Activity className="w-5 h-5 text-blue-600" />
                <span>Joy Score</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-600 mb-2">
                  {data.readinessSnapshot.joyScore.value}
                </div>
                <div className="text-sm text-gray-600 mb-3">
                  {data.readinessSnapshot.joyScore.level}
                </div>
                <Progress value={(data.readinessSnapshot.joyScore.value / 5) * 100} className="h-2 mb-2" />
                <div className="text-xs text-gray-500">
                  Industry Avg: {data.readinessSnapshot.joyScore.industryAverage} | Leaders: {data.readinessSnapshot.joyScore.industryLeaders}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Agility Level */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <Zap className="w-5 h-5 text-yellow-600" />
                <span>Agility Level</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600 mb-2">
                  {data.readinessSnapshot.agilityLevel.value}
                </div>
                <Badge className={getStatusColor(data.readinessSnapshot.agilityLevel.status)}>
                  Score: {data.readinessSnapshot.agilityLevel.score}/5
                </Badge>
                <div className="text-sm text-gray-600 mt-2">
                  Response Time: {data.readinessSnapshot.agilityLevel.responseTime}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Overall Readiness */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <Target className="w-5 h-5 text-green-600" />
                <span>Overall Readiness</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {data.readinessSnapshot.overallReadiness.value}
                </div>
                <p className="text-sm text-gray-600">
                  {data.readinessSnapshot.overallReadiness.description}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Value Leakage */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center space-x-2 text-lg">
                <DollarSign className="w-5 h-5 text-red-600" />
                <span>Value Leakage</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center">
                <div className="text-3xl font-bold text-red-600 mb-2">
                  {data.valueLeakageOverview.totalLeakage.value}
                </div>
                <Badge className={getStatusColor(data.valueLeakageOverview.totalLeakage.status)}>
                  {data.valueLeakageOverview.totalLeakage.percentage} of Revenue
                </Badge>
                <p className="text-sm text-gray-600 mt-2">
                  Annual EBITDA Impact
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Value Leakage Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertTriangle className="w-5 h-5 text-red-600" />
              <span>Value Leakage Breakdown</span>
            </CardTitle>
            <p className="text-sm text-gray-600">Click categories to see detailed analysis</p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {data.valueLeakageOverview.breakdown.map((item: any, idx: number) => (
                <Card 
                  key={idx} 
                  className={`cursor-pointer transition-all duration-300 hover:shadow-md ${
                    selectedValueCategory === item.category 
                      ? 'ring-2 ring-blue-500 md:col-span-2 lg:col-span-4' 
                      : selectedValueCategory ? 'opacity-60' : ''
                  }`}
                  onClick={() => setSelectedValueCategory(
                    selectedValueCategory === item.category ? null : item.category
                  )}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-medium text-sm">{item.category}</h4>
                        {selectedValueCategory === item.category ? (
                          <ChevronUp className="w-4 h-4 text-blue-500" />
                        ) : (
                          <ChevronDown className="w-4 h-4 text-gray-400" />
                        )}
                      </div>
                      <Badge className={getStatusColor(item.status)} variant="secondary">
                        {item.priority}
                      </Badge>
                    </div>
                    <div className="text-2xl font-bold text-red-600 mb-2">
                      {item.amount}
                    </div>
                    <div className="text-sm text-gray-600 mb-3">
                      {item.description}
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full" 
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {item.percentage}% of total leakage
                    </div>

                    {/* Expanded Details */}
                    {selectedValueCategory === item.category && (
                      <div className="mt-6 pt-4 border-t border-gray-200 transition-all duration-300">
                        {loadingDetails ? (
                          <div className="flex items-center justify-center py-8">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            <span className="ml-2 text-sm text-gray-600">Loading detailed analysis...</span>
                          </div>
                        ) : detailedLeakageData ? (
                          <div className="space-y-6">
                            {/* Recovery Timeline */}
                            <div className="flex items-center space-x-4 p-3 bg-blue-50 rounded-lg">
                              <Clock className="w-5 h-5 text-blue-600" />
                              <div>
                                <div className="font-medium text-sm">Recovery Timeline</div>
                                <div className="text-sm text-gray-600">{detailedLeakageData.timeframe}</div>
                              </div>
                              <div className="ml-auto">
                                <Badge variant="outline" className="text-green-600 border-green-600">
                                  {detailedLeakageData.recoverability} recoverability
                                </Badge>
                              </div>
                            </div>

                            {/* Root Causes */}
                            <div>
                              <h5 className="font-medium text-sm mb-3 flex items-center">
                                <TrendingDown className="w-4 h-4 text-red-500 mr-2" />
                                Root Causes
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {detailedLeakageData.details?.rootCauses?.slice(0, 4).map((cause: string, causeIdx: number) => (
                                  <div key={causeIdx} className="flex items-start space-x-2 text-sm">
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-gray-700">{cause}</span>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Specific Leakages */}
                            <div>
                              <h5 className="font-medium text-sm mb-3 flex items-center">
                                <DollarSign className="w-4 h-4 text-red-500 mr-2" />
                                Breakdown by Area
                              </h5>
                              <div className="space-y-2">
                                {detailedLeakageData.details?.specificLeakages?.slice(0, 3).map((leakage: any, leakageIdx: number) => (
                                  <div key={leakageIdx} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <div className="flex-1">
                                      <div className="font-medium text-sm">{leakage.area}</div>
                                      <div className="text-xs text-gray-600">{leakage.description}</div>
                                    </div>
                                    <div className="text-right ml-4">
                                      <div className="font-bold text-red-600">{leakage.amount}</div>
                                      <div className="text-xs text-gray-500">{leakage.progress}% progress</div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Recovery Actions */}
                            <div>
                              <h5 className="font-medium text-sm mb-3 flex items-center">
                                <Target className="w-4 h-4 text-green-600 mr-2" />
                                Key Recovery Actions
                              </h5>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                                {detailedLeakageData.details?.recoveryPath?.slice(0, 4).map((action: string, actionIdx: number) => (
                                  <div key={actionIdx} className="flex items-start space-x-2 text-sm">
                                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                                    <span className="text-gray-700">{action}</span>
                                  </div>
                                ))}
                              </div>
                            </div>

                            {/* Financial Projections */}
                            {detailedLeakageData.financialProjections && (
                              <div>
                                <h5 className="font-medium text-sm mb-3 flex items-center">
                                  <TrendingUp className="w-4 h-4 text-green-600 mr-2" />
                                  Recovery Projections
                                </h5>
                                <div className="grid grid-cols-3 gap-4">
                                  <div className="text-center p-2 bg-green-50 rounded">
                                    <div className="text-sm text-gray-600">Year 1</div>
                                    <div className="font-bold text-green-600">{detailedLeakageData.financialProjections.year1Recovery}</div>
                                  </div>
                                  <div className="text-center p-2 bg-green-50 rounded">
                                    <div className="text-sm text-gray-600">Year 2</div>
                                    <div className="font-bold text-green-600">{detailedLeakageData.financialProjections.year2Recovery}</div>
                                  </div>
                                  <div className="text-center p-2 bg-green-50 rounded">
                                    <div className="text-sm text-gray-600">Year 3</div>
                                    <div className="font-bold text-green-600">{detailedLeakageData.financialProjections.year3Recovery}</div>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="text-center py-4 text-gray-500">
                            <Info className="w-8 h-8 mx-auto mb-2" />
                            <p className="text-sm">Detailed analysis not available for this category</p>
                          </div>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Executive KPIs */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              <span>Executive KPIs</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {data.executiveKPIs.map((kpi: any, idx: number) => (
                <div key={idx} className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium">{kpi.metric}</h4>
                    <Badge className={getStatusColor(kpi.status)} variant="secondary">
                      {kpi.trend}
                    </Badge>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Current</span>
                      <span className="font-semibold">{kpi.current}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Target</span>
                      <span className="text-green-600">{kpi.target}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Benchmark</span>
                      <span className="text-blue-600">{kpi.benchmark}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Strategic Priorities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-600" />
              <span>Strategic Priorities</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.strategicPriorities.map((priority: any, idx: number) => (
                <div key={idx} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-lg">{priority.priority}</h4>
                      <p className="text-sm text-gray-600 mt-1">{priority.description}</p>
                    </div>
                    <Badge className={getStatusColor(priority.status)} variant="secondary">
                      {priority.status}
                    </Badge>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <div>
                      <div className="text-sm text-gray-500">Timeline</div>
                      <div className="font-medium">{priority.timeline}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Impact</div>
                      <div className="font-medium">{priority.impact}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Progress</div>
                      <div className="flex items-center space-x-2">
                        <Progress value={priority.progress} className="flex-1 h-2" />
                        <span className="text-sm font-medium">{priority.progress}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200 sticky bottom-0">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
          disabled={true}
        >
          Previous
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next: Company Context & Benchmark Explorer</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}