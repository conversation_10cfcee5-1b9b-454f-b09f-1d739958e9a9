import { ComingSoon } from "@/components/ui/coming-soon";
import { MessageCircle, GitBranch, Lightbulb } from "lucide-react";
import { Button } from "@/ui/button";

interface InteractiveQAScenarioPlannerProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

export function InteractiveQAScenarioPlanner({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: InteractiveQAScenarioPlannerProps) {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        <ComingSoon
          title="Interactive Q&A and Scenario Planner"
          description="Engage with an intelligent Q&A system and scenario planning tools to explore 'what-if' situations and get contextual insights for your transformation journey."
          icon={<MessageCircle className="w-10 h-10" />}
          features={[
            "AI-powered Q&A system for contextual insights",
            "Interactive scenario planning and modeling",
            "What-if analysis with multiple variable inputs",
            "Decision tree visualization and exploration",
            "Collaborative scenario sharing and discussion"
          ]}
          estimatedDate="Q3 2024"
          showBackButton={false}
        />
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Recommendations & Roadmap
        </Button>
        <Button 
          onClick={onNextSection}
          disabled={true}
        >
          Next
        </Button>
      </div>
    </div>
  );
}