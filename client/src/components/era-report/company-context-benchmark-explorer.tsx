import { useState, useEffect } from "react";
import { Building, Target, Layers, TrendingUp, Users, Award, BarChart3, <PERSON><PERSON><PERSON>, Radar } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { But<PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";

interface CompanyContextBenchmarkExplorerProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface CompanyContextData {
  companySymbol: string;
  companyName: string;
  companyProfile: any;
  competitorAnalysis: any;
  marketPositioning: any;
  industryBenchmarks: any;
  joyceAgentPrompts: any;
}

export function CompanyContextBenchmarkExplorer({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: CompanyContextBenchmarkExplorerProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<CompanyContextData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedCompetitor, setSelectedCompetitor] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/company-context-benchmark-explorer.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading company context data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading company context data...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load company context data</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const renderCompanyOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Company Profile */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="w-5 h-5" />
              <span>Company Profile</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-700">{data.companyProfile.overview.description}</p>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Founded:</span>
                <p className="text-gray-600">{data.companyProfile.overview.founded}</p>
              </div>
              <div>
                <span className="font-medium">Employees:</span>
                <p className="text-gray-600">{data.companyProfile.overview.employees}</p>
              </div>
              <div>
                <span className="font-medium">Revenue (2024):</span>
                <p className="text-gray-600">{data.companyProfile.overview.revenue2024}</p>
              </div>
              <div>
                <span className="font-medium">Market Cap:</span>
                <p className="text-gray-600">{data.companyProfile.overview.marketCap}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>Key Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {data.companyProfile.keyMetrics.map((metric: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{metric.metric}</p>
                  <p className="text-sm text-gray-600">{metric.rank}</p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-lg">{metric.value}</p>
                  <Badge className={getStatusColor(metric.status)}>
                    {metric.trend}
                  </Badge>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Business Segments */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <PieChart className="w-5 h-5" />
            <span>Business Segments</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {data.companyProfile.overview.segments.map((segment: any, index: number) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{segment.name}</h4>
                  <Badge className={getStatusColor(segment.status)}>
                    {segment.growth2024}
                  </Badge>
                </div>
                <p className="text-sm text-gray-600 mb-3">{segment.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Revenue Share</span>
                  <span className="text-lg font-bold">{segment.revenueContribution}</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCompetitorAnalysis = () => (
    <div className="space-y-6">
      {/* Competitor Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.competitorAnalysis.primaryCompetitors.map((competitor: any, index: number) => (
          <Card 
            key={index} 
            className={`cursor-pointer transition-all ${
              selectedCompetitor === competitor.symbol ? 'ring-2 ring-blue-500' : 'hover:shadow-lg'
            }`}
            onClick={() => setSelectedCompetitor(selectedCompetitor === competitor.symbol ? null : competitor.symbol)}
          >
            <CardHeader>
              <CardTitle className="flex justify-between items-center">
                <span>{competitor.name}</span>
                <Badge variant="outline">{competitor.competitivePosition}</Badge>
              </CardTitle>
              <p className="text-sm text-gray-600">{competitor.type}</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Revenue:</span>
                  <p className="text-gray-600">{competitor.revenue}</p>
                </div>
                <div>
                  <span className="font-medium">Market Cap:</span>
                  <p className="text-gray-600">{competitor.marketCap}</p>
                </div>
              </div>
              <Separator />
              <div className="space-y-2">
                <div>
                  <span className="font-medium text-green-600">Strengths:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {competitor.strengths.map((strength: string, i: number) => (
                      <li key={i}>• {strength}</li>
                    ))}
                  </ul>
                </div>
                <div>
                  <span className="font-medium text-red-600">Weaknesses:</span>
                  <ul className="text-sm text-gray-600 mt-1">
                    {competitor.weaknesses.map((weakness: string, i: number) => (
                      <li key={i}>• {weakness}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Competitive Metrics Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Radar className="w-5 h-5" />
            <span>Competitive Metrics Comparison</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Metric</th>
                  <th className="text-center py-2 font-medium">CVS</th>
                  <th className="text-center py-2 font-medium">UnitedHealth</th>
                  <th className="text-center py-2 font-medium">Walgreens</th>
                  <th className="text-center py-2 font-medium">Amazon</th>
                  <th className="text-center py-2 font-medium">Cigna</th>
                  <th className="text-center py-2 font-medium">Industry Avg</th>
                </tr>
              </thead>
              <tbody>
                {data.competitorAnalysis.competitiveMetrics.map((metric: any, index: number) => (
                  <tr key={index} className="border-b">
                    <td className="py-3 font-medium">{metric.metric}</td>
                    <td className="text-center py-3 font-bold text-blue-600">{metric.cvs}</td>
                    <td className="text-center py-3">{metric.unitedHealth}</td>
                    <td className="text-center py-3">{metric.walgreens}</td>
                    <td className="text-center py-3">{metric.amazon}</td>
                    <td className="text-center py-3">{metric.cigna}</td>
                    <td className="text-center py-3 text-gray-600">{metric.industry_avg}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderMarketPositioning = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Strengths */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-600">
              <Award className="w-5 h-5" />
              <span>Competitive Strengths</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {data.marketPositioning.strengths.map((strength: any, index: number) => (
              <div key={index} className="p-3 bg-green-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">{strength.area}</h4>
                  <Badge className="bg-green-100 text-green-800">
                    {strength.competitiveAdvantage}
                  </Badge>
                </div>
                <p className="text-sm text-gray-700 mb-2">{strength.description}</p>
                <p className="text-xs text-gray-600"><strong>Evidence:</strong> {strength.evidence}</p>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Weaknesses */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <Target className="w-5 h-5" />
              <span>Areas for Improvement</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {data.marketPositioning.weaknesses.map((weakness: any, index: number) => (
              <div key={index} className="p-3 bg-red-50 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">{weakness.area}</h4>
                  <Badge className="bg-red-100 text-red-800">
                    {weakness.competitiveDisadvantage}
                  </Badge>
                </div>
                <p className="text-sm text-gray-700 mb-2">{weakness.description}</p>
                <p className="text-xs text-gray-600"><strong>Evidence:</strong> {weakness.evidence}</p>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Opportunities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Market Opportunities</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {data.marketPositioning.opportunities.map((opportunity: any, index: number) => (
              <div key={index} className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
                <h4 className="font-medium mb-2">{opportunity.area}</h4>
                <p className="text-sm text-gray-700 mb-3">{opportunity.description}</p>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span>Market Size:</span>
                    <span className="font-medium">{opportunity.marketSize}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>CVS Position:</span>
                    <span className="font-medium text-blue-600">{opportunity.cvsPosition}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Threats */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="w-5 h-5" />
            <span>Strategic Threats</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.marketPositioning.threats.map((threat: any, index: number) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex justify-between items-start mb-2">
                  <h4 className="font-medium">{threat.threat}</h4>
                  <div className="flex space-x-2">
                    <Badge className={threat.probability === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                      {threat.probability} Prob
                    </Badge>
                    <Badge className={threat.impact === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                      {threat.impact} Impact
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-700 mb-2">{threat.description}</p>
                <p className="text-xs text-gray-600"><strong>Timeframe:</strong> {threat.timeframe}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <Building className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Company Context & Benchmark Explorer</h1>
          </div>
          <p className="text-gray-600">
            Explore {data.companyName}'s market position, competitive landscape, and industry benchmarks
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Company Overview</TabsTrigger>
            <TabsTrigger value="competitors">Competitive Analysis</TabsTrigger>
            <TabsTrigger value="positioning">Market Positioning</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderCompanyOverview()}
          </TabsContent>

          <TabsContent value="competitors">
            {renderCompetitorAnalysis()}
          </TabsContent>

          <TabsContent value="positioning">
            {renderMarketPositioning()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Executive Summary Dashboard
        </Button>
        <Button onClick={onNextSection}>
          Next: Execution Maturity (Joy Score) Deep Dive
        </Button>
      </div>
    </div>
  );
}