import { useState, useEffect } from "react";
import { DollarSign, TrendingDown, AlertCircle, Target, Calculator, TrendingUp, Droplets, CheckCircle, X, Play, <PERSON><PERSON>hart3, <PERSON><PERSON><PERSON>, Calendar, Award } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { But<PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";
import { Alert, AlertDescription } from "@/ui/alert";
import { Slider } from "@/ui/slider";

interface ValueLeakageFinancialImpactProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface ValueLeakageData {
  companySymbol: string;
  companyName: string;
  valueLeakageOverview: any;
  leakageCategories: any[];
  valueDriverTree: any;
  recoveryScenarios: any[];
  valueLeakageGame: any;
  benchmarkComparison: any;
  joyceAgentPrompts: any;
}

export function ValueLeakageFinancialImpact({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: ValueLeakageFinancialImpactProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<ValueLeakageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [gameState, setGameState] = useState<{[key: string]: boolean}>({});
  const [selectedScenario, setSelectedScenario] = useState("Realistic Recovery");
  const [investmentLevel, setInvestmentLevel] = useState([350]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/value-leakage-financial-impact.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading value leakage data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading value leakage analysis...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <DollarSign className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load value leakage data</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRecoverabilityColor = (recoverability: string) => {
    switch (recoverability) {
      case 'high': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const toggleLeak = (leakId: string) => {
    setGameState(prev => ({ ...prev, [leakId]: !prev[leakId] }));
  };

  const getFixedLeaksValue = () => {
    if (!data) return 0;
    return data.valueLeakageGame.leaks.reduce((total: number, leak: any) => {
      if (gameState[leak.id]) {
        return total + parseFloat(leak.size.replace(/[$B]/g, '')) * 1000; // Convert to millions
      }
      return total;
    }, 0);
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Total Value Leakage Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingDown className="w-5 h-5 text-red-600" />
              <span>Total Value Leakage</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-red-600">{data.valueLeakageOverview.totalLeakage.amount}</div>
              <div className="text-lg text-gray-600">{data.valueLeakageOverview.totalLeakage.percentage} of revenue</div>
              <p className="text-sm text-gray-600">{data.valueLeakageOverview.totalLeakage.description}</p>
              <Badge className="bg-red-100 text-red-800">
                Above industry average
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="w-5 h-5 text-green-600" />
              <span>Recoverable Value</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-green-600">{data.valueLeakageOverview.recoverablePortion.amount}</div>
              <div className="text-lg text-gray-600">{data.valueLeakageOverview.recoverablePortion.percentage} recoverable</div>
              <p className="text-sm text-gray-600">{data.valueLeakageOverview.recoverablePortion.description}</p>
              <Badge className="bg-green-100 text-green-800">
                {data.valueLeakageOverview.recoverablePortion.timeframe}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calculator className="w-5 h-5 text-blue-600" />
              <span>EPS Impact</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-2">
              <div className="text-3xl font-bold text-blue-600">+$2.45</div>
              <div className="text-lg text-gray-600">Potential EPS uplift</div>
              <p className="text-sm text-gray-600">If all value leakage recovered</p>
              <div className="text-xs text-gray-500 mt-2">
                Current EPS → Enhanced EPS
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Priority Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Value Recovery Priority Matrix</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Quick Wins</h4>
              <div className="text-2xl font-bold text-red-600">{data.valueLeakageOverview.priorityMatrix.quickWins}</div>
              <p className="text-sm text-red-700">6-12 months</p>
            </div>
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-semibold text-yellow-800 mb-2">Medium Term</h4>
              <div className="text-2xl font-bold text-yellow-600">{data.valueLeakageOverview.priorityMatrix.mediumTerm}</div>
              <p className="text-sm text-yellow-700">12-24 months</p>
            </div>
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Long Term</h4>
              <div className="text-2xl font-bold text-blue-600">{data.valueLeakageOverview.priorityMatrix.longTerm}</div>
              <p className="text-sm text-blue-700">24+ months</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderLeakageAnalysis = () => (
    <div className="space-y-6">
      {/* Leakage Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.leakageCategories.map((category: any, index: number) => (
          <Card 
            key={index}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedCategory === category.category ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedCategory(selectedCategory === category.category ? null : category.category)}
          >
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{category.category}</span>
                <Badge className={getPriorityColor(category.priority)}>
                  {category.priority}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold text-red-600">{category.amount}</span>
                  <span className={`font-medium ${getRecoverabilityColor(category.recoverability)}`}>
                    {category.recoverability} recovery
                  </span>
                </div>
                <Progress value={category.percentage} className="h-2" />
                <p className="text-sm text-gray-600">{category.description}</p>
                <div className="flex justify-between items-center text-sm">
                  <span>Timeline:</span>
                  <Badge variant="outline">{category.timeframe}</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Category Details */}
      {selectedCategory && (
        <Card>
          <CardHeader>
            <CardTitle>Deep Dive: {selectedCategory}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const category = data.leakageCategories.find((c: any) => c.category === selectedCategory);
              return (
                <div className="space-y-6">
                  <Tabs defaultValue="breakdown" className="w-full">
                    <TabsList>
                      <TabsTrigger value="breakdown">Value Breakdown</TabsTrigger>
                      <TabsTrigger value="rootcauses">Root Causes</TabsTrigger>
                      <TabsTrigger value="recovery">Recovery Path</TabsTrigger>
                      <TabsTrigger value="projections">Financial Projections</TabsTrigger>
                    </TabsList>
                    <TabsContent value="breakdown" className="space-y-3">
                      {category?.details?.specificLeakages?.map((leakage: any, i: number) => (
                        <div key={i} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <h4 className="font-medium">{leakage.area}</h4>
                            <Badge className="bg-blue-100 text-blue-800">{leakage.amount}</Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{leakage.description}</p>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">Progress:</span>
                            <Progress value={parseInt(leakage.progress)} className="h-2 flex-1" />
                            <span className="text-sm">{leakage.progress}</span>
                          </div>
                        </div>
                      ))}
                    </TabsContent>
                    <TabsContent value="rootcauses" className="space-y-2">
                      {category?.details?.rootCauses?.map((cause: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2">
                          <AlertCircle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{cause}</span>
                        </div>
                      ))}
                    </TabsContent>
                    <TabsContent value="recovery" className="space-y-2">
                      {category?.details?.recoveryPath?.map((step: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{step}</span>
                        </div>
                      ))}
                    </TabsContent>
                    <TabsContent value="projections">
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{category?.financialProjections?.year1Recovery}</div>
                          <div className="text-sm text-gray-600">Year 1</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">{category?.financialProjections?.year2Recovery}</div>
                          <div className="text-sm text-gray-600">Year 2</div>
                        </div>
                        <div className="text-center p-3 bg-yellow-50 rounded-lg">
                          <div className="font-bold text-yellow-600">{category?.financialProjections?.year3Recovery}</div>
                          <div className="text-sm text-gray-600">Year 3</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="font-bold text-gray-800">{category?.financialProjections?.totalRecoverable}</div>
                          <div className="text-sm text-gray-600">Total</div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderValueLeakageGame = () => (
    <div className="space-y-6">
      <Alert>
        <Droplets className="h-4 w-4" />
        <AlertDescription>
          Interactive simulation: Click on each "leak" to fix it and see the cumulative value recovery impact.
        </AlertDescription>
      </Alert>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Game Board */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Droplets className="w-5 h-5 text-blue-600" />
              <span>Plug the Value Leaks</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="relative">
              {/* Value Bucket Visualization */}
              <div className="bg-gradient-to-b from-blue-400 to-blue-600 rounded-lg p-6 mb-6">
                <div className="text-center text-white">
                  <div className="text-2xl font-bold mb-2">CVS Value Bucket</div>
                  <div className="text-lg">Total Potential: {data.valueLeakageGame.totalBucket}</div>
                  <div className="text-sm mt-2">Fixed Value: ${getFixedLeaksValue()}M</div>
                </div>
              </div>

              {/* Leak Controls */}
              <div className="space-y-3">
                {data.valueLeakageGame.leaks.map((leak: any, index: number) => (
                  <div key={leak.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{leak.name}</div>
                      <div className="text-sm text-gray-600">Value: {leak.size} | Time: {leak.timeToFix}</div>
                    </div>
                    <Button
                      variant={gameState[leak.id] ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleLeak(leak.id)}
                      className={gameState[leak.id] ? "bg-green-600 hover:bg-green-700" : ""}
                    >
                      {gameState[leak.id] ? (
                        <><CheckCircle className="w-4 h-4 mr-1" /> Fixed</>
                      ) : (
                        <><X className="w-4 h-4 mr-1" /> Fix Leak</>
                      )}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="w-5 h-5 text-yellow-600" />
              <span>Impact Summary</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Progress Metrics */}
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    ${getFixedLeaksValue()}M
                  </div>
                  <div className="text-sm text-gray-600">Value Recovered</div>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">
                    {Object.values(gameState).filter(Boolean).length}/4
                  </div>
                  <div className="text-sm text-gray-600">Leaks Fixed</div>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Recovery Progress</span>
                  <span className="text-sm text-gray-600">
                    {Math.round((getFixedLeaksValue() / 4200) * 100)}%
                  </span>
                </div>
                <Progress value={(getFixedLeaksValue() / 4200) * 100} className="h-3" />
              </div>

              {/* Completion Message */}
              {Object.values(gameState).filter(Boolean).length === 4 && (
                <Alert className="bg-green-50 border-green-200">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    {data.valueLeakageGame.completionMessages.allFixed}
                  </AlertDescription>
                </Alert>
              )}

              {/* EPS Impact */}
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="text-center">
                  <div className="text-lg font-bold text-yellow-800">
                    +${((getFixedLeaksValue() * 0.75) / 1300).toFixed(2)} EPS
                  </div>
                  <div className="text-sm text-yellow-700">Estimated EPS Impact</div>
                  <div className="text-xs text-gray-600 mt-1">
                    Assuming 75% flows to bottom line
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderScenarioAnalysis = () => (
    <div className="space-y-6">
      {/* Scenario Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Recovery Scenarios</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {data.recoveryScenarios.map((scenario: any, index: number) => (
              <div 
                key={index}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedScenario === scenario.scenario ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedScenario(scenario.scenario)}
              >
                <h4 className="font-semibold mb-2">{scenario.scenario}</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Recovery:</span>
                    <span className="font-bold">{scenario.totalRecovery}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Probability:</span>
                    <Badge className={scenario.probability.includes('70') ? 'bg-green-100 text-green-800' : 
                                   scenario.probability.includes('85') ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}>
                      {scenario.probability}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>ROI:</span>
                    <span className="font-bold text-green-600">{scenario.roi}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Selected Scenario Details */}
          {selectedScenario && (
            <div className="p-4 border border-blue-200 bg-blue-50 rounded-lg">
              {(() => {
                const scenario = data.recoveryScenarios.find((s: any) => s.scenario === selectedScenario);
                return (
                  <div className="space-y-4">
                    <h4 className="font-semibold">{scenario?.scenario} Details</h4>
                    <p className="text-sm">{scenario?.description}</p>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <div className="font-bold text-blue-600">{scenario?.totalRecovery}</div>
                        <div className="text-xs text-gray-600">Total Recovery</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-green-600">{scenario?.timeframe}</div>
                        <div className="text-xs text-gray-600">Timeframe</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-orange-600">{scenario?.investmentRequired}</div>
                        <div className="text-xs text-gray-600">Investment</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-purple-600">{scenario?.roi}</div>
                        <div className="text-xs text-gray-600">ROI</div>
                      </div>
                    </div>

                    <Tabs defaultValue="assumptions" className="w-full">
                      <TabsList>
                        <TabsTrigger value="assumptions">Key Assumptions</TabsTrigger>
                        <TabsTrigger value="risks">Risks</TabsTrigger>
                      </TabsList>
                      <TabsContent value="assumptions">
                        <ul className="text-sm space-y-1">
                          {scenario?.keyAssumptions?.map((assumption: string, i: number) => (
                            <li key={i} className="flex items-start space-x-2">
                              <CheckCircle className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                              <span>{assumption}</span>
                            </li>
                          ))}
                        </ul>
                      </TabsContent>
                      <TabsContent value="risks">
                        <ul className="text-sm space-y-1">
                          {scenario?.risks?.map((risk: string, i: number) => (
                            <li key={i} className="flex items-start space-x-2">
                              <AlertCircle className="w-3 h-3 text-red-500 mt-1 flex-shrink-0" />
                              <span>{risk}</span>
                            </li>
                          ))}
                        </ul>
                      </TabsContent>
                    </Tabs>
                  </div>
                );
              })()
              }
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <DollarSign className="w-6 h-6 text-green-600" />
            <h1 className="text-2xl font-bold text-gray-900">Value Leakage & Financial Impact</h1>
          </div>
          <p className="text-gray-600">
            Comprehensive analysis of {data.companyName}'s value leakage sources and recovery opportunities
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Value Overview</TabsTrigger>
            <TabsTrigger value="analysis">Leakage Analysis</TabsTrigger>
            <TabsTrigger value="game">Interactive Game</TabsTrigger>
            <TabsTrigger value="scenarios">Recovery Scenarios</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderOverview()}
          </TabsContent>

          <TabsContent value="analysis">
            {renderLeakageAnalysis()}
          </TabsContent>

          <TabsContent value="game">
            {renderValueLeakageGame()}
          </TabsContent>

          <TabsContent value="scenarios">
            {renderScenarioAnalysis()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Agility Simulator
        </Button>
        <Button onClick={onNextSection}>
          Next: Strategic Lens Engine
        </Button>
      </div>
    </div>
  );
}