import { But<PERSON> } from "@/ui/button";
import { Card } from "@/ui/card";
import { X, FileText } from "lucide-react";

interface TableOfContentsItem {
  id: string;
  label: string;
  description: string;
}

const tableOfContentsItems: TableOfContentsItem[] = [
  {
    id: "executive-summary-dashboard",
    label: "Executive Summary Dashboard",
    description: "High-level readiness overview with key metrics and strategic priorities"
  },
  {
    id: "company-context-benchmark-explorer",
    label: "Company Context & Benchmark Explorer", 
    description: "Industry positioning, peer comparison, and contextual analysis"
  },
  {
    id: "execution-maturity-joy-score-deep-dive",
    label: "Execution Maturity (Joy Score) Deep Dive",
    description: "Comprehensive maturity assessment and Joy Score analysis"
  },
  {
    id: "agility-simulator",
    label: "Agility Simulator",
    description: "Interactive scenario modeling and agility impact analysis"
  },
  {
    id: "value-leakage-financial-impact",
    label: "Value Leakage & Financial Impact",
    description: "Financial impact quantification and value recovery opportunities"
  },
  {
    id: "strategic-lens-engine",
    label: "Strategic Lens Engine",
    description: "Multi-perspective strategic analysis and framework application"
  },
  {
    id: "best-practice-library",
    label: "Best Practice Library",
    description: "Curated practices, case studies, and implementation guides"
  },
  {
    id: "recommendations-roadmap",
    label: "Recommendations & Roadmap",
    description: "Personalized recommendations and strategic transformation roadmap"
  },
  {
    id: "interactive-qa-scenario-planner",
    label: "Interactive Q&A and Scenario Planner",
    description: "AI-powered insights and collaborative scenario planning tools"
  }
];

interface ERATableOfContentsProps {
  activeTab: string;
  onNavigate: (tabId: string) => void;
  onClose: () => void;
}

export function ERATableOfContents({ 
  activeTab, 
  onNavigate, 
  onClose 
}: ERATableOfContentsProps) {
  const handleItemClick = (tabId: string) => {
    onNavigate(tabId);
    onClose();
  };

  return (
    <div className="absolute top-full left-0 mt-2 w-[600px] bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-[600px] overflow-y-auto">
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-gray-600" />
            <h3 className="text-lg font-semibold text-gray-900">ERA Report Contents</h3>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Table of Contents Items */}
        <div className="space-y-2">
          {tableOfContentsItems.map((item, index) => (
            <Card 
              key={item.id}
              className={`p-4 cursor-pointer transition-all hover:shadow-md hover:bg-gray-50 ${
                activeTab === item.id ? 'ring-2 ring-blue-500 bg-blue-50' : ''
              }`}
              onClick={() => handleItemClick(item.id)}
            >
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium ${
                    activeTab === item.id 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={`font-medium ${
                    activeTab === item.id ? 'text-blue-900' : 'text-gray-900'
                  }`}>
                    {item.label}
                  </h4>
                  <p className={`text-sm mt-1 ${
                    activeTab === item.id ? 'text-blue-700' : 'text-gray-600'
                  }`}>
                    {item.description}
                  </p>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Click any section to navigate directly to that part of the ERA report
          </p>
        </div>
      </div>
    </div>
  );
}