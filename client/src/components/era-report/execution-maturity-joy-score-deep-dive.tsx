import { useState, useEffect } from "react";
import { Smile, TrendingUp, Activity, Target, Zap, <PERSON>, Brain, Settings, ArrowUp, ArrowDown, Info, CheckCircle, AlertTriangle } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/ui/tooltip";

interface ExecutionMaturityJoyScoreDeepDiveProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface JoyScoreData {
  companySymbol: string;
  companyName: string;
  joyScoreOverview: any;
  dimensionBreakdown: any;
  maturityLevels: any;
  benchmarkComparison: any;
  improvementRoadmap: any;
  executionGaps: any;
  joyceAgentPrompts: any;
}

export function ExecutionMaturityJoyScoreDeepDive({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: ExecutionMaturityJoyScoreDeepDiveProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<JoyScoreData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedDimension, setSelectedDimension] = useState<string | null>(null);
  const [showMaturityTooltip, setShowMaturityTooltip] = useState<number | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/execution-maturity-joy-score-deep-dive.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading Joy Score data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading Joy Score analysis...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Smile className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load Joy Score data</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'positive': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getLevelColor = (level: number) => {
    if (level >= 4) return 'text-green-600';
    if (level >= 3) return 'text-blue-600';
    if (level >= 2) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getDimensionIcon = (dimension: string) => {
    switch (dimension.toLowerCase()) {
      case 'strategy & leadership': return Target;
      case 'process excellence': return Settings;
      case 'technology & innovation': return Zap;
      case 'talent & people': return Users;
      case 'culture & mindset': return Brain;
      default: return Activity;
    }
  };

  const renderJoyScoreOverview = () => (
    <div className="space-y-6">
      {/* Joy Score Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Smile className="w-5 h-5 text-blue-600" />
              <span>Current Joy Score</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center space-y-4">
              <div className="relative">
                <div className="w-32 h-32 mx-auto">
                  <svg className="w-32 h-32 transform -rotate-90" viewBox="0 0 100 100">
                    <circle
                      cx="50"
                      cy="50"
                      r="40"
                      stroke="#E5E7EB"
                      strokeWidth="8"
                      fill="none"
                    />
                    <circle
                      cx="50"
                      cy="50"
                      r="40"
                      stroke="#3B82F6"
                      strokeWidth="8"
                      fill="none"
                      strokeLinecap="round"
                      strokeDasharray={`${(data.joyScoreOverview.currentScore / data.joyScoreOverview.maxScore) * 251.2} 251.2`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className="text-2xl font-bold text-blue-600">{data.joyScoreOverview.currentScore}</span>
                  </div>
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold">{data.joyScoreOverview.level}</h3>
                <p className="text-sm text-gray-600">{data.joyScoreOverview.levelDescription}</p>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Industry Average:</span>
                  <p className="text-gray-600">{data.joyScoreOverview.industryAverage}</p>
                </div>
                <div>
                  <span className="font-medium">Target Score:</span>
                  <p className="text-gray-600">{data.joyScoreOverview.targetScore}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <span>Performance Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Percentile Rank</span>
              <div className="flex items-center space-x-2">
                <span className="text-lg font-bold">{data.joyScoreOverview.percentileRank}th</span>
                <Badge className="bg-yellow-100 text-yellow-800">Mid-tier</Badge>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Year-over-Year</span>
              <div className="flex items-center space-x-2">
                {data.joyScoreOverview.yearOverYearChange > 0 ? (
                  <ArrowUp className="w-4 h-4 text-green-600" />
                ) : (
                  <ArrowDown className="w-4 h-4 text-red-600" />
                )}
                <span className={`font-bold ${data.joyScoreOverview.yearOverYearChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                  +{data.joyScoreOverview.yearOverYearChange}
                </span>
              </div>
            </div>
            <div className="flex justify-between items-center">
              <span className="font-medium">Time to Target</span>
              <span className="font-bold text-blue-600">{data.joyScoreOverview.timeToTarget}</span>
            </div>
            <div className="mt-4">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Progress to Target</span>
                <span className="text-sm text-gray-600">
                  {data.joyScoreOverview.currentScore}/{data.joyScoreOverview.targetScore}
                </span>
              </div>
              <Progress 
                value={(data.joyScoreOverview.currentScore / data.joyScoreOverview.targetScore) * 100} 
                className="h-2" 
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderDimensionBreakdown = () => (
    <div className="space-y-6">
      {/* Dimension Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {data.dimensionBreakdown.dimensions.map((dimension: any, index: number) => {
          const Icon = getDimensionIcon(dimension.name);
          return (
            <Card 
              key={index}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedDimension === dimension.name ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedDimension(selectedDimension === dimension.name ? null : dimension.name)}
            >
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Icon className="w-5 h-5" />
                    <span className="text-sm">{dimension.name}</span>
                  </div>
                  <Badge className={getStatusColor(dimension.status)}>
                    {dimension.levelName}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-blue-600">{dimension.currentLevel}</span>
                    <div className="text-right text-sm">
                      <div className="text-gray-500">Target: {dimension.targetLevel}</div>
                      <div className={`font-medium ${getLevelColor(dimension.currentLevel)}`}>
                        Gap: {(dimension.targetLevel - dimension.currentLevel).toFixed(1)}
                      </div>
                    </div>
                  </div>
                  <Progress value={(dimension.currentLevel / 5) * 100} className="h-2" />
                  <p className="text-sm text-gray-600">{dimension.description}</p>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selected Dimension Detail */}
      {selectedDimension && (
        <Card>
          <CardHeader>
            <CardTitle>Deep Dive: {selectedDimension}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const dimension = data.dimensionBreakdown.dimensions.find((d: any) => d.name === selectedDimension);
              return (
                <div className="space-y-6">
                  <Tabs defaultValue="evidence" className="w-full">
                    <TabsList>
                      <TabsTrigger value="evidence">Evidence</TabsTrigger>
                      <TabsTrigger value="opportunities">Opportunities</TabsTrigger>
                      <TabsTrigger value="findings">Key Findings</TabsTrigger>
                    </TabsList>
                    <TabsContent value="evidence" className="space-y-3">
                      {dimension?.evidence?.map((evidence: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{evidence}</span>
                        </div>
                      ))}
                    </TabsContent>
                    <TabsContent value="opportunities" className="space-y-3">
                      {dimension?.improvementOpportunities?.map((opportunity: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2">
                          <Target className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{opportunity}</span>
                        </div>
                      ))}
                    </TabsContent>
                    <TabsContent value="findings" className="space-y-3">
                      {dimension?.keyFindings?.map((finding: any, i: number) => (
                        <div key={i} className="p-3 bg-gray-50 rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-medium">{finding.finding}</h4>
                            <Badge className={finding.impact === 'High' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'}>
                              {finding.impact} Impact
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600">{finding.description}</p>
                        </div>
                      ))}
                    </TabsContent>
                  </Tabs>
                </div>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderMaturityLevels = () => (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5" />
              <span>Execution Maturity Framework</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {data.maturityLevels.definitions.map((level: any, index: number) => (
                <Tooltip key={index}>
                  <TooltipTrigger asChild>
                    <div 
                      className={`p-4 border rounded-lg cursor-help transition-all hover:shadow-md ${
                        level.level === Math.floor(data.joyScoreOverview.currentScore) 
                          ? 'border-blue-500 bg-blue-50' 
                          : 'border-gray-200'
                      }`}
                      onMouseEnter={() => setShowMaturityTooltip(level.level)}
                      onMouseLeave={() => setShowMaturityTooltip(null)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-white ${
                            level.level === Math.floor(data.joyScoreOverview.currentScore) ? 'bg-blue-600' : 'bg-gray-400'
                          }`}>
                            {level.level}
                          </div>
                          <div>
                            <h4 className="font-medium">{level.name}</h4>
                            <p className="text-sm text-gray-600">{level.description}</p>
                          </div>
                        </div>
                        {level.level === Math.floor(data.joyScoreOverview.currentScore) && (
                          <Badge className="bg-blue-100 text-blue-800">Current Level</Badge>
                        )}
                      </div>
                    </div>
                  </TooltipTrigger>
                  <TooltipContent side="right" className="max-w-sm">
                    <div className="space-y-2">
                      <h4 className="font-medium">Level {level.level}: {level.name}</h4>
                      <ul className="text-sm space-y-1">
                        {level.characteristics.map((char: string, i: number) => (
                          <li key={i} className="flex items-start space-x-1">
                            <span>•</span>
                            <span>{char}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </TooltipContent>
                </Tooltip>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );

  const renderBenchmarkComparison = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Industry Benchmark Comparison</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 font-medium">Company</th>
                  <th className="text-center py-3 font-medium">Joy Score</th>
                  <th className="text-center py-3 font-medium">Strategy</th>
                  <th className="text-center py-3 font-medium">Processes</th>
                  <th className="text-center py-3 font-medium">Technology</th>
                  <th className="text-center py-3 font-medium">Talent</th>
                  <th className="text-center py-3 font-medium">Culture</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b bg-blue-50">
                  <td className="py-3 font-bold text-blue-600">CVS (Current)</td>
                  <td className="text-center py-3 font-bold text-blue-600">{data.joyScoreOverview.currentScore}</td>
                  <td className="text-center py-3">2.5</td>
                  <td className="text-center py-3">3.5</td>
                  <td className="text-center py-3">3.5</td>
                  <td className="text-center py-3">2.0</td>
                  <td className="text-center py-3">2.5</td>
                </tr>
                {data.benchmarkComparison.industryComparison.map((company: any, index: number) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 font-medium">{company.company}</td>
                    <td className="text-center py-3 font-bold">{company.joyScore}</td>
                    <td className="text-center py-3">{company.dimensions.strategy}</td>
                    <td className="text-center py-3">{company.dimensions.processes}</td>
                    <td className="text-center py-3">{company.dimensions.technology}</td>
                    <td className="text-center py-3">{company.dimensions.talent}</td>
                    <td className="text-center py-3">{company.dimensions.culture}</td>
                  </tr>
                ))}
                <tr className="border-b bg-gray-100">
                  <td className="py-3 font-medium text-gray-600">Industry Average</td>
                  <td className="text-center py-3 font-bold text-gray-600">{data.joyScoreOverview.industryAverage}</td>
                  <td className="text-center py-3 text-gray-600">3.2</td>
                  <td className="text-center py-3 text-gray-600">3.4</td>
                  <td className="text-center py-3 text-gray-600">3.8</td>
                  <td className="text-center py-3 text-gray-600">3.0</td>
                  <td className="text-center py-3 text-gray-600">3.2</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Top Performer Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.benchmarkComparison.industryComparison.slice(0, 2).map((company: any, index: number) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>{company.company}</span>
                <Badge className="bg-green-100 text-green-800">
                  Joy Score: {company.joyScore}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <span className="font-medium text-green-600">Key Strengths:</span>
                  <ul className="text-sm text-gray-600 mt-1 space-y-1">
                    {company.strengths.map((strength: string, i: number) => (
                      <li key={i} className="flex items-start space-x-1">
                        <span>•</span>
                        <span>{strength}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <Smile className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Execution Maturity (Joy Score) Deep Dive</h1>
          </div>
          <p className="text-gray-600">
            Comprehensive analysis of {data.companyName}'s execution maturity across five key dimensions
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Joy Score Overview</TabsTrigger>
            <TabsTrigger value="dimensions">Dimension Breakdown</TabsTrigger>
            <TabsTrigger value="maturity">Maturity Levels</TabsTrigger>
            <TabsTrigger value="benchmarks">Benchmarks</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderJoyScoreOverview()}
          </TabsContent>

          <TabsContent value="dimensions">
            {renderDimensionBreakdown()}
          </TabsContent>

          <TabsContent value="maturity">
            {renderMaturityLevels()}
          </TabsContent>

          <TabsContent value="benchmarks">
            {renderBenchmarkComparison()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Company Context & Benchmark Explorer
        </Button>
        <Button onClick={onNextSection}>
          Next: Agility Simulator
        </Button>
      </div>
    </div>
  );
}