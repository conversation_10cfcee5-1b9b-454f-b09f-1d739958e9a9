import { useState, useEffect } from "react";
import { MapPin, Target, Route, Calendar, DollarSign, TrendingUp, Alert<PERSON>riangle, CheckCircle, Clock, Award, BarChart3, Settings, Users, Building } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { But<PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";
import { Alert, AlertDescription } from "@/ui/alert";

interface RecommendationsRoadmapProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface RecommendationsData {
  companySymbol: string;
  companyName: string;
  executiveSummary: any;
  strategicRecommendations: any[];
  implementationRoadmap: any;
  successMetrics: any;
  riskMitigation: any;
  joyceAgentPrompts: any;
}

export function RecommendationsRoadmap({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: RecommendationsRoadmapProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<RecommendationsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [selectedRecommendation, setSelectedRecommendation] = useState<string | null>(null);
  const [selectedPhase, setSelectedPhase] = useState<number>(0);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/recommendations-roadmap.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading recommendations data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading recommendations and roadmap...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load recommendations data</p>
        </div>
      </div>
    );
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Leadership & Governance': return Target;
      case 'Integration & Synergies': return Building;
      case 'Operations & Efficiency': return Settings;
      case 'Technology & Innovation': return BarChart3;
      case 'Culture & People': return Users;
      default: return CheckCircle;
    }
  };

  const renderExecutiveSummary = () => (
    <div className="space-y-6">
      {/* Key Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.executiveSummary.keyRecommendations}</div>
            <div className="text-sm text-gray-600">Key Recommendations</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <DollarSign className="w-8 h-8 text-red-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.executiveSummary.totalInvestment}</div>
            <div className="text-sm text-gray-600">Total Investment</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.executiveSummary.expectedReturn}</div>
            <div className="text-sm text-gray-600">Expected Return</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Calendar className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.executiveSummary.implementationTimeframe}</div>
            <div className="text-sm text-gray-600">Implementation Timeline</div>
          </CardContent>
        </Card>
      </div>

      {/* ROI Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="w-5 h-5 text-yellow-600" />
            <span>Transformation ROI Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-3xl font-bold text-green-600">8.4x</div>
              <div className="text-sm text-gray-600">Overall ROI</div>
              <div className="text-xs text-gray-500 mt-1">$4.2B return on $500M investment</div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-3xl font-bold text-blue-600">+$1.70</div>
              <div className="text-sm text-gray-600">Potential EPS Uplift</div>
              <div className="text-xs text-gray-500 mt-1">Based on realistic recovery scenario</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-3xl font-bold text-yellow-600">{data.executiveSummary.confidenceLevel}</div>
              <div className="text-sm text-gray-600">Confidence Level</div>
              <div className="text-xs text-gray-500 mt-1">Based on industry benchmarks</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Critical Success Factors */}
      <Alert>
        <CheckCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Critical Success Factors:</strong> CEO commitment, dedicated execution office, cross-functional collaboration, and sustained focus on cultural transformation.
        </AlertDescription>
      </Alert>
    </div>
  );

  const renderRecommendations = () => (
    <div className="space-y-6">
      {/* Recommendations Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {data.strategicRecommendations.map((rec: any, index: number) => {
          const Icon = getCategoryIcon(rec.category);
          return (
            <Card 
              key={index}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                selectedRecommendation === rec.recommendationId ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedRecommendation(selectedRecommendation === rec.recommendationId ? null : rec.recommendationId)}
            >
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <Icon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <CardTitle className="text-lg">{rec.title}</CardTitle>
                      <div className="text-sm text-gray-600 mt-1">{rec.category}</div>
                    </div>
                  </div>
                  <Badge className={getPriorityColor(rec.priority)}>
                    {rec.priority}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 mb-4">{rec.description}</p>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <DollarSign className="w-4 h-4 text-green-600 mx-auto mb-1" />
                    <div className="font-bold">{rec.investment}</div>
                    <div className="text-xs text-gray-600">Investment</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <TrendingUp className="w-4 h-4 text-blue-600 mx-auto mb-1" />
                    <div className="font-bold">{rec.expectedReturn}</div>
                    <div className="text-xs text-gray-600">Return</div>
                  </div>
                  <div className="text-center p-2 bg-gray-50 rounded">
                    <Clock className="w-4 h-4 text-purple-600 mx-auto mb-1" />
                    <div className="font-bold">{rec.timeframe}</div>
                    <div className="text-xs text-gray-600">Timeline</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Selected Recommendation Details */}
      {selectedRecommendation && (
        <Card>
          <CardHeader>
            <CardTitle>Implementation Deep Dive: {data.strategicRecommendations.find((r: any) => r.recommendationId === selectedRecommendation)?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const rec = data.strategicRecommendations.find((r: any) => r.recommendationId === selectedRecommendation);
              return (
                <Tabs defaultValue="rationale" className="w-full">
                  <TabsList>
                    <TabsTrigger value="rationale">Rationale</TabsTrigger>
                    <TabsTrigger value="components">Key Components</TabsTrigger>
                    <TabsTrigger value="metrics">Success Metrics</TabsTrigger>
                    <TabsTrigger value="risks">Risks & Dependencies</TabsTrigger>
                  </TabsList>
                  <TabsContent value="rationale" className="space-y-3">
                    {rec?.rationale?.map((rationale: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{rationale}</span>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="components" className="space-y-4">
                    {rec?.keyComponents?.map((component: any, i: number) => (
                      <div key={i} className="p-3 border border-gray-200 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">{component.component}</h4>
                          <Badge variant="outline">{component.timeline}</Badge>
                        </div>
                        <p className="text-sm text-gray-600">{component.description}</p>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="metrics" className="space-y-2">
                    {rec?.successMetrics?.map((metric: string, i: number) => (
                      <div key={i} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                        <Target className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{metric}</span>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="risks">
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-medium mb-2 text-red-600">Key Risks</h4>
                        <div className="space-y-2">
                          {rec?.risks?.map((risk: string, i: number) => (
                            <div key={i} className="flex items-start space-x-2">
                              <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{risk}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2 text-blue-600">Dependencies</h4>
                        <div className="space-y-2">
                          {rec?.dependencies?.map((dependency: string, i: number) => (
                            <div key={i} className="flex items-start space-x-2">
                              <CheckCircle className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                              <span className="text-sm">{dependency}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderRoadmap = () => (
    <div className="space-y-6">
      {/* Implementation Phases */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Route className="w-5 h-5" />
            <span>Implementation Roadmap</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {data.implementationRoadmap.phases.map((phase: any, index: number) => (
              <div 
                key={index}
                className={`p-4 border rounded-lg cursor-pointer transition-all ${
                  selectedPhase === index ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedPhase(index)}
              >
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold">{phase.phase}</h3>
                  <Badge className={phase.riskLevel === 'Low' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                    {phase.riskLevel} Risk
                  </Badge>
                </div>
                
                <p className="text-gray-700 mb-4">{phase.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-white rounded border">
                    <DollarSign className="w-5 h-5 text-red-600 mx-auto mb-1" />
                    <div className="font-bold">{phase.investment}</div>
                    <div className="text-xs text-gray-600">Investment</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <TrendingUp className="w-5 h-5 text-green-600 mx-auto mb-1" />
                    <div className="font-bold">{phase.expectedReturns}</div>
                    <div className="text-xs text-gray-600">Expected Returns</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded border">
                    <Target className="w-5 h-5 text-blue-600 mx-auto mb-1" />
                    <div className="font-bold">{phase.keyMilestones.length}</div>
                    <div className="text-xs text-gray-600">Key Milestones</div>
                  </div>
                </div>
                
                {selectedPhase === index && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="font-medium mb-2">Key Milestones:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {phase.keyMilestones.map((milestone: string, i: number) => (
                        <div key={i} className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm">{milestone}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Critical Path */}
      <Card>
        <CardHeader>
          <CardTitle>Critical Path Dependencies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {data.implementationRoadmap.criticalPath.map((item: string, index: number) => (
              <div key={index} className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                  <span className="text-sm font-bold text-blue-600">{index + 1}</span>
                </div>
                <span className="text-sm">{item}</span>
                {index < data.implementationRoadmap.criticalPath.length - 1 && (
                  <div className="flex-1 h-px bg-gray-300"></div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderSuccessMetrics = () => (
    <div className="space-y-6">
      {/* Metrics Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {Object.entries(data.successMetrics).map(([category, metrics]: [string, any]) => (
          <Card key={category}>
            <CardHeader>
              <CardTitle className="capitalize">{category.replace(/([A-Z])/g, ' $1').trim()} Metrics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {metrics.map((metric: any, index: number) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{metric.metric}</span>
                    <Badge variant="outline">{metric.timeframe}</Badge>
                  </div>
                  <div className="text-xs text-gray-600">
                    <span>From: {metric.baseline}</span>
                    <br />
                    <span>To: {metric.target}</span>
                  </div>
                  <Progress value={25} className="h-2" />
                </div>
              ))}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Success Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Success Milestone Timeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              {milestone: "Execution Office Established", timeline: "Month 3", status: "pending"},
              {milestone: "First Integration Synergies Captured", timeline: "Month 6", status: "pending"},
              {milestone: "Star Ratings Improvement Visible", timeline: "Month 12", status: "pending"},
              {milestone: "Joy Score Improvement to 3.5+", timeline: "Month 18", status: "pending"},
              {milestone: "Target Joy Score of 4.0 Achieved", timeline: "Month 24", status: "pending"}
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg">
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                <div className="flex-1">
                  <div className="font-medium">{item.milestone}</div>
                  <div className="text-sm text-gray-600">{item.timeline}</div>
                </div>
                <Badge variant="outline">{item.status}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <MapPin className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Recommendations & Roadmap</h1>
          </div>
          <p className="text-gray-600">
            Strategic recommendations and implementation roadmap for {data.companyName}'s transformation
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Executive Summary</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
            <TabsTrigger value="roadmap">Implementation Roadmap</TabsTrigger>
            <TabsTrigger value="metrics">Success Metrics</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderExecutiveSummary()}
          </TabsContent>

          <TabsContent value="recommendations">
            {renderRecommendations()}
          </TabsContent>

          <TabsContent value="roadmap">
            {renderRoadmap()}
          </TabsContent>

          <TabsContent value="metrics">
            {renderSuccessMetrics()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Best Practice Library
        </Button>
        <Button onClick={onNextSection}>
          Next: Interactive Q&A and Scenario Planner
        </Button>
      </div>
    </div>
  );
}