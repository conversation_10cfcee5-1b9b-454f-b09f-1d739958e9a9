import { useState, useEffect } from "react";
import { <PERSON>, Filter, Compass, TrendingUp, DollarSign, Zap, Target, BarChart3, Calculator, Award, AlertTriangle, CheckCircle, ArrowRight } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/ui/tabs";
import { Progress } from "@/ui/progress";
import { Separator } from "@/ui/separator";
import { Alert, AlertDescription } from "@/ui/alert";

interface StrategicLensEngineProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface StrategicLensData {
  companySymbol: string;
  companyName: string;
  lensOverview: any;
  strategicLenses: any[];
  lensComparison: any;
  lensSpecificInsights: any;
  dynamicRecommendations: any;
  joyceAgentPrompts: any;
}

export function StrategicLensEngine({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: StrategicLensEngineProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<StrategicLensData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [activeLens, setActiveLens] = useState("growth");
  const [comparisonMode, setComparisonMode] = useState(false);
  const [selectedLenses, setSelectedLenses] = useState<string[]>(["growth", "cost"]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/strategic-lens-engine.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading strategic lens data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading strategic lens engine...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <Eye className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load strategic lens data</p>
        </div>
      </div>
    );
  }

  const getLensIcon = (iconName: string) => {
    switch (iconName) {
      case 'TrendingUp': return TrendingUp;
      case 'DollarSign': return DollarSign;
      case 'Zap': return Zap;
      case 'Target': return Target;
      default: return Eye;
    }
  };

  const getLensColor = (lensId: string) => {
    switch (lensId) {
      case 'growth': return 'from-green-500 to-green-600';
      case 'cost': return 'from-yellow-500 to-yellow-600';
      case 'innovation': return 'from-purple-500 to-purple-600';
      case 'pre-exit': return 'from-blue-500 to-blue-600';
      default: return 'from-gray-500 to-gray-600';
    }
  };

  const toggleLensComparison = (lensId: string) => {
    if (selectedLenses.includes(lensId)) {
      setSelectedLenses(selectedLenses.filter(id => id !== lensId));
    } else {
      setSelectedLenses([...selectedLenses, lensId]);
    }
  };

  const renderLensOverview = () => (
    <div className="space-y-6">
      {/* Lens Selector */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-4 gap-6">
        {data.strategicLenses.map((lens: any, index: number) => {
          const Icon = getLensIcon(lens.icon);
          return (
            <Card 
              key={index}
              className={`cursor-pointer transition-all hover:shadow-lg ${
                activeLens === lens.lensId ? 'ring-2 ring-blue-500' : ''
              }`}
              onClick={() => setActiveLens(lens.lensId)}
            >
              <CardHeader className="pb-3">
                <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${getLensColor(lens.lensId)} flex items-center justify-center mb-3`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <CardTitle className="text-lg">{lens.name}</CardTitle>
                {lens.isDefault && <Badge className="bg-blue-100 text-blue-800">Default</Badge>}
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">{lens.description}</p>
                <div className="mt-3 text-xs text-gray-500">
                  <strong>Focus:</strong> {lens.priority}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Active Lens Details */}
      {activeLens && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-3">
              {(() => {
                const lens = data.strategicLenses.find((l: any) => l.lensId === activeLens);
                const Icon = getLensIcon(lens?.icon);
                return (
                  <>
                    <div className={`w-8 h-8 rounded-lg bg-gradient-to-br ${getLensColor(activeLens)} flex items-center justify-center`}>
                      <Icon className="w-4 h-4 text-white" />
                    </div>
                    <span>{lens?.name} Lens Deep Dive</span>
                  </>
                );
              })()
              }
            </CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const lens = data.strategicLenses.find((l: any) => l.lensId === activeLens);
              return (
                <Tabs defaultValue="strategy" className="w-full">
                  <TabsList>
                    <TabsTrigger value="strategy">Strategic Focus</TabsTrigger>
                    <TabsTrigger value="drivers">Value Drivers</TabsTrigger>
                    <TabsTrigger value="priorities">Execution Priorities</TabsTrigger>
                    <TabsTrigger value="risks">Risk Factors</TabsTrigger>
                  </TabsList>
                  <TabsContent value="strategy" className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-3 bg-blue-50 rounded-lg">
                        <h4 className="font-medium text-blue-800">Primary Focus</h4>
                        <p className="text-sm text-blue-700">{lens?.strategicFocus.primary}</p>
                      </div>
                      <div className="p-3 bg-green-50 rounded-lg">
                        <h4 className="font-medium text-green-800">Secondary Focus</h4>
                        <p className="text-sm text-green-700">{lens?.strategicFocus.secondary}</p>
                      </div>
                      <div className="p-3 bg-yellow-50 rounded-lg">
                        <h4 className="font-medium text-yellow-800">Tertiary Focus</h4>
                        <p className="text-sm text-yellow-700">{lens?.strategicFocus.tertiary}</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Key Metrics:</h4>
                      <div className="flex flex-wrap gap-2">
                        {lens?.keyMetrics.map((metric: string, i: number) => (
                          <Badge key={i} variant="outline">{metric}</Badge>
                        ))}
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="drivers" className="space-y-3">
                    {lens?.valueDrivers.map((driver: any, i: number) => (
                      <div key={i} className="p-3 border border-gray-200 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="font-medium">{driver.driver}</h4>
                          <Badge className={driver.probability === 'high' ? 'bg-green-100 text-green-800' : 
                                         driver.probability === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
                            {driver.probability} probability
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="font-medium">Impact:</span>
                            <span className="ml-2 font-bold text-green-600">{driver.impact}</span>
                          </div>
                          <div>
                            <span className="font-medium">Timeframe:</span>
                            <span className="ml-2">{driver.timeframe}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="priorities" className="space-y-2">
                    {lens?.executionPriorities.map((priority: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{priority}</span>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="risks" className="space-y-2">
                    {lens?.riskFactors.map((risk: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2">
                        <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{risk}</span>
                      </div>
                    ))}
                  </TabsContent>
                </Tabs>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderLensComparison = () => (
    <div className="space-y-6">
      {/* Lens Selection for Comparison */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="w-5 h-5" />
            <span>EV Uplift Comparison</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
            {data.lensComparison.evUpliftAnalysis.map((analysis: any, index: number) => (
              <div key={index} className="p-4 border border-gray-200 rounded-lg text-center">
                <h4 className="font-semibold mb-2">{analysis.lens}</h4>
                <div className="text-2xl font-bold text-green-600 mb-1">{analysis.uplift}</div>
                <div className="text-sm text-gray-600 mb-2">{analysis.upliftPercent} uplift</div>
                <Badge className={analysis.confidenceLevel === 'high' ? 'bg-green-100 text-green-800' : 
                               analysis.confidenceLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
                  {analysis.confidenceLevel} confidence
                </Badge>
                <div className="text-xs text-gray-500 mt-2">{analysis.timeframe}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Risk-Adjusted Returns */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Calculator className="w-5 h-5" />
            <span>Risk-Adjusted Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 font-medium">Strategic Lens</th>
                  <th className="text-center py-2 font-medium">Expected Return</th>
                  <th className="text-center py-2 font-medium">Volatility</th>
                  <th className="text-center py-2 font-medium">Sharpe Ratio</th>
                  <th className="text-center py-2 font-medium">Risk Score</th>
                </tr>
              </thead>
              <tbody>
                {data.lensComparison.riskAdjustedReturns.map((analysis: any, index: number) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="py-3 font-medium">{analysis.lens}</td>
                    <td className="text-center py-3 font-bold text-green-600">{analysis.expectedReturn}</td>
                    <td className="text-center py-3">
                      <Badge className={analysis.volatility === 'low' ? 'bg-green-100 text-green-800' :
                                       analysis.volatility === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'}>
                        {analysis.volatility}
                      </Badge>
                    </td>
                    <td className="text-center py-3 font-bold">{analysis.sharpeRatio}</td>
                    <td className="text-center py-3">
                      <div className={`inline-block w-6 h-6 rounded-full ${
                        analysis.sharpeRatio > 1.0 ? 'bg-green-500' : 
                        analysis.sharpeRatio > 0.8 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}></div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderLensInsights = () => {
    const insights = data.lensSpecificInsights[activeLens];
    if (!insights) return null;

    return (
      <div className="space-y-6">
        <Alert>
          <Eye className="h-4 w-4" />
          <AlertDescription>
            Deep insights for the {data.strategicLenses.find((l: any) => l.lensId === activeLens)?.name} lens perspective.
          </AlertDescription>
        </Alert>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Market Opportunities / Efficiency Opportunities / etc */}
          <Card>
            <CardHeader>
              <CardTitle>
                {activeLens === 'growth' ? 'Market Opportunities' :
                 activeLens === 'cost' ? 'Efficiency Opportunities' :
                 activeLens === 'innovation' ? 'Technology Trends' : 'Valuation Drivers'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(insights.marketOpportunities || insights.efficiencyOpportunities || insights.technologyTrends || insights.valuationDrivers)?.map((item: string, i: number) => (
                  <div key={i} className="flex items-start space-x-2">
                    <ArrowRight className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Competitive Advantages / Benchmark Gaps / etc */}
          <Card>
            <CardHeader>
              <CardTitle>
                {activeLens === 'growth' ? 'Competitive Advantages' :
                 activeLens === 'cost' ? 'Benchmark Gaps' :
                 activeLens === 'innovation' ? 'Innovation Capabilities' : 'Multiple Enhancement'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {(insights.competitiveAdvantages || insights.benchmarkGaps || insights.innovationCapabilities || insights.multipleEnhancement)?.map((item: string, i: number) => (
                  <div key={i} className="flex items-start space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm">{item}</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Success Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Award className="w-5 h-5" />
              <span>Success Metrics</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {insights.successMetrics?.map((metric: string, i: number) => (
                <div key={i} className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-green-600" />
                    <span className="text-sm font-medium">{metric}</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  const renderRecommendations = () => (
    <div className="space-y-6">
      {/* Dynamic Recommendations by Lens */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Object.entries(data.dynamicRecommendations.lensBasedPriorities).map(([lensId, priorities]: [string, any]) => {
          const lens = data.strategicLenses.find((l: any) => l.lensId === lensId);
          const Icon = getLensIcon(lens?.icon);
          return (
            <Card key={lensId} className={activeLens === lensId ? 'ring-2 ring-blue-500' : ''}>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className={`w-6 h-6 rounded-lg bg-gradient-to-br ${getLensColor(lensId)} flex items-center justify-center`}>
                    <Icon className="w-3 h-3 text-white" />
                  </div>
                  <span>{lens?.name} Lens Priorities</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {priorities.map((priority: string, i: number) => (
                    <div key={i} className="flex items-start space-x-2 p-2 bg-gray-50 rounded">
                      <span className="text-xs bg-blue-100 text-blue-800 rounded-full w-5 h-5 flex items-center justify-center font-medium">
                        {i + 1}
                      </span>
                      <span className="text-sm">{priority}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <Eye className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Strategic Lens Engine</h1>
          </div>
          <p className="text-gray-600">
            Dynamic strategic analysis for {data.companyName} with multiple analytical perspectives
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Lens Overview</TabsTrigger>
            <TabsTrigger value="comparison">Lens Comparison</TabsTrigger>
            <TabsTrigger value="insights">Deep Insights</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderLensOverview()}
          </TabsContent>

          <TabsContent value="comparison">
            {renderLensComparison()}
          </TabsContent>

          <TabsContent value="insights">
            {renderLensInsights()}
          </TabsContent>

          <TabsContent value="recommendations">
            {renderRecommendations()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Value Leakage & Financial Impact
        </Button>
        <Button onClick={onNextSection}>
          Next: Best Practice Library
        </Button>
      </div>
    </div>
  );
}