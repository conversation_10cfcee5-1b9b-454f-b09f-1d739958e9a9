import { useState, useEffect } from "react";
import { Book<PERSON>pen, Star, Search, Filter, Building, Target, Zap, Users, Settings, Award, ArrowRight, CheckCircle, AlertTriangle, Clock, DollarSign } from "lucide-react";
import { useCompany } from "@/contexts/company-context";
import { <PERSON><PERSON> } from "@/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/ui/tabs";
import { Input } from "@/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/ui/select";
import { Separator } from "@/ui/separator";
import { Progress } from "@/ui/progress";

interface BestPracticeLibraryProps {
  onNextSection: () => void;
  onPrevSection: () => void;
  selectedLens: string;
}

interface BestPracticeData {
  companySymbol: string;
  companyName: string;
  libraryOverview: any;
  practiceCategories: any[];
  bestPractices: any[];
  implementationGuidance: any;
  joyceAgentPrompts: any;
}

export function BestPracticeLibrary({ 
  onNextSection, 
  onPrevSection, 
  selectedLens 
}: BestPracticeLibraryProps) {
  const { selectedCompany } = useCompany();
  const [data, setData] = useState<BestPracticeData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedApplicability, setSelectedApplicability] = useState("all");
  const [selectedPractice, setSelectedPractice] = useState<string | null>(null);

  useEffect(() => {
    const loadData = async () => {
      try {
        const response = await fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/best-practice-library.json`);
        const jsonData = await response.json();
        setData(jsonData);
      } catch (error) {
        console.error('Error loading best practice data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading best practice library...</p>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">Unable to load best practice library</p>
        </div>
      </div>
    );
  }

  const getApplicabilityColor = (applicability: string) => {
    switch (applicability) {
      case 'High': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'Simple': return 'bg-green-100 text-green-800';
      case 'Moderate': return 'bg-yellow-100 text-yellow-800';
      case 'Complex': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTimeHorizonColor = (timeHorizon: string) => {
    switch (timeHorizon) {
      case 'Quick Win': return 'bg-green-100 text-green-800';
      case 'Medium Term': return 'bg-yellow-100 text-yellow-800';
      case 'Long Term': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Leadership & Governance': return Target;
      case 'Technology & Innovation': return Zap;
      case 'Culture & People': return Users;
      case 'Operations & Efficiency': return Settings;
      case 'Integration & Synergies': return Building;
      case 'Customer Experience': return Star;
      default: return BookOpen;
    }
  };

  const filteredPractices = data.bestPractices.filter((practice: any) => {
    const matchesSearch = searchTerm === "" || 
      practice.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      practice.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
      practice.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = selectedCategory === "all" || practice.category === selectedCategory;
    const matchesApplicability = selectedApplicability === "all" || practice.applicability === selectedApplicability;
    
    return matchesSearch && matchesCategory && matchesApplicability;
  });

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Library Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-4 text-center">
            <BookOpen className="w-8 h-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.libraryOverview.totalPractices}</div>
            <div className="text-sm text-gray-600">Total Practices</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Building className="w-8 h-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">{data.libraryOverview.categories.length}</div>
            <div className="text-sm text-gray-600">Categories</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Star className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">18</div>
            <div className="text-sm text-gray-600">High Applicability</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="w-8 h-8 text-purple-600 mx-auto mb-2" />
            <div className="text-2xl font-bold">85%</div>
            <div className="text-sm text-gray-600">Relevance Score</div>
          </CardContent>
        </Card>
      </div>

      {/* Practice Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Practice Categories</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
            {data.practiceCategories.map((category: any, index: number) => {
              const Icon = getCategoryIcon(category.category);
              return (
                <div key={index} className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-all cursor-pointer"
                     onClick={() => setSelectedCategory(category.category)}>
                  <div className="flex items-start space-x-3">
                    <div className="p-2 bg-blue-50 rounded-lg">
                      <Icon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold mb-1">{category.category}</h4>
                      <p className="text-sm text-gray-600 mb-2">{category.description}</p>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">{category.practiceCount} practices</span>
                        <Badge className={getApplicabilityColor(category.averageApplicability)}>
                          {category.averageApplicability}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPracticeLibrary = () => (
    <div className="space-y-6">
      {/* Search and Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Search className="w-5 h-5" />
            <span>Search & Filter Practices</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Input
              placeholder="Search practices, companies..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {data.practiceCategories.map((cat: any) => (
                  <SelectItem key={cat.category} value={cat.category}>{cat.category}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedApplicability} onValueChange={setSelectedApplicability}>
              <SelectTrigger>
                <SelectValue placeholder="Applicability" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="High">High Applicability</SelectItem>
                <SelectItem value="Medium">Medium Applicability</SelectItem>
                <SelectItem value="Low">Low Applicability</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" onClick={() => { setSearchTerm(""); setSelectedCategory("all"); setSelectedApplicability("all"); }}>
              Clear Filters
            </Button>
          </div>
          <div className="mt-2 text-sm text-gray-600">
            Showing {filteredPractices.length} of {data.bestPractices.length} practices
          </div>
        </CardContent>
      </Card>

      {/* Practice Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredPractices.map((practice: any, index: number) => (
          <Card 
            key={index}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              selectedPractice === practice.practiceId ? 'ring-2 ring-blue-500' : ''
            }`}
            onClick={() => setSelectedPractice(selectedPractice === practice.practiceId ? null : practice.practiceId)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <CardTitle className="text-lg mb-2">{practice.title}</CardTitle>
                  <div className="text-sm text-gray-600 font-medium">{practice.company}</div>
                </div>
                <div className="flex flex-col space-y-2 ml-4">
                  <Badge className={getApplicabilityColor(practice.applicability)}>
                    {practice.applicability}
                  </Badge>
                  <Badge className={getComplexityColor(practice.implementationComplexity)}>
                    {practice.implementationComplexity}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-700 mb-4">{practice.description}</p>
              
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Clock className="w-4 h-4 text-gray-400" />
                  <Badge className={getTimeHorizonColor(practice.timeHorizon)} variant="outline">
                    {practice.timeHorizon}
                  </Badge>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-4 h-4 text-gray-400" />
                  <span className="font-medium">{practice.businessImpact?.financialBenefit || 'TBD'}</span>
                </div>
              </div>

              <div className="mt-4">
                <div className="flex items-center space-x-2 text-sm font-medium mb-2">
                  <Target className="w-4 h-4 text-blue-500" />
                  <span>Applicability Score</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Progress value={practice.relevanceToCVS?.similaritiesScore || 0} className="flex-1 h-2" />
                  <span className="text-sm font-bold">{practice.relevanceToCVS?.similaritiesScore || 0}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Selected Practice Details */}
      {selectedPractice && (
        <Card>
          <CardHeader>
            <CardTitle>Implementation Deep Dive: {data.bestPractices.find((p: any) => p.practiceId === selectedPractice)?.title}</CardTitle>
          </CardHeader>
          <CardContent>
            {(() => {
              const practice = data.bestPractices.find((p: any) => p.practiceId === selectedPractice);
              return (
                <Tabs defaultValue="elements" className="w-full">
                  <TabsList>
                    <TabsTrigger value="elements">Key Elements</TabsTrigger>
                    <TabsTrigger value="relevance">CVS Relevance</TabsTrigger>
                    <TabsTrigger value="implementation">Implementation</TabsTrigger>
                    <TabsTrigger value="impact">Business Impact</TabsTrigger>
                    <TabsTrigger value="risks">Risks</TabsTrigger>
                  </TabsList>
                  <TabsContent value="elements" className="space-y-3">
                    {practice?.keyElements?.map((element: string, i: number) => (
                      <div key={i} className="flex items-start space-x-2">
                        <CheckCircle className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{element}</span>
                      </div>
                    ))}
                  </TabsContent>
                  <TabsContent value="relevance" className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <h4 className="font-medium mb-2">Key Relevance Areas</h4>
                      {practice?.relevanceToCVS?.keyRelevance?.map((relevance: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2 mb-1">
                          <ArrowRight className="w-3 h-3 text-blue-500 mt-1 flex-shrink-0" />
                          <span className="text-sm">{relevance}</span>
                        </div>
                      ))}
                    </div>
                    <div className="p-3 bg-green-50 rounded-lg">
                      <h4 className="font-medium mb-2">Implementation Guidance</h4>
                      {practice?.relevanceToCVS?.implementationGuidance?.map((guidance: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2 mb-1">
                          <CheckCircle className="w-3 h-3 text-green-500 mt-1 flex-shrink-0" />
                          <span className="text-sm">{guidance}</span>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="implementation">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="p-3 border border-gray-200 rounded-lg text-center">
                        <DollarSign className="w-6 h-6 text-green-600 mx-auto mb-2" />
                        <div className="font-bold">{practice?.businessImpact?.financialBenefit}</div>
                        <div className="text-sm text-gray-600">Financial Benefit</div>
                      </div>
                      <div className="p-3 border border-gray-200 rounded-lg text-center">
                        <Clock className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                        <div className="font-bold">{practice?.businessImpact?.timeToValue}</div>
                        <div className="text-sm text-gray-600">Time to Value</div>
                      </div>
                      <div className="p-3 border border-gray-200 rounded-lg text-center">
                        <Target className="w-6 h-6 text-purple-600 mx-auto mb-2" />
                        <div className="font-bold">{practice?.implementationComplexity}</div>
                        <div className="text-sm text-gray-600">Complexity</div>
                      </div>
                    </div>
                  </TabsContent>
                  <TabsContent value="impact">
                    <div className="space-y-3">
                      <h4 className="font-medium">Success Metrics:</h4>
                      {practice?.businessImpact?.successMetrics?.map((metric: string, i: number) => (
                        <div key={i} className="flex items-center space-x-2 p-2 bg-green-50 rounded">
                          <Award className="w-4 h-4 text-green-600" />
                          <span className="text-sm">{metric}</span>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                  <TabsContent value="risks">
                    <div className="space-y-2">
                      {practice?.implementationRisks?.map((risk: string, i: number) => (
                        <div key={i} className="flex items-start space-x-2">
                          <AlertTriangle className="w-4 h-4 text-red-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{risk}</span>
                        </div>
                      ))}
                    </div>
                  </TabsContent>
                </Tabs>
              );
            })()
            }
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderImplementationGuidance = () => (
    <div className="space-y-6">
      {/* Prioritization Framework */}
      <Card>
        <CardHeader>
          <CardTitle>Prioritization Framework</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Evaluation Criteria</h4>
              <div className="space-y-3">
                {data.implementationGuidance.prioritizationFramework.criteria.map((criterion: any, i: number) => (
                  <div key={i} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <span className="text-sm font-medium">{criterion.criterion}</span>
                    <Badge variant="outline">{criterion.weight}% weight</Badge>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium mb-3">Implementation Phases</h4>
              <div className="space-y-3">
                {data.implementationGuidance.implementationPhases.map((phase: any, i: number) => (
                  <div key={i} className="p-3 border border-gray-200 rounded-lg">
                    <div className="flex justify-between items-center mb-2">
                      <span className="font-medium">{phase.phase}</span>
                      <Badge variant="outline">{phase.duration}</Badge>
                    </div>
                    <p className="text-sm text-gray-600">{phase.activities[0]}</p>
                    <div className="text-xs text-gray-500 mt-1">
                      +{phase.activities.length - 1} more activities
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      <div className="flex-1 p-6">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center space-x-3 mb-2">
            <BookOpen className="w-6 h-6 text-blue-600" />
            <h1 className="text-2xl font-bold text-gray-900">Best Practice Library</h1>
          </div>
          <p className="text-gray-600">
            Curated collection of proven practices and case studies relevant to {data.companyName}'s transformation
          </p>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">Library Overview</TabsTrigger>
            <TabsTrigger value="practices">Practice Catalog</TabsTrigger>
            <TabsTrigger value="guidance">Implementation Guidance</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            {renderOverview()}
          </TabsContent>

          <TabsContent value="practices">
            {renderPracticeLibrary()}
          </TabsContent>

          <TabsContent value="guidance">
            {renderImplementationGuidance()}
          </TabsContent>
        </Tabs>
      </div>
      
      {/* Navigation Footer */}
      <div className="flex justify-between items-center p-6 bg-white border-t border-gray-200">
        <Button 
          variant="outline" 
          onClick={onPrevSection}
        >
          Previous: Strategic Lens Engine
        </Button>
        <Button onClick={onNextSection}>
          Next: Recommendations & Roadmap
        </Button>
      </div>
    </div>
  );
}