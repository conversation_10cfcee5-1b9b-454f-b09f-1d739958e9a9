import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { ChevronRight, ChevronLeft, Lightbulb, TrendingUp } from "lucide-react";
import { Button } from "@/ui/button";
import { useState } from "react";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";

interface CustomerEngagementProps {
  onNextSection: () => void;
  onPrevSection: () => void;
}

export function CustomerEngagement({ onNextSection, onPrevSection }: CustomerEngagementProps) {
  const { selectedCompany } = useCompany();
  const { data: contentData, loading: contentLoading } = useCompanyData(selectedCompany, "customer-engagement");
  const [activeSegment, setActiveSegment] = useState("segment1");
  

  if (contentLoading) {
    return <div className="p-6">Loading...</div>;
  }

  if (!contentData) {
    return <div className="p-6">Failed to load content data</div>;
  }

  // Get current segment data
  const currentSegmentData = contentData.segmentData[activeSegment];
  
  // Use shared charts for all segments
  const sharedCharts = contentData.sharedCharts;
  
  // Get line colors for shared charts
  const lineColors = contentData.sharedLineColors.consumer;
  const lineKeys = Object.keys(lineColors);

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{contentData.customerEngagementOverview.title}</h2>
        <p className="text-gray-700 mb-6">
          {contentData.customerEngagementOverview.description}
        </p>

        {/* Charts Section - ALL 4 charts BEFORE tabs */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          {/* Customer Segmentation Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Segmentation</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="donut"
                data={sharedCharts.customerSegmentation.chartData}
                config={{
                  ...sharedCharts.customerSegmentation.chartData.reduce((acc: any, item: any) => {
                    acc[item.name] = { 
                      label: item.name,
                      color: item.color
                    };
                    return acc;
                  }, {})
                }}
                xKey="name"
                yKey="value"
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                chartProps={{ innerRadius: 45, outerRadius: 95 }}
              />
            </CardContent>
          </Card>

          {/* Churn Rate Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Churn Rate Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="line"
                data={sharedCharts.churnRateTrends.chartData}
                config={generateChartConfig(
                  sharedCharts.churnRateTrends.chartData,
                  lineKeys,
                  lineKeys.reduce((acc: any, key: string) => {
                    acc[key] = key.charAt(0).toUpperCase() + key.slice(1);
                    return acc;
                  }, {}),
                  lineColors
                )}
                xKey="month"
                yKey={lineKeys}
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
              />
            </CardContent>
          </Card>

          {/* Customer Lifetime Value */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Lifetime Value by Segment</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="bar"
                data={sharedCharts.customerLifetimeValue.chartData}
                config={generateChartConfig(
                  sharedCharts.customerLifetimeValue.chartData,
                  ["verizon", "benchmark"],
                  { verizon: "Verizon", benchmark: "Benchmark" },
                  contentData.sharedBarColors
                )}
                xKey="segment"
                yKey={["verizon", "benchmark"]}
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
              />
            </CardContent>
          </Card>

          {/* Customer Journey Satisfaction */}
          <Card>
            <CardHeader>
              <CardTitle>Customer Journey Satisfaction</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="line"
                data={sharedCharts.customerJourneySatisfaction.chartData}
                config={generateChartConfig(
                  sharedCharts.customerJourneySatisfaction.chartData,
                  ["current", "target"],
                  { current: "Current", target: "Target" },
                  contentData.sharedJourneyColors
                )}
                xKey="stage"
                yKey={["current", "target"]}
                height={300}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  yAxis: {
                    domain: [70, 100]
                  }
                }}
              />
            </CardContent>
          </Card>
        </div>

        {/* Segment Tabs - Left Aligned */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
            {contentData.segments.map((segment: any) => (
              <button
                key={segment.id}
                onClick={() => setActiveSegment(segment.id)}
                className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeSegment === segment.id
                    ? "bg-white text-gray-900 shadow-sm"
                    : "text-gray-500 hover:text-gray-700"
                }`}
              >
                {segment.label}
              </button>
            ))}
          </div>
        </div>

        {/* Tab Content - Segment-Specific Cards */}
        <div className="grid grid-cols-2 gap-8 mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <Lightbulb className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">Key Engagement Insights</h3>
              </div>
              <ul className="space-y-3">
                {currentSegmentData.keyInsights.map((insight: any, index: number) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-green-600 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{insight.text}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center space-x-2 mb-4">
                <TrendingUp className="w-5 h-5 text-blue-600" />
                <h3 className="font-semibold text-gray-900">Enhancement Opportunities</h3>
              </div>
              <ul className="space-y-3">
                {currentSegmentData.opportunities.map((opportunity: any, index: number) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2"></div>
                    <span className="text-sm text-gray-700">{opportunity.text}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Action Items */}
        <Card>
          <CardHeader>
            <CardTitle>Strategic Action Items</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {contentData.strategicActionItems.map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">{item.title}</h4>
                    <p className="text-sm text-gray-600">{item.description}</p>
                  </div>
                  <Badge variant={item.priority === 'HIGH' ? 'destructive' : 'secondary'}>{item.priority}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <Button onClick={onNextSection} className="flex items-center space-x-2">
          <span>Next Section</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    </div>
  );
}
