import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Badge } from "@/ui/badge";
import { Progress } from "@/ui/progress";
import { ChevronLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/ui/tabs";
import { ExecutionPriority } from "@shared/schema";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";
import { DataVisualization, generateChartConfig } from "@/components/ui/data-visualization";

interface ExecutionRoadmapProps {
  onPrevSection: () => void;
}

export function ExecutionRoadmap({ onPrevSection }: ExecutionRoadmapProps) {
  const { selectedCompany } = useCompany();
  const { data: roadmapData, loading: roadmapLoading } = useCompanyData(selectedCompany, "execution-roadmap");

  if (roadmapLoading) {
    return <div className="p-6">Loading execution roadmap...</div>;
  }

  if (!roadmapData) {
    return <div className="p-6">Failed to load execution roadmap data</div>;
  }

  // Use JSON data from the roadmap file
  const impactAssessmentData = roadmapData.impactAssessment || [];
  const roiProjectionData = roadmapData.roiProjection || [];
  const sizeOfPrizeData = roadmapData.sizeOfPrize || [];
  const scenarioModelingData = roadmapData.scenarioModeling || [];
  const phasedExecutionPlan = roadmapData.phasedExecutionPlan || {};
  const dependencies = roadmapData.dependencies || [];
  const risks = roadmapData.risks || [];
  const strategicInitiativeCards = roadmapData.strategicInitiativeCards || [];
  const implementationTimeline = roadmapData.implementationTimeline || [];

  return (
    <div className="p-6 space-y-8">
      <section>
        <h2 className="text-xl font-bold text-gray-900 mb-4">{roadmapData.title}</h2>
        <p className="text-gray-700 mb-6">
          {roadmapData.description}
        </p>

        {/* Charts Side by Side */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Initiative Impact Assessment Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Initiative Impact Assessment</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="composed"
                data={impactAssessmentData}
                config={generateChartConfig(
                  impactAssessmentData,
                  ["businessImpact", "implementationComplexity"],
                  { 
                    businessImpact: "Business Impact",
                    implementationComplexity: "Implementation Complexity"
                  },
                  { 
                    businessImpact: "#e74c3c",
                    implementationComplexity: "#2ecc71"
                  }
                )}
                xKey="name"
                yKey={["businessImpact", "implementationComplexity"]}
                height={280}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
                axes={{
                  secondaryYAxis: {
                    domain: [0, 10]
                  }
                }}
              />
            </CardContent>
          </Card>

          {/* Strategic ROI Projection Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Strategic ROI Projection</CardTitle>
            </CardHeader>
            <CardContent>
              <DataVisualization
                type="bar"
                data={roiProjectionData}
                config={generateChartConfig(
                  roiProjectionData,
                  ["actualROI", "projectedROI"],
                  { 
                    actualROI: "Actual ROI (%)",
                    projectedROI: "Projected ROI (%)"
                  },
                  { 
                    actualROI: "#e74c3c",
                    projectedROI: "#3498db"
                  }
                )}
                xKey="quarter"
                yKey={["actualROI", "projectedROI"]}
                height={280}
                tooltip={{ enabled: true }}
                legend={{ enabled: true, position: "bottom" }}
              />
              <p className="text-sm text-gray-600 mt-2">
                {roadmapData.roiTargetDescription}
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Tabbed Content */}
        <Tabs defaultValue="initiatives" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="initiatives">Strategic Initiatives</TabsTrigger>
            <TabsTrigger value="timeline">Implementation Timeline</TabsTrigger>
            <TabsTrigger value="dependencies">Dependencies & Risks</TabsTrigger>
          </TabsList>

          <TabsContent value="initiatives" className="space-y-6">
            {/* Strategic Initiatives with Priority Cards */}
            <div className="space-y-4">
              {strategicInitiativeCards.map((initiative, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${initiative.priority === 'High Priority' ? 'bg-blue-500' : 'bg-orange-500'}`}></div>
                        <h3 className="font-semibold">{initiative.title}</h3>
                      </div>
                      <Badge className={initiative.priorityClass}>{initiative.priority}</Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{initiative.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Size of the Prize Table */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Size of the Prize
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 font-medium">Strategic Initiative</th>
                        <th className="text-left py-2 font-medium">Financial Impact</th>
                        <th className="text-left py-2 font-medium">Time Horizon</th>
                        <th className="text-left py-2 font-medium">Confidence Level</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sizeOfPrizeData.map((row, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3">{row.initiative}</td>
                          <td className="py-3 text-green-600 font-medium">{row.financialImpact}</td>
                          <td className="py-3">{row.timeHorizon}</td>
                          <td className="py-3">{row.confidenceLevel}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-800 mb-2">{roadmapData.aggregateRoiPotential?.title}</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    {roadmapData.aggregateRoiPotential?.items?.map((item, index) => (
                      <li key={index}>{item}</li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>

            {/* Scenario Modeling */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Scenario Modeling
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 font-medium">Scenario</th>
                        <th className="text-left py-2 font-medium">Revenue Growth</th>
                        <th className="text-left py-2 font-medium">EBITDA Margin</th>
                        <th className="text-left py-2 font-medium">Cash Flow</th>
                        <th className="text-left py-2 font-medium">Enterprise Value</th>
                      </tr>
                    </thead>
                    <tbody>
                      {scenarioModelingData.map((row, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3 font-medium">{row.scenario}</td>
                          <td className="py-3">{row.revenueGrowth}</td>
                          <td className="py-3">{row.ebitdaMargin}</td>
                          <td className="py-3">{row.cashFlow}</td>
                          <td className="py-3">{row.enterpriseValue}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <p className="text-xs text-gray-500 mt-3">
                  {roadmapData.scenarioModelingAssumptions}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="timeline" className="space-y-6">
            {/* Implementation Timeline */}
            <div className="space-y-6">
              <div className="relative">
                {implementationTimeline.map((phase, index) => (
                  <div key={index} className="flex items-center space-x-4 mb-4">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <div className="font-medium text-blue-700 mb-1">{phase.quarter} - {phase.phase}</div>
                      {phase.items.map((item, itemIndex) => (
                        <div key={itemIndex} className={`text-sm text-gray-600 ${itemIndex === phase.items.length - 1 ? '' : 'mb-1'}`}>
                          {item}
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>

              {/* Size of the Prize Repeat */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    Size of the Prize
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left py-2 font-medium">Strategic Initiative</th>
                          <th className="text-left py-2 font-medium">Financial Impact</th>
                          <th className="text-left py-2 font-medium">Time Horizon</th>
                          <th className="text-left py-2 font-medium">Confidence Level</th>
                        </tr>
                      </thead>
                      <tbody>
                        {sizeOfPrizeData.map((row, index) => (
                          <tr key={index} className="border-b">
                            <td className="py-3">{row.initiative}</td>
                            <td className="py-3 text-green-600 font-medium">{row.financialImpact}</td>
                            <td className="py-3">{row.timeHorizon}</td>
                            <td className="py-3">{row.confidenceLevel}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Phased Execution Plan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Phased Execution Plan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {Object.entries(phasedExecutionPlan).map(([initiative, phases]) => (
                  <div key={initiative}>
                    <h4 className="font-semibold mb-3">{initiative}</h4>
                    <div className="space-y-3">
                      {Array.isArray(phases) && phases.map((phase, index) => (
                        <div key={index} className="flex justify-between items-center">
                          <div>
                            <div className="text-sm font-medium text-blue-600">{phase.quarter}</div>
                            <div className="text-sm">{phase.task}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm font-medium">Investment: {phase.investment}</div>
                            <div className="text-sm text-green-600">Impact: {phase.impact}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 text-sm text-gray-600 bg-gray-50 p-3 rounded">
                      Key Performance Indicators: KPIs vary by initiative - tracking progress and impact metrics.
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Cumulative Financial Impact */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  {roadmapData.cumulativeFinancialImpact?.title}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-700 mb-4">
                  {roadmapData.cumulativeFinancialImpact?.introText}
                </p>
                <ul className="space-y-2 text-sm">
                  {roadmapData.cumulativeFinancialImpact?.projections?.map((projection, index) => (
                    <li key={index} className="flex items-center gap-2">
                      <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                      <span>{projection}</span>
                    </li>
                  ))}
                </ul>
                <p className="text-xs text-gray-500 mt-4">
                  {roadmapData.cumulativeFinancialImpact?.assumptions || "Cumulative impact projections are based on phased investments, adoption rates, and market response. Actual results may vary depending on execution and external factors."}
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="dependencies" className="space-y-6">
            {/* Critical Dependencies */}
            <Card>
              <CardHeader>
                <CardTitle>Critical Dependencies</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {dependencies.map((dependency, index) => (
                  <div key={index} className="flex items-start gap-3">
                    <div className="w-3 h-3 bg-blue-500 rounded-full mt-1"></div>
                    <div>
                      <div className="font-medium">{dependency.name}</div>
                      <div className="text-sm text-gray-600 mb-2">{dependency.description}</div>
                      <div className="text-xs">
                        <span className={`font-medium ${dependency.priority === 'High' ? 'text-blue-600' : 'text-orange-600'}`}>
                          Priority: {dependency.priority}
                        </span>
                        <span className="ml-4 font-medium">Owner: {dependency.owner}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Key Implementation Risks */}
            <Card>
              <CardHeader>
                <CardTitle>Key Implementation Risks</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {risks.map((risk, index) => (
                  <div key={index} className={`border-l-4 ${risk.level === 'High' ? 'border-red-500' : 'border-orange-500'} pl-4`}>
                    <div className={`font-medium ${risk.level === 'High' ? 'text-red-600' : 'text-orange-600'}`}>
                      {risk.level}
                    </div>
                    <div className="font-medium">{risk.name}</div>
                    <div className="text-sm text-gray-600">{risk.description}</div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Size of the Prize Table (repeated) */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  Size of the Prize
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 font-medium">Strategic Initiative</th>
                        <th className="text-left py-2 font-medium">Financial Impact</th>
                        <th className="text-left py-2 font-medium">Time Horizon</th>
                        <th className="text-left py-2 font-medium">Confidence Level</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sizeOfPrizeData.map((row, index) => (
                        <tr key={index} className="border-b">
                          <td className="py-3">{row.initiative}</td>
                          <td className="py-3 text-green-600 font-medium">{row.financialImpact}</td>
                          <td className="py-3">{row.timeHorizon}</td>
                          <td className="py-3">{row.confidenceLevel}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </section>

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t border-gray-200">
        <Button variant="ghost" onClick={onPrevSection} className="flex items-center space-x-2">
          <ChevronLeft className="w-4 h-4" />
          <span>Previous Section</span>
        </Button>
        <div></div>
      </div>
    </div>
  );
}
