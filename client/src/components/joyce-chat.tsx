import { useState } from "react";
import { useJ<PERSON>ce<PERSON>hat } from "@/hooks/use-joyce-chat";
import { <PERSON>, <PERSON>Content, CardH<PERSON><PERSON>, CardTitle } from "@/ui/card";
import { Button } from "@/ui/button";
import { Input } from "@/ui/input";
import { ScrollArea } from "@/ui/scroll-area";
import { MessageCircle, Send, Bot, User } from "lucide-react";
import { cn } from "@/lib/utils";
import { useCompany } from "@/contexts/company-context";
import { MarkdownText } from "@/components/ui/markdown-text";

interface JoyceChatProps {
  mode?: "embedded" | "modal";
}

export function JoyceChat({ mode = "embedded" }: JoyceChatProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useJoyceChat();

  // In modal mode, always show the chat interface
  if (mode === "modal" || isOpen) {
    return (
      <div className={cn(
        "flex flex-col",
        mode === "modal" ? "h-[600px] w-full" : "w-full max-w-2xl max-h-[600px]"
      )}>
        {mode === "embedded" && (
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Bot className="h-5 w-5 text-blue-600" />
              <h3 className="font-semibold">Joyce AI Assistant</h3>
            </div>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsOpen(false)}
            >
              ✕
            </Button>
          </div>
        )}
        
        <Card className="flex-1 flex flex-col min-h-0">
          <CardContent className="flex-1 flex flex-col p-0 min-h-0">
            <div className="flex-1 overflow-y-auto max-h-[450px] p-4">
              <div className="space-y-4">
                {messages.length === 0 && (
                  <div className="flex items-center gap-2 p-3 rounded-lg bg-blue-50 border border-blue-200">
                    <Bot className="h-5 w-5 text-blue-600" />
                    <p className="text-sm text-blue-800">
                      Hi! I'm Joyce, your AI assistant for Rejoyce analytics. Ask me about your data, KPIs, or strategic insights.
                    </p>
                  </div>
                )}
                
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3 p-3 rounded-lg",
                      message.type === "user" 
                        ? "bg-gray-50 ml-8" 
                        : "bg-blue-50 mr-8"
                    )}
                  >
                    <div className="flex-shrink-0">
                      {message.type === "user" ? (
                        <User className="h-5 w-5 text-gray-600" />
                      ) : (
                        <Bot className="h-5 w-5 text-blue-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      {message.type === "ai" ? (
                        <MarkdownText>{message.content}</MarkdownText>
                      ) : (
                        <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                      )}
                    </div>
                  </div>
                ))}
                
                {isLoading && (
                  <div className="flex gap-3 p-3 rounded-lg bg-blue-50 mr-8">
                    <Bot className="h-5 w-5 text-blue-600" />
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <p className="text-sm text-blue-800">Joyce is thinking</p>
                        <div className="flex gap-1">
                          <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
                          <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '150ms' }}></div>
                          <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="p-4 border-t">
              <form onSubmit={handleSubmit} className="flex gap-2">
                <Input
                  value={input}
                  onChange={handleInputChange}
                  placeholder="Ask Joyce about your data..."
                  disabled={isLoading}
                  className="flex-1"
                />
                <Button type="submit" disabled={isLoading || !input.trim()}>
                  <Send className="h-4 w-4" />
                </Button>
              </form>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Collapsed state for embedded mode
  return (
    <Card className="w-full max-w-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-blue-600" />
          Joyce AI Assistant
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-muted-foreground mb-4">
          Get insights about your enterprise data and strategic recommendations.
        </p>
        <Button 
          onClick={() => setIsOpen(true)}
          className="w-full"
        >
          <MessageCircle className="h-4 w-4 mr-2" />
          Start Chat
        </Button>
      </CardContent>
    </Card>
  );
}