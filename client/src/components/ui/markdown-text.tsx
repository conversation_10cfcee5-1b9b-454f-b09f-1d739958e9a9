// Enhanced markdown renderer with list support
export function MarkdownText({ children }: { children: string }) {
  const formatText = (text: string) => {
    let formatted = text;
    
    // Handle headings
    formatted = formatted.replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold mt-4 mb-2">$1</h3>');
    formatted = formatted.replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold mt-4 mb-2">$1</h2>');
    formatted = formatted.replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold mt-4 mb-2">$1</h1>');
    
    // Handle bold and italic
    formatted = formatted.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>');
    formatted = formatted.replace(/\*(.*?)\*/g, '<em class="italic">$1</em>');
    
    // Handle inline code
    formatted = formatted.replace(/`(.*?)`/g, '<code class="bg-gray-100 px-1 rounded text-sm font-mono">$1</code>');
    
    // Split into blocks for list processing
    const blocks = formatted.split(/\n\s*\n/);
    const processedBlocks = blocks.map(block => {
      const lines = block.split('\n');
      
      // Check if this block is a list
      const isUnorderedList = lines.every(line => line.trim() === '' || line.match(/^\s*[-*+]\s/));
      const isOrderedList = lines.every(line => line.trim() === '' || line.match(/^\s*\d+\.\s/));
      
      if (isUnorderedList && lines.some(line => line.trim() !== '')) {
        const listItems = lines
          .filter(line => line.trim() !== '')
          .map(line => {
            const content = line.replace(/^\s*[-*+]\s/, '').trim();
            return `<li class="mb-1">${content}</li>`;
          })
          .join('');
        return `<ul class="list-disc list-inside space-y-1 my-2">${listItems}</ul>`;
      }
      
      if (isOrderedList && lines.some(line => line.trim() !== '')) {
        const listItems = lines
          .filter(line => line.trim() !== '')
          .map(line => {
            const content = line.replace(/^\s*\d+\.\s/, '').trim();
            return `<li class="mb-1">${content}</li>`;
          })
          .join('');
        return `<ol class="list-decimal list-inside space-y-1 my-2">${listItems}</ol>`;
      }
      
      // Regular paragraph
      if (block.trim()) {
        return `<p class="mb-2">${block.replace(/\n/g, '<br>')}</p>`;
      }
      
      return '';
    });
    
    return processedBlocks.join('');
  };

  return (
    <div 
      className="prose prose-sm max-w-none text-gray-900"
      dangerouslySetInnerHTML={{ __html: formatText(children) }}
    />
  );
}
