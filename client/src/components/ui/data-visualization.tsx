import * as React from "react"
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  ComposedChart,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  XAxis,
  YAxis,
  CartesianGrid,
  Cell,
  ResponsiveContainer,
} from "recharts"

import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  ChartConfig,
} from "@/ui/chart"
import { cn } from "@/lib/utils"

// Chart type definitions
export type ChartType = "line" | "bar" | "pie" | "donut" | "composed" | "radar" | "custom"

// Axis configuration
export interface ChartAxisConfig {
  domain?: [number | string, number | string]
  tickFormatter?: (value: any) => string
  label?: string
  hide?: boolean
}

// Tooltip configuration
export interface TooltipConfig {
  enabled?: boolean
  formatter?: (value: any, name: string, props: any) => React.ReactNode
  labelFormatter?: (label: string) => React.ReactNode
  position?: "top" | "topLeft" | "topRight" | "right" | "rightTop" | "rightBottom" | "bottom" | "bottomLeft" | "bottomRight" | "left" | "leftTop" | "leftBottom" | "center"
}

// Legend configuration
export interface LegendConfig {
  enabled?: boolean
  position?: "top" | "bottom" | "left" | "right"
  hideIcon?: boolean
}

// Data visualization component props
export interface DataVisualizationProps {
  // Chart configuration
  type: ChartType
  data: any[]
  config: ChartConfig
  
  // Layout & styling
  width?: number | string
  height?: number | string
  className?: string
  
  // Chart-specific configuration
  xKey?: string
  yKey?: string | string[]
  
  // Interactive features
  tooltip?: TooltipConfig
  legend?: LegendConfig
  
  // Advanced configuration
  axes?: {
    xAxis?: ChartAxisConfig
    yAxis?: ChartAxisConfig
    secondaryYAxis?: ChartAxisConfig
  }
  
  // Custom rendering
  customRenderer?: (props: {
    data: any[]
    config: ChartConfig
    width?: number | string
    height?: number | string
  }) => React.ReactNode
  
  // Additional chart-specific props
  chartProps?: Record<string, any>
}

// Default color palette matching existing system
const DEFAULT_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#EF4444", // Red
  "#F59E0B", // Amber
  "#14B8A6", // Teal
  "#8B5CF6", // Purple
  "#F97316", // Orange
  "#06B6D4", // Cyan
]

export function DataVisualization({
  type,
  data,
  config,
  width = "100%",
  height = 300,
  className,
  xKey = "period",
  yKey,
  tooltip = { enabled: true },
  legend = { enabled: false },
  axes = {},
  customRenderer,
  chartProps = {},
}: DataVisualizationProps) {
  // Handle custom renderer
  if (type === "custom" && customRenderer) {
    return (
      <div className={cn("w-full", className)}>
        {customRenderer({ data, config, width, height })}
      </div>
    )
  }

  // Determine the primary data key
  const primaryDataKey = React.useMemo(() => {
    if (typeof yKey === "string") return yKey
    if (Array.isArray(yKey) && yKey.length > 0) return yKey[0]
    
    // Fallback: find first numeric key in data
    if (data.length > 0) {
      const sampleItem = data[0]
      const numericKeys = Object.keys(sampleItem).filter(
        key => key !== xKey && typeof sampleItem[key] === "number"
      )
      return numericKeys[0]
    }
    
    return "value"
  }, [yKey, data, xKey])

  // Generate chart content based on type
  const renderChart = () => {
    const commonProps = {
      data,
      ...chartProps,
    }

    switch (type) {
      case "line":
        return (
          <LineChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey={xKey}
              domain={axes.xAxis?.domain}
              tickFormatter={axes.xAxis?.tickFormatter}
              hide={axes.xAxis?.hide}
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              domain={axes.yAxis?.domain}
              tickFormatter={axes.yAxis?.tickFormatter}
              hide={axes.yAxis?.hide}
              axisLine={false}
              tickLine={false}
            />
            {tooltip.enabled && (
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={tooltip.formatter}
                    labelFormatter={tooltip.labelFormatter}
                  />
                }
                position={tooltip.position}
              />
            )}
            {legend.enabled && (
              <ChartLegend
                content={
                  <ChartLegendContent 
                    hideIcon={legend.hideIcon}
                    verticalAlign={legend.position === "top" || legend.position === "bottom" ? legend.position : "bottom"}
                  />
                }
              />
            )}
            {Array.isArray(yKey) ? (
              yKey.map((key, index) => (
                <Line
                  key={key}
                  type="monotone"
                  dataKey={key}
                  stroke={`var(--color-${key})`}
                  strokeWidth={2}
                  dot={{ r: 4 }}
                />
              ))
            ) : (
              <Line
                type="monotone"
                dataKey={primaryDataKey}
                stroke={`var(--color-${primaryDataKey})`}
                strokeWidth={2}
                dot={{ r: 4 }}
              />
            )}
          </LineChart>
        )

      case "bar":
        return (
          <BarChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey={xKey}
              domain={axes.xAxis?.domain}
              tickFormatter={axes.xAxis?.tickFormatter}
              hide={axes.xAxis?.hide}
              axisLine={false}
              tickLine={false}
              angle={-45}
              textAnchor="end"
              height={60}
              interval={0}
            />
            <YAxis
              domain={axes.yAxis?.domain}
              tickFormatter={axes.yAxis?.tickFormatter}
              hide={axes.yAxis?.hide}
              axisLine={false}
              tickLine={false}
            />
            {tooltip.enabled && (
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={tooltip.formatter}
                    labelFormatter={tooltip.labelFormatter}
                  />
                }
                position={tooltip.position}
              />
            )}
            {legend.enabled && (
              <ChartLegend
                content={
                  <ChartLegendContent 
                    hideIcon={legend.hideIcon}
                    verticalAlign={legend.position === "top" || legend.position === "bottom" ? legend.position : "bottom"}
                  />
                }
              />
            )}
            {Array.isArray(yKey) ? (
              yKey.map((key) => (
                <Bar
                  key={key}
                  dataKey={key}
                  fill={`var(--color-${key})`}
                />
              ))
            ) : (
              <Bar
                dataKey={primaryDataKey}
                fill={`var(--color-${primaryDataKey})`}
              />
            )}
          </BarChart>
        )

      case "pie":
      case "donut":
        const defaultInnerRadius = type === "donut" ? 60 : 0
        const defaultOuterRadius = 80
        const pieInnerRadius = chartProps?.innerRadius !== undefined ? chartProps.innerRadius : defaultInnerRadius
        const pieOuterRadius = chartProps?.outerRadius !== undefined ? chartProps.outerRadius : defaultOuterRadius
        return (
          <PieChart {...commonProps}>
            {tooltip.enabled && (
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={tooltip.formatter}
                    labelFormatter={tooltip.labelFormatter}
                  />
                }
                position={tooltip.position}
              />
            )}
            {legend.enabled && (
              <ChartLegend
                content={
                  <ChartLegendContent 
                    hideIcon={legend.hideIcon}
                    verticalAlign={legend.position === "top" || legend.position === "bottom" ? legend.position : "bottom"}
                  />
                }
              />
            )}
            <Pie
              data={data}
              dataKey={primaryDataKey}
              nameKey={xKey}
              cx="50%"
              cy="50%"
              innerRadius={pieInnerRadius}
              outerRadius={pieOuterRadius}
              paddingAngle={1}
            >
              {data.map((_, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={DEFAULT_COLORS[index % DEFAULT_COLORS.length]} 
                />
              ))}
            </Pie>
          </PieChart>
        )

      case "composed":
        return (
          <ComposedChart {...commonProps}>
            <CartesianGrid strokeDasharray="3 3" className="stroke-muted" />
            <XAxis 
              dataKey={xKey}
              domain={axes.xAxis?.domain}
              tickFormatter={axes.xAxis?.tickFormatter}
              hide={axes.xAxis?.hide}
              axisLine={false}
              tickLine={false}
            />
            <YAxis
              domain={axes.yAxis?.domain}
              tickFormatter={axes.yAxis?.tickFormatter}
              hide={axes.yAxis?.hide}
              axisLine={false}
              tickLine={false}
            />
            {axes.secondaryYAxis && (
              <YAxis
                yAxisId="right"
                orientation="right"
                domain={axes.secondaryYAxis.domain}
                tickFormatter={axes.secondaryYAxis.tickFormatter}
                hide={axes.secondaryYAxis.hide}
                axisLine={false}
                tickLine={false}
              />
            )}
            {tooltip.enabled && (
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={tooltip.formatter}
                    labelFormatter={tooltip.labelFormatter}
                  />
                }
                position={tooltip.position}
              />
            )}
            {legend.enabled && (
              <ChartLegend
                content={
                  <ChartLegendContent 
                    hideIcon={legend.hideIcon}
                    verticalAlign={legend.position === "top" || legend.position === "bottom" ? legend.position : "bottom"}
                  />
                }
              />
            )}
            {/* Composed charts require manual configuration of bars/lines */}
            {Array.isArray(yKey) && yKey.map((key) => (
              <Bar
                key={key}
                dataKey={key}
                fill={`var(--color-${key})`}
              />
            ))}
          </ComposedChart>
        )

      case "radar":
        return (
          <RadarChart {...commonProps}>
            <PolarGrid />
            <PolarAngleAxis dataKey={xKey} />
            <PolarRadiusAxis 
              domain={axes.yAxis?.domain}
              tick={false}
              tickFormatter={axes.yAxis?.tickFormatter}
            />
            {tooltip.enabled && (
              <ChartTooltip 
                content={
                  <ChartTooltipContent 
                    formatter={tooltip.formatter}
                    labelFormatter={tooltip.labelFormatter}
                  />
                }
              />
            )}
            {legend.enabled && (
              <ChartLegend
                content={
                  <ChartLegendContent 
                    hideIcon={legend.hideIcon}
                    verticalAlign={legend.position === "top" || legend.position === "bottom" ? legend.position : "bottom"}
                  />
                }
              />
            )}
            {Array.isArray(yKey) ? (
              yKey.map((key) => (
                <Radar
                  key={key}
                  name={key}
                  dataKey={key}
                  stroke={`var(--color-${key})`}
                  fill={`var(--color-${key})`}
                  fillOpacity={0.3}
                  strokeWidth={2}
                />
              ))
            ) : (
              <Radar
                name={primaryDataKey}
                dataKey={primaryDataKey}
                stroke={`var(--color-${primaryDataKey})`}
                fill={`var(--color-${primaryDataKey})`}
                fillOpacity={0.3}
                strokeWidth={2}
              />
            )}
          </RadarChart>
        )

      default:
        return <div>Unsupported chart type: {type}</div>
    }
  }

  return (
    <div className={cn("w-full", className)}>
      <ChartContainer config={config} className="w-full" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          {renderChart()}
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  )
}

// Helper function to generate chart config from data
export function generateChartConfig(
  data: any[],
  keys: string[],
  labels?: Record<string, string>,
  colors?: Record<string, string>
): ChartConfig {
  const config: ChartConfig = {}

  keys.forEach((key, index) => {
    config[key] = {
      label: labels?.[key] || key.charAt(0).toUpperCase() + key.slice(1),
      color: colors?.[key] || DEFAULT_COLORS[index % DEFAULT_COLORS.length],
    }
  })

  return config
}