// Strategic lens configuration and data transformation logic
// This file defines how each strategic lens affects KPI weighting and content emphasis

export interface LensWeighting {
  kpiWeights: Record<string, number>;
  benchmarkPreference: 'growth' | 'cost' | 'innovation' | 'operational' | 'customer';
  narrativeEmphasis: string[];
}

export interface StrategicLensConfig {
  [lensId: string]: {
    enterpriseLayer: LensWeighting;
    productServiceLayer: LensWeighting;
    customerLayer: LensWeighting;
  };
}

export const strategicLensConfig: StrategicLensConfig = {
  'growth-expansion': {
    enterpriseLayer: {
      kpiWeights: {
        'Revenue Growth': 2.0,
        'Market Share': 1.8,
        'Revenue': 1.5,
        'EBITDA Margin': 1.0,
        'Free Cash Flow': 1.2,
        'Net Debt/EBITDA': 0.8
      },
      benchmarkPreference: 'growth',
      narrativeEmphasis: [
        'revenue expansion opportunities',
        'market share growth potential',
        'investment in growth initiatives',
        'scalability of business model'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Product Revenue Growth': 2.0,
        'Revenue per Product': 1.8,
        'Cross-sell Rate': 1.5,
        'Product Profitability': 1.2,
        'Innovation Pipeline': 1.0
      },
      benchmarkPreference: 'growth',
      narrativeEmphasis: [
        'revenue-generating product lines',
        'product portfolio expansion',
        'market penetration strategies'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'Customer Acquisition': 2.0,
        'Customer Expansion': 1.8,
        'CLTV': 1.8,
        'Churn Reduction': 1.5,
        'NPS': 1.2,
        'CAC': 1.0
      },
      benchmarkPreference: 'growth',
      narrativeEmphasis: [
        'customer acquisition strategies',
        'expansion revenue opportunities',
        'customer lifetime value optimization'
      ]
    }
  },
  'cost-optimization': {
    enterpriseLayer: {
      kpiWeights: {
        'EBITDA Margin': 2.0,
        'SG&A Efficiency': 1.8,
        'Operating Leverage': 1.5,
        'Free Cash Flow': 1.5,
        'Cost per Unit': 1.8,
        'Revenue Growth': 0.8
      },
      benchmarkPreference: 'cost',
      narrativeEmphasis: [
        'cost structure optimization',
        'operational efficiency gains',
        'margin improvement opportunities',
        'resource allocation efficiency'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Cost-to-Serve': 2.0,
        'Product Margin': 1.8,
        'Resource Utilization': 1.5,
        'Operational Efficiency': 1.8,
        'Process Automation': 1.5
      },
      benchmarkPreference: 'cost',
      narrativeEmphasis: [
        'product-level cost structures',
        'service delivery optimization',
        'resource allocation efficiency'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'CAC Efficiency': 2.0,
        'Retention Cost': 1.8,
        'Service Cost per Customer': 1.5,
        'Customer Profitability': 1.5,
        'Support Efficiency': 1.2
      },
      benchmarkPreference: 'cost',
      narrativeEmphasis: [
        'customer acquisition cost optimization',
        'retention cost efficiency',
        'service delivery cost management'
      ]
    }
  },
  'innovation-transformation': {
    enterpriseLayer: {
      kpiWeights: {
        'R&D ROI': 2.0,
        'Innovation Pipeline': 1.8,
        'Digital Transformation': 1.8,
        'Agility Score': 1.5,
        'Technology Investment': 1.5,
        'Traditional Metrics': 0.8
      },
      benchmarkPreference: 'innovation',
      narrativeEmphasis: [
        'R&D investment efficiency',
        'digital transformation progress',
        'innovation pipeline strength',
        'organizational agility'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Innovation Pipeline ROI': 2.0,
        'Time-to-Market': 1.8,
        'New Product Revenue': 1.8,
        'Digital Product Adoption': 1.5,
        'Product Innovation Score': 1.5
      },
      benchmarkPreference: 'innovation',
      narrativeEmphasis: [
        'product innovation pipeline',
        'speed to market capabilities',
        'digital transformation of products'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'New Feature Adoption': 2.0,
        'Innovation-driven CLTV': 1.8,
        'Digital Engagement': 1.5,
        'Technology Satisfaction': 1.5,
        'Early Adopter Rate': 1.2
      },
      benchmarkPreference: 'innovation',
      narrativeEmphasis: [
        'customer adoption of innovations',
        'digital engagement patterns',
        'value from new offerings'
      ]
    }
  },
  'operational-excellence': {
    enterpriseLayer: {
      kpiWeights: {
        'Process Efficiency': 2.0,
        'KPI Consistency': 1.8,
        'Execution Maturity': 1.8,
        'Quality Metrics': 1.5,
        'Lean Flow': 1.5,
        'Operational Variance': 0.5
      },
      benchmarkPreference: 'operational',
      narrativeEmphasis: [
        'process optimization opportunities',
        'execution capability maturity',
        'operational consistency',
        'quality improvement initiatives'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Lean Flow Metrics': 2.0,
        'Capacity Utilization': 1.8,
        'Process Automation': 1.5,
        'Quality Score': 1.8,
        'Delivery Efficiency': 1.5
      },
      benchmarkPreference: 'operational',
      narrativeEmphasis: [
        'lean manufacturing principles',
        'capacity optimization',
        'process standardization'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'Service Quality': 2.0,
        'Operational Impact on CX': 1.8,
        'Process Consistency': 1.5,
        'First Call Resolution': 1.5,
        'Service Level Agreement': 1.8
      },
      benchmarkPreference: 'operational',
      narrativeEmphasis: [
        'service quality excellence',
        'operational impact on customer experience',
        'process consistency in delivery'
      ]
    }
  },
  'customer-centricity': {
    enterpriseLayer: {
      kpiWeights: {
        'NPS Impact on Revenue': 2.0,
        'Customer-driven Revenue': 1.8,
        'Churn Impact': 1.5,
        'Customer Satisfaction': 1.8,
        'Customer-centric Investments': 1.5
      },
      benchmarkPreference: 'customer',
      narrativeEmphasis: [
        'customer satisfaction impact on business',
        'customer-driven revenue growth',
        'churn prevention strategies'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Customer Profitability': 2.0,
        'Product-Customer Fit': 1.8,
        'Customer Feedback Integration': 1.5,
        'Service Customization': 1.5,
        'User Experience Score': 1.8
      },
      benchmarkPreference: 'customer',
      narrativeEmphasis: [
        'customer profitability by product',
        'product-market fit optimization',
        'customer-driven product development'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'Customer Satisfaction': 2.0,
        'Customer Loyalty': 2.0,
        'CLTV': 1.8,
        'NPS': 1.8,
        'Customer Success Score': 1.5
      },
      benchmarkPreference: 'customer',
      narrativeEmphasis: [
        'customer satisfaction and loyalty',
        'lifetime value optimization',
        'customer success initiatives'
      ]
    }
  },
  'wildcard-ai': {
    enterpriseLayer: {
      kpiWeights: {
        // AI-driven weights would be dynamically calculated
        'Opportunity Score': 2.0,
        'Gap Analysis': 1.8,
        'ROI Potential': 1.5
      },
      benchmarkPreference: 'growth', // Default, would be AI-determined
      narrativeEmphasis: [
        'AI-identified opportunities',
        'data-driven insights',
        'performance gap analysis'
      ]
    },
    productServiceLayer: {
      kpiWeights: {
        'Dynamic Priority': 2.0
      },
      benchmarkPreference: 'growth',
      narrativeEmphasis: [
        'AI-recommended focus areas',
        'dynamic prioritization'
      ]
    },
    customerLayer: {
      kpiWeights: {
        'Risk-Opportunity Balance': 2.0
      },
      benchmarkPreference: 'customer',
      narrativeEmphasis: [
        'customer-linked opportunities',
        'risk mitigation priorities'
      ]
    }
  }
};


// Data transformation functions
export function applyStrategicLens(data: any, lensId: string, layer: 'enterprise' | 'productService' | 'customer'): any {
  const lensConfig = strategicLensConfig[lensId];
  if (!lensConfig) return data;

  const layerConfig = layer === 'enterprise' ? lensConfig.enterpriseLayer : 
                     layer === 'productService' ? lensConfig.productServiceLayer : 
                     lensConfig.customerLayer;

  // Transform KPI performance data with weighted emphasis
  if (data.coreKpiPerformance?.metrics) {
    data.coreKpiPerformance.metrics = data.coreKpiPerformance.metrics.map((metric: any) => {
      const lensWeight = layerConfig.kpiWeights[metric.indicator] || 1.0;
      
      return {
        ...metric,
        lensWeight: lensWeight,
        isEmphasized: lensWeight > 1.4
      };
    });
  }

  // Add lens-specific narrative emphasis
  if (data.strategicMetrics?.keyInsights) {
    data.strategicMetrics.keyInsights = data.strategicMetrics.keyInsights.map((insight: any) => {
      const lensRelevant = layerConfig.narrativeEmphasis.some(emphasis => 
        insight.description.toLowerCase().includes(emphasis.split(' ')[0])
      );
      
      return {
        ...insight,
        lensRelevant: lensRelevant,
        lensWeight: lensRelevant ? 1.5 : 1.0
      };
    });
  }

  // Add comprehensive context to the data
  data.lensContext = {
    selectedLens: lensId,
    layer: layer,
    emphasis: layerConfig.narrativeEmphasis,
    benchmarkPreference: layerConfig.benchmarkPreference
  };

  return data;
}

export function getLensDisplayName(lensId: string): string {
  const lensNames: Record<string, string> = {
    'growth-expansion': 'Growth & Expansion',
    'cost-optimization': 'Cost Optimization',
    'innovation-transformation': 'Innovation & Transformation',
    'operational-excellence': 'Operational Excellence',
    'customer-centricity': 'Customer-Centricity',
    'wildcard-ai': 'Wildcard (AI-Driven)'
  };
  return lensNames[lensId] || 'Default';
}

export function generateLensSpecificInsights(data: any, lensId: string): string[] {
  const lensConfig = strategicLensConfig[lensId];
  if (!lensConfig) return [];

  const insights: string[] = [];
  
  // Generate insights based on lens
  if (lensId === 'growth-expansion') {
    insights.push("To reach industry-leading growth rates, prioritize market expansion and product innovation.");
    insights.push("Focus on scalable revenue streams with high margin potential.");
  } else if (lensId === 'cost-optimization') {
    insights.push("Identify quick wins in operational efficiency to free up resources.");
    insights.push("Benchmark against top-quartile peers on SG&A efficiency.");
  } else if (lensId === 'innovation-transformation') {
    insights.push("Accelerate digital transformation initiatives with measurable ROI targets.");
    insights.push("Build organizational agility to respond to market changes rapidly.");
  } else if (lensId === 'operational-excellence') {
    insights.push("Standardize processes across functions to reduce variance.");
    insights.push("Implement lean methodologies to improve flow and efficiency.");
  } else if (lensId === 'customer-centricity') {
    insights.push("Address customer journey bottlenecks to improve satisfaction and retention.");
    insights.push("Align organizational metrics with customer success outcomes.");
  }

  return insights;
}