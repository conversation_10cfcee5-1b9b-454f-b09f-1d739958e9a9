import { MainLayout } from "@/components/layout/main-layout";
import { Header } from "@/components/layout/header";
import { Settings, User, Shield, Palette, Database } from "lucide-react";

export default function SettingsPage() {
  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        <Header 
          title="Settings & Preferences"
          subtitle="Customize your Rejoyce experience and manage account preferences"
        />
        
        <div className="flex-1 p-6 flex items-center justify-center">
          <div className="max-w-lg text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-gray-100 p-6 rounded-full">
                <Settings className="w-12 h-12 text-gray-600" />
              </div>
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Settings Coming Soon
            </h2>
            
            <p className="text-gray-600 mb-8">
              We're developing a comprehensive settings panel where you'll be able to customize 
              your dashboard preferences, manage account settings, and configure notifications.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <User className="w-5 h-5 text-blue-500" />
                  <span className="font-medium text-gray-900">Account</span>
                </div>
                <p className="text-sm text-gray-600">Profile settings and preferences</p>
              </div>
              
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <Palette className="w-5 h-5 text-purple-500" />
                  <span className="font-medium text-gray-900">Appearance</span>
                </div>
                <p className="text-sm text-gray-600">Theme and display options</p>
              </div>
              
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <Shield className="w-5 h-5 text-green-500" />
                  <span className="font-medium text-gray-900">Security</span>
                </div>
                <p className="text-sm text-gray-600">Password and access controls</p>
              </div>
              
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center space-x-3 mb-2">
                  <Database className="w-5 h-5 text-amber-500" />
                  <span className="font-medium text-gray-900">Data</span>
                </div>
                <p className="text-sm text-gray-600">Data sources and integrations</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}