import { MainLayout } from "@/components/layout/main-layout";
import { ComingSoon } from "@/components/ui/coming-soon";
import { Rocket } from "lucide-react";

export default function Action() {
  return (
    <MainLayout>
      <ComingSoon
        title="Action"
        description="Your AI-powered execution partner for insights, scenario modeling, and action recommendations. Context-aware based on the current page/report."
        icon={<Rocket className="w-10 h-10" />}
        features={[
          "Strategy Lab for scenario modeling and strategic planning",
          "Mine for data extraction and insight generation", 
          "Forge for blueprint creation and action planning",
          "Refinery for validation and optimization recommendations",
          "Context-aware AI analysis based on current report or page",
          "Integrated with Joyce AI for intelligent recommendations"
        ]}
        estimatedDate="Q2 2025"
        showBackButton={false}
      />
    </MainLayout>
  );
}