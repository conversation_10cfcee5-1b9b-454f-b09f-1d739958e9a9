import { MainLayout } from "@/components/layout/main-layout";
import { ComingSoon } from "@/components/ui/coming-soon";
import { Settings } from "lucide-react";

export default function ExecutionReadiness() {
  return (
    <MainLayout>
      <ComingSoon
        title="Readiness"
        description="Opens Execution Readiness Assessment for the selected company, with summary metrics, directional performance, and alignment benchmarks. Includes link to board-ready PDF/PPT export."
        icon={<Settings className="w-10 h-10" />}
        features={[
          "Comprehensive organizational readiness assessment",
          "Strategic alignment and capability gap analysis",
          "Performance benchmarking against industry standards",
          "Board-ready executive summary reports",
          "PDF and PowerPoint export capabilities",
          "Actionable recommendations for readiness improvement"
        ]}
        estimatedDate="Q1 2025"
        showBackButton={false}
      />
    </MainLayout>
  );
}