import { Sidebar } from "@/components/layout/sidebar";
import { <PERSON><PERSON> } from "@/components/layout/header";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/ui/card";
import { Input } from "@/ui/input";
import { Button } from "@/ui/button";
import { Send, Bot, User } from "lucide-react";
import { useJoyceChat } from "@/hooks/use-joyce-chat";
import ReactMarkdown from 'react-markdown';

export default function JoyceAgent() {
  const { messages, sendMessage, isLoading, input, handleInputChange, handleSubmit } = useJoyceChat();

  const handleQuickAction = (question: string) => {
    sendMessage(question);
  };

  return (
    <div className="min-h-screen flex bg-gray-50">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header 
          title="Joyce Agent - Strategic Intelligence Assistant"
          subtitle="AI-powered analysis and insights for strategic decision making"
          showRefresh={false}
          showPeriod={false}
        />
        
        <div className="flex-1 p-6">
          <div className="max-w-4xl mx-auto h-full flex flex-col">
            {/* Chat Interface */}
            <Card className="flex-1 flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Bot className="w-5 h-5 text-blue-600" />
                  <span>Joyce Strategic Intelligence Agent</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col">
                {/* Messages */}
                <div className="flex-1 overflow-y-auto space-y-4 mb-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.type === "user" ? "justify-end" : "justify-start"}`}
                    >
                      <div
                        className={`max-w-3xl rounded-lg px-4 py-3 ${
                          message.type === "user"
                            ? "bg-blue-600 text-white"
                            : "bg-gray-100 text-gray-900"
                        }`}
                      >
                        <div className="flex items-start space-x-2">
                          {message.type === "ai" && (
                            <Bot className="w-4 h-4 mt-1 text-blue-600" />
                          )}
                          {message.type === "user" && (
                            <User className="w-4 h-4 mt-1 text-white" />
                          )}
                          <div>
                            <div className={`text-sm ${message.type === "ai" ? "prose prose-sm max-w-none" : ""}`}>
                              {message.type === "ai" ? (
                                <ReactMarkdown>{message.content}</ReactMarkdown>
                              ) : (
                                message.content
                              )}
                            </div>
                            <p className={`text-xs mt-1 ${
                              message.type === "user" ? "text-blue-100" : "text-gray-500"
                            }`}>
                              {message.timestamp.toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex justify-start">
                      <div className="max-w-3xl rounded-lg px-4 py-3 bg-blue-50 text-blue-900">
                        <div className="flex items-start space-x-2">
                          <Bot className="w-4 h-4 mt-1 text-blue-600" />
                          <div className="flex items-center gap-2">
                            <span className="text-sm">Joyce is thinking</span>
                            <div className="flex gap-1">
                              <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '0ms' }}></div>
                              <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '150ms' }}></div>
                              <div className="w-1 h-1 bg-blue-600 rounded-full animate-pulse" style={{ animationDelay: '300ms' }}></div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Input */}
                <form onSubmit={handleSubmit} className="flex space-x-2">
                  <Input
                    placeholder="Ask Joyce about strategic performance..."
                    value={input}
                    onChange={handleInputChange}
                    className="flex-1"
                    disabled={isLoading}
                  />
                  <Button type="submit" disabled={!input.trim() || isLoading}>
                    <Send className="w-4 h-4" />
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleQuickAction("Can you analyze the key performance indicators and recent trends for our company?")}
              >
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Performance Analysis</h3>
                  <p className="text-sm text-gray-600">
                    Analyze key performance indicators and trends
                  </p>
                </CardContent>
              </Card>
              <Card 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleQuickAction("What are the most important strategic insights and recommendations for our market positioning?")}
              >
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Strategic Insights</h3>
                  <p className="text-sm text-gray-600">
                    Explore strategic recommendations and market positioning
                  </p>
                </CardContent>
              </Card>
              <Card 
                className="cursor-pointer hover:bg-gray-50 transition-colors"
                onClick={() => handleQuickAction("How does our company's performance compare against industry benchmarks and competitors?")}
              >
                <CardContent className="pt-6">
                  <h3 className="font-semibold text-gray-900 mb-2">Competitive Analysis</h3>
                  <p className="text-sm text-gray-600">
                    Compare performance against industry benchmarks
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
