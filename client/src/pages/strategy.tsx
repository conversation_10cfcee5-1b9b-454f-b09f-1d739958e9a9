import { usePara<PERSON>, useLocation } from "wouter";
import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Head<PERSON> } from "@/components/layout/header";
import { But<PERSON> } from "@/ui/button";
import { List, Filter, ChevronDown } from "lucide-react";
import { StrategicFocusCustomization } from "@/components/sei-report/strategic-focus-customization";
import { StrategyTableOfContents } from "@/components/strategy/table-of-contents";
import { ExecutiveSummaryDashboard } from "@/components/era-report/executive-summary-dashboard";
import { CompanyContextBenchmarkExplorer } from "@/components/era-report/company-context-benchmark-explorer";
import { ExecutionMaturityJoyScoreDeepDive } from "@/components/era-report/execution-maturity-joy-score-deep-dive";
import { AgilitySimulator } from "@/components/era-report/agility-simulator";
import { ValueLeakageFinancialImpact } from "@/components/era-report/value-leakage-financial-impact";
import { StrategicLensEngine } from "@/components/era-report/strategic-lens-engine";
import { RecommendationsRoadmap } from "@/components/era-report/recommendations-roadmap";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";

const tabs = [
  { id: "executive-summary-dashboard", label: "Executive Summary Dashboard", component: ExecutiveSummaryDashboard },
  { id: "company-context-benchmark-explorer", label: "Company Context & Benchmark Explorer", component: CompanyContextBenchmarkExplorer },
  { id: "execution-maturity-joy-score-deep-dive", label: "Execution Maturity (Joy Score) Deep Dive", component: ExecutionMaturityJoyScoreDeepDive },
  { id: "agility-simulator", label: "Agility Simulator", component: AgilitySimulator },
  { id: "value-leakage-financial-impact", label: "Value Leakage & Financial Impact", component: ValueLeakageFinancialImpact },
  { id: "strategic-lens-engine", label: "Strategic Lens Engine", component: StrategicLensEngine },
  { id: "recommendations-roadmap", label: "Recommendations & Roadmap", component: RecommendationsRoadmap },
];

export default function Strategy() {
  const { section } = useParams<{ section?: string }>();
  const [, setLocation] = useLocation();
  const { selectedCompany } = useCompany();
  const { data: strategyData } = useCompanyData(selectedCompany, "strategy/executive-summary-dashboard");
  const [showStrategicFocus, setShowStrategicFocus] = useState(false);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [selectedLens, setSelectedLens] = useState("readiness-optimization");
  
  // Default to executive-summary-dashboard if no section is provided or invalid section
  const activeTab = section && tabs.find(tab => tab.id === section) ? section : "executive-summary-dashboard";
  
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const ActiveComponent = tabs[currentTabIndex]?.component;

  const handleNextSection = () => {
    const nextIndex = Math.min(currentTabIndex + 1, tabs.length - 1);
    const nextSection = tabs[nextIndex].id;
    setLocation(`/strategy/${nextSection}`);
  };

  const handlePrevSection = () => {
    const prevIndex = Math.max(currentTabIndex - 1, 0);
    const prevSection = tabs[prevIndex].id;
    setLocation(`/strategy/${prevSection}`);
  };

  // Extract suggested questions for Joyce (limit to 4 from Performance category)
  const joyceSuggestedQuestions = (strategyData as any)?.joyceAgentPrompts?.suggestedQuestions?.[0]?.questions?.slice(0, 4) || [];

  return (
    <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
      <div className="flex flex-col h-screen">
        <Header 
          title="Strategic Readiness Diagnostic"
          subtitle="Strategic enterprise analysis and transformation planning"
        />
        
        {/* Report Controls */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex space-x-4">
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowTableOfContents(!showTableOfContents);
                  setShowStrategicFocus(false);
                }}
              >
                <List className="w-4 h-4" />
                <span>Table of Contents</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showTableOfContents ? 'rotate-180' : ''}`} />
              </Button>
              {showTableOfContents && (
                <StrategyTableOfContents
                  activeTab={activeTab}
                  onNavigate={(tabId) => setLocation(`/strategy/${tabId}`)}
                  onClose={() => setShowTableOfContents(false)}
                />
              )}
            </div>
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowStrategicFocus(!showStrategicFocus);
                  setShowTableOfContents(false);
                }}
              >
                <Filter className="w-4 h-4" />
                <span>Strategic Lens</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showStrategicFocus ? 'rotate-180' : ''}`} />
              </Button>
              {showStrategicFocus && (
                <StrategicFocusCustomization
                  selectedLens={selectedLens}
                  onLensChange={setSelectedLens}
                  onClose={() => setShowStrategicFocus(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-50">
          {ActiveComponent && (
            <ActiveComponent 
              onNextSection={handleNextSection}
              onPrevSection={handlePrevSection}
              selectedLens={selectedLens}
            />
          )}
        </div>
      </div>
    </MainLayout>
  );
}
