import { MainLayout } from "@/components/layout/main-layout";
import { Header } from "@/components/layout/header";
import { ERAKPICard } from "@/components/dashboard/era-kpi-card";
import { JoyceSummary } from "@/components/dashboard/joyce-summary";
import { useERAData } from "@/hooks/use-era-data";
import { useCompany } from "@/contexts/company-context";
import { transformERADataToHUD, transformSEIDataToHUD } from "@/utils/era-hud-data";
import { useState } from "react";
import { Button } from "@/ui/button";

type DashboardMode = "ERA" | "SEI";

export default function Dashboard() {
  const { selectedCompany, companyDisplayName } = useCompany();
  const { executiveDashboard, valueLeakage, companyContext, loading, error } = useERAData(selectedCompany);
  const [dashboardMode, setDashboardMode] = useState<DashboardMode>("ERA");

  // Transform data for HUD display based on mode
  const hudData = executiveDashboard && valueLeakage && companyContext ? 
    (dashboardMode === "ERA" 
      ? transformERADataToHUD(executiveDashboard, valueLeakage, companyContext, selectedCompany)
      : transformSEIDataToHUD(executiveDashboard, valueLeakage, companyContext, selectedCompany)
    ) : null;

  const joyceSuggestedQuestions = hudData?.joyceSummary.suggestedQuestions || [];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !hudData) {
    return (
      <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
        <div className="flex flex-col h-full">
          <Header 
            title={`${companyDisplayName} - ERA Dashboard`}
            subtitle="Execution Readiness Assessment"
          />
          <div className="flex-1 p-6 flex items-center justify-center">
            <div className="text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">ERA Data Not Available</h2>
              <p className="text-gray-600">No ERA assessment data found for {companyDisplayName}</p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
      <div className="flex flex-col h-full">
        <Header 
          title={`${companyDisplayName} - ${dashboardMode === "ERA" ? "Execution Readiness Assessment" : "Strategic Execution Intelligence"}`}
          subtitle={dashboardMode === "ERA" ? "Executive strategic dashboard with proprietary execution metrics" : "Comprehensive strategic intelligence with financial and execution insights"}
        />
        
        {/* ERA/SEI Toggle */}
        <div className="px-6 pt-2 pb-4">
          <div className="flex items-center space-x-1 bg-gray-100 rounded-lg p-1 w-fit">
            <Button
              variant={dashboardMode === "ERA" ? "default" : "ghost"}
              size="sm"
              onClick={() => setDashboardMode("ERA")}
              className="text-xs font-medium px-3 py-1.5"
            >
              ERA View
            </Button>
            <Button
              variant={dashboardMode === "SEI" ? "default" : "ghost"}
              size="sm"
              onClick={() => setDashboardMode("SEI")}
              className="text-xs font-medium px-3 py-1.5"
            >
              SEI View
            </Button>
          </div>
        </div>
        
        <div className="flex-1 p-6 space-y-8 overflow-auto">
          {/* Joyce Summary */}
          <JoyceSummary
            companyName={companyDisplayName}
            summary={hudData.joyceSummary.summary}
            highlights={hudData.joyceSummary.highlights}
            suggestedQuestions={hudData.joyceSummary.suggestedQuestions}
            onQuestionClick={(question) => {
              // This would integrate with Joyce agent
              console.log('Ask Joyce:', question);
            }}
          />

          {/* KPI Cards */}
          <section>
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              {dashboardMode === "ERA" ? "Execution Readiness Metrics" : "Strategic Performance Intelligence"}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {hudData.kpiCards.map((kpi) => (
                <ERAKPICard key={kpi.type} {...kpi} />
              ))}
            </div>
          </section>
        </div>
      </div>
    </MainLayout>
  );
}