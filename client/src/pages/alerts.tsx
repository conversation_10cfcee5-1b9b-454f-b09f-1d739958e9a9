import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Head<PERSON> } from "@/components/layout/header";
import { Card, CardContent } from "@/ui/card";
import { But<PERSON> } from "@/ui/button";
import { Badge } from "@/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/ui/dialog";
import { Bell, AlertTriangle, Info, CheckCircle, Clock, ExternalLink } from "lucide-react";
import { useAlerts, type Alert } from "@/hooks/use-alerts";
import { cn } from "@/lib/utils";
import { Link } from "wouter";

export default function Alerts() {
  const { alertsData, loading, error, markAsRead, markAllAsRead, unreadCount } = useAlerts();
  const [selectedAlert, setSelectedAlert] = useState<Alert | null>(null);
  const [filterSeverity, setFilterSeverity] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  const getSeverityIcon = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'high': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'medium': return <Info className="h-4 w-4 text-yellow-600" />;
      case 'low': return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getSeverityColor = (severity: Alert['severity']) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 border-red-200 text-red-800';
      case 'high': return 'bg-orange-100 border-orange-200 text-orange-800';
      case 'medium': return 'bg-yellow-100 border-yellow-200 text-yellow-800';
      case 'low': return 'bg-blue-100 border-blue-200 text-blue-800';
    }
  };

  const handleAlertClick = (alert: Alert) => {
    setSelectedAlert(alert);
    if (!alert.isRead) {
      markAsRead(alert.id);
    }
  };

  const filteredAlerts = alertsData?.alerts.filter(alert => {
    const severityMatch = filterSeverity === 'all' || alert.severity === filterSeverity;
    const typeMatch = filterType === 'all' || alert.type === filterType;
    return severityMatch && typeMatch;
  }) || [];

  if (loading) {
    return (
      <MainLayout>
        <div className="flex flex-col h-full">
          <Header title="Alerts & Notifications" subtitle="Loading alerts..." />
          <div className="flex-1 p-6">
            <div className="max-w-4xl mx-auto">
              <div className="animate-pulse space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout>
        <div className="flex flex-col h-full">
          <Header title="Alerts & Notifications" subtitle="Error loading alerts" />
          <div className="flex-1 p-6">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardContent className="p-6 text-center">
                  <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Failed to Load Alerts</h3>
                  <p className="text-gray-600">{error}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="flex flex-col h-full">
        <Header 
          title="Alerts & Notifications" 
          subtitle={`${unreadCount} unread notifications • Last updated ${new Date(alertsData?.lastUpdated || '').toLocaleString()}`}
        />
        
        <div className="flex-1 p-6">
          <div className="max-w-6xl mx-auto space-y-6">
            
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Alerts</p>
                      <p className="text-2xl font-bold">{alertsData?.alerts.length || 0}</p>
                    </div>
                    <Bell className="h-8 w-8 text-gray-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Unread</p>
                      <p className="text-2xl font-bold text-red-600">{unreadCount}</p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-400" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Critical</p>
                      <p className="text-2xl font-bold text-red-600">
                        {alertsData?.alerts.filter(a => a.severity === 'critical').length || 0}
                      </p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">High Priority</p>
                      <p className="text-2xl font-bold text-orange-600">
                        {alertsData?.alerts.filter(a => a.severity === 'high').length || 0}
                      </p>
                    </div>
                    <AlertTriangle className="h-8 w-8 text-orange-500" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Actions Bar */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h2 className="text-xl font-semibold">All Alerts</h2>
                {unreadCount > 0 && (
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={markAllAsRead}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Mark All Read
                  </Button>
                )}
              </div>
              
              {/* Filters */}
              <div className="flex items-center gap-2">
                <select 
                  value={filterSeverity} 
                  onChange={(e) => setFilterSeverity(e.target.value)}
                  className="text-sm border rounded px-2 py-1"
                >
                  <option value="all">All Severities</option>
                  <option value="critical">Critical</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
                
                <select 
                  value={filterType} 
                  onChange={(e) => setFilterType(e.target.value)}
                  className="text-sm border rounded px-2 py-1"
                >
                  <option value="all">All Types</option>
                  <option value="era">ERA</option>
                  <option value="sei">SEI</option>
                </select>
              </div>
            </div>

            {/* Alerts List */}
            <div className="space-y-3">
              {filteredAlerts.length === 0 ? (
                <Card>
                  <CardContent className="p-6 text-center">
                    <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-600 mb-2">No alerts found</h3>
                    <p className="text-gray-500">Try adjusting your filters or check back later.</p>
                  </CardContent>
                </Card>
              ) : (
                filteredAlerts.map((alert) => (
                  <Card 
                    key={alert.id}
                    className={cn(
                      "cursor-pointer transition-all hover:shadow-md",
                      !alert.isRead && "border-l-4 border-l-blue-500 bg-blue-50/30"
                    )}
                    onClick={() => handleAlertClick(alert)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-start gap-3 flex-1">
                          <div className="flex-shrink-0 mt-0.5">
                            {getSeverityIcon(alert.severity)}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-2">
                              <h3 className={cn(
                                "font-semibold text-gray-900 truncate",
                                !alert.isRead && "font-bold"
                              )}>
                                {alert.title}
                              </h3>
                              <div className="flex items-center gap-2 ml-4">
                                <Badge variant="outline" className={getSeverityColor(alert.severity)}>
                                  {alert.severity.toUpperCase()}
                                </Badge>
                                <Badge variant="secondary">
                                  {alert.type.toUpperCase()}
                                </Badge>
                              </div>
                            </div>
                            
                            <p className="text-gray-600 text-sm mb-2">{alert.description}</p>
                            
                            <div className="flex items-center gap-4 text-xs text-gray-500">
                              <span className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {new Date(alert.timestamp).toLocaleString()}
                              </span>
                              <span>Source: {alert.source}</span>
                              <span>Category: {alert.category}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Alert Detail Modal */}
      <Dialog open={!!selectedAlert} onOpenChange={() => setSelectedAlert(null)}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          {selectedAlert && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  {getSeverityIcon(selectedAlert.severity)}
                  {selectedAlert.title}
                </DialogTitle>
              </DialogHeader>
              
              <div className="space-y-4 mt-4">
                <div className="flex items-center gap-2">
                  <Badge className={getSeverityColor(selectedAlert.severity)}>
                    {selectedAlert.severity.toUpperCase()}
                  </Badge>
                  <Badge variant="secondary">
                    {selectedAlert.type.toUpperCase()}
                  </Badge>
                  <span className="text-sm text-gray-500">
                    {new Date(selectedAlert.timestamp).toLocaleString()}
                  </span>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Description</h4>
                  <p className="text-gray-700">{selectedAlert.description}</p>
                </div>

                {selectedAlert.sourceUrl && (
                  <div>
                    <h4 className="font-semibold mb-2">Source</h4>
                    <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{selectedAlert.source}</p>
                          <p className="text-sm text-gray-600">Category: {selectedAlert.category}</p>
                        </div>
                        <a 
                          href={selectedAlert.sourceUrl} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                        >
                          <ExternalLink className="h-4 w-4 mr-1" />
                          View Source
                        </a>
                      </div>
                    </div>
                  </div>
                )}

                {selectedAlert.metrics && (
                  <div>
                    <h4 className="font-semibold mb-2">Metrics Impact</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">KPI:</span> {selectedAlert.metrics.kpiName}
                        </div>
                        <div>
                          <span className="text-gray-600">Current Value:</span> {selectedAlert.metrics.currentValue}
                        </div>
                        <div>
                          <span className="text-gray-600">Previous Value:</span> {selectedAlert.metrics.previousValue}
                        </div>
                        <div>
                          <span className="text-gray-600">Change:</span> 
                          <span className={cn(
                            "ml-1 font-medium",
                            selectedAlert.metrics.change?.startsWith('+') ? "text-green-600" : "text-red-600"
                          )}>
                            {selectedAlert.metrics.change}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {selectedAlert.rootCause && (
                  <div>
                    <h4 className="font-semibold mb-2">Root Cause Analysis</h4>
                    <p className="text-gray-700 bg-orange-50 p-3 rounded-lg border border-orange-200">
                      {selectedAlert.rootCause}
                    </p>
                  </div>
                )}

                {selectedAlert.recommendedAction && (
                  <div>
                    <h4 className="font-semibold mb-2">Recommended Action</h4>
                    <p className="text-gray-700 bg-blue-50 p-3 rounded-lg border border-blue-200">
                      {selectedAlert.recommendedAction}
                    </p>
                  </div>
                )}

                {(selectedAlert.joyScoreImpact || selectedAlert.fogScoreImpact) && (
                  <div>
                    <h4 className="font-semibold mb-2">Score Impact</h4>
                    <div className="flex gap-4">
                      {selectedAlert.joyScoreImpact && (
                        <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                          <span className="text-sm text-gray-600">Joy Score Impact:</span>
                          <span className={cn(
                            "ml-2 font-medium",
                            selectedAlert.joyScoreImpact.startsWith('+') ? "text-green-600" : "text-red-600"
                          )}>
                            {selectedAlert.joyScoreImpact}
                          </span>
                        </div>
                      )}
                      {selectedAlert.fogScoreImpact && (
                        <div className="bg-orange-50 p-3 rounded-lg border border-orange-200">
                          <span className="text-sm text-gray-600">Fog Score Impact:</span>
                          <span className={cn(
                            "ml-2 font-medium",
                            selectedAlert.fogScoreImpact.startsWith('+') ? "text-red-600" : "text-green-600"
                          )}>
                            {selectedAlert.fogScoreImpact}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {selectedAlert.deepLink && (
                  <div className="flex justify-end pt-4 border-t">
                    <Link href={selectedAlert.deepLink}>
                      <Button onClick={() => setSelectedAlert(null)}>
                        <ExternalLink className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}