import { useParams, useLocation } from "wouter";
import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Header } from "@/components/layout/header";
import { Button } from "@/ui/button";
import { List, Filter, ChevronDown } from "lucide-react";
import { StrategicFocusCustomization } from "@/components/sei-report/strategic-focus-customization";
import { SEIToolboxTableOfContents } from "@/components/sei-toolbox/table-of-contents";
import { BestPracticeLibrary } from "@/components/era-report/best-practice-library";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";

const tabs = [
  { id: "best-practice-library", label: "Best Practice Library", component: BestPracticeLibrary },
];

export default function SEIToolbox() {
  const { section } = useParams<{ section?: string }>();
  const [, setLocation] = useLocation();
  const { selectedCompany } = useCompany();
  const { data: seiToolboxData } = useCompanyData(selectedCompany, "sei-toolbox/best-practice-library");
  const [showStrategicFocus, setShowStrategicFocus] = useState(false);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [selectedLens, setSelectedLens] = useState("readiness-optimization");
  
  // Default to best-practice-library if no section is provided or invalid section
  const activeTab = section && tabs.find(tab => tab.id === section) ? section : "best-practice-library";
  
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const ActiveComponent = tabs[currentTabIndex]?.component;

  const handleNextSection = () => {
    const nextIndex = Math.min(currentTabIndex + 1, tabs.length - 1);
    const nextSection = tabs[nextIndex].id;
    setLocation(`/sei-toolbox/${nextSection}`);
  };

  const handlePrevSection = () => {
    const prevIndex = Math.max(currentTabIndex - 1, 0);
    const prevSection = tabs[prevIndex].id;
    setLocation(`/sei-toolbox/${prevSection}`);
  };

  // Extract suggested questions for Joyce (limit to 4 from Performance category)
  const joyceSuggestedQuestions = seiToolboxData?.joyceAgentPrompts?.suggestedQuestions?.[0]?.questions?.slice(0, 4) || [];

  return (
    <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
      <div className="flex flex-col h-screen">
        <Header 
          title="SEI Toolbox"
          subtitle="Strategic Enterprise Intelligence Toolbox - Best Practices and Implementation Resources"
        />
        
        {/* Report Controls */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex space-x-4">
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowTableOfContents(!showTableOfContents);
                  setShowStrategicFocus(false);
                }}
              >
                <List className="w-4 h-4" />
                <span>Table of Contents</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showTableOfContents ? 'rotate-180' : ''}`} />
              </Button>
              {showTableOfContents && (
                <SEIToolboxTableOfContents
                  activeTab={activeTab}
                  onNavigate={(tabId) => setLocation(`/sei-toolbox/${tabId}`)}
                  onClose={() => setShowTableOfContents(false)}
                />
              )}
            </div>
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowStrategicFocus(!showStrategicFocus);
                  setShowTableOfContents(false);
                }}
              >
                <Filter className="w-4 h-4" />
                <span>Strategic Lens</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showStrategicFocus ? 'rotate-180' : ''}`} />
              </Button>
              {showStrategicFocus && (
                <StrategicFocusCustomization
                  selectedLens={selectedLens}
                  onLensChange={setSelectedLens}
                  onClose={() => setShowStrategicFocus(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-50">
          {ActiveComponent && (
            <ActiveComponent 
              onNextSection={handleNextSection}
              onPrevSection={handlePrevSection}
              selectedLens={selectedLens}
            />
          )}
        </div>
      </div>
    </MainLayout>
  );
}