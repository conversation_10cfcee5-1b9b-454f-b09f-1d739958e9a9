import { Card, CardContent, CardDescription, CardHeader } from "@/ui/card";
import LoginButton from "@/components/auth/login-button";

export default function Login() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <img 
              src="/images/rejoyce-logo--black.png" 
              alt="Rejoyce" 
              className="h-12 w-auto"
            />
          </div>
          <CardDescription className="text-center">
            Strategic Execution Intelligence Platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <LoginButton />
        </CardContent>
      </Card>
    </div>
  );
}
