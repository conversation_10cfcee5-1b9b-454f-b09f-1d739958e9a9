import { useParams, useLocation } from "wouter";
import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Header } from "@/components/layout/header";
import { But<PERSON> } from "@/ui/button";
import { List, Filter, ChevronDown } from "lucide-react";
import { StrategicFocusCustomization } from "@/components/sei-report/strategic-focus-customization";
import { SEILabTableOfContents } from "@/components/sei-lab/table-of-contents";
import { InteractiveQAScenarioPlanner } from "@/components/era-report/interactive-qa-scenario-planner";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";

const tabs = [
  { id: "interactive-qa-scenario-planner", label: "Interactive Q&A and Scenario Planner", component: InteractiveQAScenarioPlanner },
];

export default function SEILab() {
  const { section } = useParams<{ section?: string }>();
  const [, setLocation] = useLocation();
  const { selectedCompany } = useCompany();
  const { data: seiLabData } = useCompanyData(selectedCompany, "sei-lab/interactive-qa-scenario-planner");
  const [showStrategicFocus, setShowStrategicFocus] = useState(false);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [selectedLens, setSelectedLens] = useState("readiness-optimization");
  
  // Default to interactive-qa-scenario-planner if no section is provided or invalid section
  const activeTab = section && tabs.find(tab => tab.id === section) ? section : "interactive-qa-scenario-planner";
  
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const ActiveComponent = tabs[currentTabIndex]?.component;

  const handleNextSection = () => {
    const nextIndex = Math.min(currentTabIndex + 1, tabs.length - 1);
    const nextSection = tabs[nextIndex].id;
    setLocation(`/sei-lab/${nextSection}`);
  };

  const handlePrevSection = () => {
    const prevIndex = Math.max(currentTabIndex - 1, 0);
    const prevSection = tabs[prevIndex].id;
    setLocation(`/sei-lab/${prevSection}`);
  };

  // Extract suggested questions for Joyce (limit to 4 from Performance category)
  const joyceSuggestedQuestions = (seiLabData as any)?.joyceAgentPrompts?.suggestedQuestions?.[0]?.questions?.slice(0, 4) || [];

  return (
    <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
      <div className="flex flex-col h-screen">
        <Header 
          title="SEI Lab"
          subtitle="Strategic Enterprise Intelligence Laboratory - Advanced Analytics and Simulation Tools"
        />
        
        {/* Report Controls */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex space-x-4">
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowTableOfContents(!showTableOfContents);
                  setShowStrategicFocus(false);
                }}
              >
                <List className="w-4 h-4" />
                <span>Table of Contents</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showTableOfContents ? 'rotate-180' : ''}`} />
              </Button>
              {showTableOfContents && (
                <SEILabTableOfContents
                  activeTab={activeTab}
                  onNavigate={(tabId) => setLocation(`/sei-lab/${tabId}`)}
                  onClose={() => setShowTableOfContents(false)}
                />
              )}
            </div>
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowStrategicFocus(!showStrategicFocus);
                  setShowTableOfContents(false);
                }}
              >
                <Filter className="w-4 h-4" />
                <span>Strategic Lens</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showStrategicFocus ? 'rotate-180' : ''}`} />
              </Button>
              {showStrategicFocus && (
                <StrategicFocusCustomization
                  selectedLens={selectedLens}
                  onLensChange={setSelectedLens}
                  onClose={() => setShowStrategicFocus(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-50">
          {ActiveComponent && (
            <ActiveComponent 
              onNextSection={handleNextSection}
              onPrevSection={handlePrevSection}
              selectedLens={selectedLens}
            />
          )}
        </div>
      </div>
    </MainLayout>
  );
}
