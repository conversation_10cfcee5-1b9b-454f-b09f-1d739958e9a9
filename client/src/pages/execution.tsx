import { useParams, useLocation } from "wouter";
import { useState } from "react";
import { MainLayout } from "@/components/layout/main-layout";
import { Head<PERSON> } from "@/components/layout/header";
import { <PERSON><PERSON> } from "@/ui/button";
import { List, Filter, ChevronDown } from "lucide-react";
import { StrategicFocusCustomization } from "@/components/sei-report/strategic-focus-customization";
import { ExecutionTableOfContents } from "@/components/execution/table-of-contents";
import { ExecutiveSummary } from "@/components/sei-report/executive-summary";
import { EnterpriseLayer } from "@/components/sei-report/enterprise-layer";
import { ProductsServices } from "@/components/sei-report/products-services";
import { CustomerEngagement } from "@/components/sei-report/customer-engagement";
import { OrganizationalMaturity } from "@/components/sei-report/organizational-maturity";
import { ExecutionRoadmap } from "@/components/sei-report/execution-roadmap";
import { useCompanyData } from "@/hooks/use-company-data";
import { useCompany } from "@/contexts/company-context";

const tabs = [
  { id: "executive-summary", label: "Executive Summary", component: ExecutiveSummary },
  { id: "enterprise-layer", label: "Enterprise Layer", component: EnterpriseLayer },
  { id: "products-services", label: "Products & Services", component: ProductsServices },
  { id: "customer-engagement", label: "Customer Engagement", component: CustomerEngagement },
  { id: "organizational-maturity", label: "Organizational Maturity", component: OrganizationalMaturity },
  { id: "execution-roadmap", label: "Execution Roadmap", component: ExecutionRoadmap },
];

export default function Execution() {
  const { section } = useParams<{ section?: string }>();
  const [, setLocation] = useLocation();
  const { selectedCompany } = useCompany();
  const { data: productsServicesData } = useCompanyData(selectedCompany, "products-services");
  const [activeJourneyStage, setActiveJourneyStage] = useState("summary");
  const [showStrategicFocus, setShowStrategicFocus] = useState(false);
  const [showTableOfContents, setShowTableOfContents] = useState(false);
  const [selectedLens, setSelectedLens] = useState("growth-expansion");
  
  // Default to executive-summary if no section is provided or invalid section
  const activeTab = section && tabs.find(tab => tab.id === section) ? section : "executive-summary";
  
  const currentTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const ActiveComponent = tabs[currentTabIndex]?.component;

  const handleNextSection = () => {
    const nextIndex = Math.min(currentTabIndex + 1, tabs.length - 1);
    const nextSection = tabs[nextIndex].id;
    setLocation(`/execution/${nextSection}`);
  };

  const handlePrevSection = () => {
    const prevIndex = Math.max(currentTabIndex - 1, 0);
    const prevSection = tabs[prevIndex].id;
    setLocation(`/execution/${prevSection}`);
  };

  // Contextual questions based on products-services data  
  const joyceSuggestedQuestions = productsServicesData ? [
    "How is our Consumer Wireless segment performing versus Business Wireless?",
    "What growth opportunities do you see in our product portfolio?", 
    "Which services have the highest margin potential?",
    "How can we better monetize our 5G network investments?"
  ] : [
    "What are our key strategic initiatives?",
    "How is our product portfolio positioned competitively?",
    "What are the main growth drivers for our business?",
    "Which market segments offer the best opportunities?"
  ];

  return (
    <MainLayout joyceSuggestedQuestions={joyceSuggestedQuestions}>
      <div className="flex flex-col h-screen">
        <Header 
          title="Strategic Execution Playbook"
          subtitle="Comprehensive performance analysis and strategic execution intelligence"
        />
        
        {/* Report Controls */}
        <div className="bg-white px-6 py-4 border-b border-gray-200">
          <div className="flex space-x-4">
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowTableOfContents(!showTableOfContents);
                  setShowStrategicFocus(false);
                }}
              >
                <List className="w-4 h-4" />
                <span>Table of Contents</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showTableOfContents ? 'rotate-180' : ''}`} />
              </Button>
              {showTableOfContents && (
                <ExecutionTableOfContents
                  activeTab={activeTab}
                  onNavigate={(tabId) => setLocation(`/execution/${tabId}`)}
                  onClose={() => setShowTableOfContents(false)}
                />
              )}
            </div>
            <div className="relative">
              <Button 
                variant="outline" 
                className="flex items-center space-x-2"
                onClick={() => {
                  setShowStrategicFocus(!showStrategicFocus);
                  setShowTableOfContents(false);
                }}
              >
                <Filter className="w-4 h-4" />
                <span>Strategic Lens</span>
                <ChevronDown className={`w-4 h-4 transition-transform ${showStrategicFocus ? 'rotate-180' : ''}`} />
              </Button>
              {showStrategicFocus && (
                <StrategicFocusCustomization
                  selectedLens={selectedLens}
                  onLensChange={setSelectedLens}
                  onClose={() => setShowStrategicFocus(false)}
                />
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto bg-gray-50">
          {ActiveComponent && (
            <ActiveComponent 
              onNextSection={handleNextSection}
              onPrevSection={handlePrevSection}
              selectedLens={selectedLens}
              {...(activeTab === "products-services" ? { activeJourneyStage } : {})}
            />
          )}
        </div>
      </div>
    </MainLayout>
  );
}