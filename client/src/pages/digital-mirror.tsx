import { useState, useEffect } from "react";
import { DigitalMirrorView } from "@/components/digital-mirror/digital-mirror-view";
import { adaptCompanyDataToMirrorKPIs } from "@/adapters/digital-mirror-adapter";
import { useCompany } from "@/contexts/company-context";
import { MirrorKPIData } from "@/components/digital-mirror/mirror-kpi-card";
import { MainLayout, useJoyce } from "@/components/layout/main-layout";

function DigitalMirrorContent() {
  const { selectedCompany } = useCompany();
  const [kpis, setKpis] = useState<MirrorKPIData[]>([]);
  const [loading, setLoading] = useState(true);
  const { openJoyce } = useJoyce();

  useEffect(() => {
    const loadDigitalMirrorData = async () => {
      if (!selectedCompany) return;
      
      try {
        setLoading(true);
        
        // Load Digital Mirror data files
        const [enterpriseResponse, productsResponse, customerResponse, execResponse] = await Promise.all([
          fetch(`/data/companies/${selectedCompany.toLowerCase()}/enterprise-layer.json`),
          fetch(`/data/companies/${selectedCompany.toLowerCase()}/products-services.json`),
          fetch(`/data/companies/${selectedCompany.toLowerCase()}/customer-engagement.json`),
          fetch(`/data/companies/${selectedCompany.toLowerCase()}/era-report/executive-summary-dashboard.json`)
        ]);

        if (!enterpriseResponse.ok || !productsResponse.ok || !customerResponse.ok) {
          throw new Error('Failed to load company data');
        }

        const [enterpriseData, productsData, customerData, eraExecDashboard] = await Promise.all([
          enterpriseResponse.json(),
          productsResponse.json(), 
          customerResponse.json(),
          execResponse.ok ? execResponse.json() : Promise.resolve(undefined)
        ]);

        // Transform existing data into Digital Mirror KPI format (attach trend data from ERA exec dashboard when available)
        const transformedKPIs = adaptCompanyDataToMirrorKPIs(
          enterpriseData,
          productsData,
          customerData,
          selectedCompany,
          eraExecDashboard
        );

        setKpis(transformedKPIs);
      } catch (error) {
        console.error('Error loading Digital Mirror data:', error);
        setKpis([]); // Fallback to empty state
      } finally {
        setLoading(false);
      }
    };

    loadDigitalMirrorData();
  }, [selectedCompany]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <DigitalMirrorView kpis={kpis} onJoyceOpen={openJoyce} />;
}

export default function DigitalMirror() {
  return (
    <MainLayout>
      <DigitalMirrorContent />
    </MainLayout>
  );
}
