export interface CompanyInfo {
  code: string;
  displayName: string;
  fullName: string;
}

export class CompanyService {
  private static instance: CompanyService;
  private availableCompanies: string[] = [];

  private constructor() {}

  static getInstance(): CompanyService {
    if (!CompanyService.instance) {
      CompanyService.instance = new CompanyService();
    }
    return CompanyService.instance;
  }

  async fetchAvailableCompanies(): Promise<string[]> {
    try {
      // Use the new API endpoint to dynamically discover companies
      const response = await fetch('/api/companies/list');
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const availableCompanies = await response.json();
      
      // If no companies found, default to vz
      if (availableCompanies.length === 0) {
        availableCompanies.push('vz');
      }

      this.availableCompanies = availableCompanies;
      return availableCompanies;
    } catch (error) {
      console.error('Error fetching available companies:', error);
      // Fallback to checking for vz directory if API fails
      try {
        const fallbackResponse = await fetch('/data/companies/vz/executive-summary.json');
        if (fallbackResponse.ok) {
          return ['vz'];
        }
      } catch (fallbackError) {
        // Continue to default fallback
      }
      return ['vz']; // Default fallback
    }
  }

  getCompanyDisplayName(companyCode: string): string {
    // Just return the company code in uppercase - truly dynamic
    return companyCode.toUpperCase();
  }

  async getCompanyFullName(companyCode: string): Promise<string> {
    try {
      const response = await fetch(`/data/companies/${companyCode.toLowerCase()}/executive-summary.json`);
      if (response.ok) {
        const data = await response.json();
        return data.companyName || companyCode.toUpperCase();
      }
    } catch (error) {
      console.error(`Error fetching company name for ${companyCode}:`, error);
    }
    return companyCode.toUpperCase();
  }

  async searchCompanies(query: string): Promise<CompanyInfo[]> {
    const available = await this.fetchAvailableCompanies();
    
    if (!query.trim()) {
      const results = await Promise.all(available.map(async code => ({
        code,
        displayName: this.getCompanyDisplayName(code),
        fullName: await this.getCompanyFullName(code)
      })));
      return results;
    }

    // For search, we need to get full names first to search through them
    const companiesWithNames = await Promise.all(available.map(async code => ({
      code,
      displayName: this.getCompanyDisplayName(code),
      fullName: await this.getCompanyFullName(code)
    })));

    const filtered = companiesWithNames.filter(company => {
      const searchQuery = query.toLowerCase();
      
      return (
        company.code.toLowerCase().includes(searchQuery) ||
        company.displayName.toLowerCase().includes(searchQuery) ||
        company.fullName.toLowerCase().includes(searchQuery)
      );
    });

    return filtered;
  }
}