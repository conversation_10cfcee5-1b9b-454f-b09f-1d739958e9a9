import { ERAKPICardProps, ContextualLabel } from "@/components/dashboard/era-kpi-card";

interface ERAHUDData {
  kpiCards: ERAKPICardProps[];
  joyceSummary: {
    summary: string;
    highlights: Array<{
      text: string;
      type: "positive" | "negative" | "neutral";
    }>;
    suggestedQuestions: string[];
  };
}

export function transformERADataToHUD(
  executiveDashboard: any,
  valueLeakage: any,
  companyContext: any,
  companySymbol: string
): ERAHUDData {
  const companyDisplayName = getCompanyDisplayName(companySymbol);
  
  // Extract key metrics for calculations
  const joyScore = executiveDashboard?.readinessSnapshot?.joyScore;
  const agilityLevel = executiveDashboard?.readinessSnapshot?.agilityLevel;
  const totalLeakage = valueLeakage?.valueLeakageOverview?.totalLeakage;
  const recoverablePortion = valueLeakage?.valueLeakageOverview?.recoverablePortion;
  const marketCap = companyContext?.companyProfile?.overview?.marketCap;
  const revenue = companyContext?.companyProfile?.overview?.revenue2024;

  // Calculate derived metrics
  const enterpriseValue = marketCap || "N/A";
  const executionAlpha = recoverablePortion?.amount || totalLeakage?.value || "N/A";
  const investabilityScore = calculateInvestabilityScore(companyContext, executiveDashboard);
  const valueAtRisk = totalLeakage?.value || "N/A";

  const kpiCards: ERAKPICardProps[] = [
    {
      type: "enterprise-value",
      title: "Enterprise Value",
      value: formatCurrency(enterpriseValue),
      contextualLabel: "watchlist" as ContextualLabel,
      definition: "Total company value (equity + debt - cash) reflecting market confidence",
      benchmark: {
        text: "Market-based valuation",
        status: "at"
      },
      fogScore: {
        level: "high",
        description: "Based on real-time market data"
      },
      onClick: () => navigateToSection("company-context")
    },
    {
      type: "execution-alpha",
      title: "Execution Alpha", 
      value: formatCurrency(executionAlpha),
      contextualLabel: "opportunity" as ContextualLabel,
      definition: "Estimated value uplift if company executed at best-in-class levels",
      benchmark: {
        text: `~${calculatePercentOfEV(executionAlpha, enterpriseValue)}% of current EV`,
        status: "below"
      },
      fogScore: {
        level: "medium",
        description: "Model-derived estimate with uncertainty range"
      },
      onClick: () => navigateToSection("value-leakage")
    },
    {
      type: "joy-score",
      title: "Joy Score",
      value: joyScore?.value?.toString() || "N/A",
      unit: `/ ${joyScore?.maxValue || 5}`,
      contextualLabel: determineJoyScoreLabel(joyScore),
      trend: {
        direction: "flat" as const,
        period: "vs last quarter"
      },
      definition: "Overall execution maturity index measuring organizational capability",
      benchmark: {
        text: `Industry avg: ${joyScore?.industryAverage || "N/A"}`,
        status: joyScore && joyScore.value < joyScore.industryAverage ? "below" : "at"
      },
      fogScore: {
        level: "medium",
        description: "Based on structured surveys and benchmarks"
      },
      onClick: () => navigateToSection("execution-maturity")
    },
    {
      type: "strategic-velocity",
      title: "Strategic Velocity Index",
      value: agilityLevel?.score?.toString() || "N/A", 
      unit: "/ 5.0",
      contextualLabel: determineAgilityLabel(agilityLevel),
      definition: "Speed at which organization executes strategic initiatives",
      benchmark: {
        text: agilityLevel?.benchmarkComparison || "Peer comparison",
        status: "below"
      },
      fogScore: {
        level: "medium", 
        description: "Qualitative assessment with quantitative benchmarks"
      },
      onClick: () => navigateToSection("agility-simulator")
    },
    {
      type: "investability-score",
      title: "Investability Score",
      value: investabilityScore.value,
      unit: "/ 100",
      contextualLabel: investabilityScore.label,
      definition: "Composite index of attractiveness to investors",
      benchmark: {
        text: investabilityScore.benchmark,
        status: investabilityScore.status
      },
      fogScore: {
        level: "high",
        description: "Based on audited financials and established benchmarks"
      },
      onClick: () => navigateToSection("company-context")
    },
    {
      type: "value-at-risk",
      title: "Value-at-Risk", 
      value: formatCurrency(valueAtRisk),
      contextualLabel: "risk" as ContextualLabel,
      definition: "Estimated enterprise value at stake from execution risks",
      benchmark: {
        text: `${totalLeakage?.percentage || "N/A"} of revenue`,
        status: "above"
      },
      fogScore: {
        level: "medium",
        description: "Scenario modeling with probability ranges"
      },
      onClick: () => navigateToSection("value-leakage")
    }
  ];

  // Generate Joyce summary
  const joyceSummary = generateJoyceSummary(
    companyDisplayName,
    executiveDashboard,
    valueLeakage,
    companyContext
  );

  return {
    kpiCards,
    joyceSummary
  };
}

export function transformSEIDataToHUD(
  executiveDashboard: any,
  valueLeakage: any,
  companyContext: any,
  companySymbol: string
): ERAHUDData {
  const companyDisplayName = getCompanyDisplayName(companySymbol);
  
  // Extract key metrics for calculations
  const joyScore = executiveDashboard?.readinessSnapshot?.joyScore;
  const agilityLevel = executiveDashboard?.readinessSnapshot?.agilityLevel;
  const totalLeakage = valueLeakage?.valueLeakageOverview?.totalLeakage;
  const recoverablePortion = valueLeakage?.valueLeakageOverview?.recoverablePortion;
  const marketCap = companyContext?.companyProfile?.overview?.marketCap;
  const revenue = companyContext?.companyProfile?.overview?.revenue2024;
  const prevRevenue = companyContext?.companyProfile?.overview?.revenue2023;
  
  // Calculate derived metrics
  const enterpriseValue = marketCap || "N/A";
  const executionAlpha = recoverablePortion?.amount || totalLeakage?.value || "N/A";
  const investabilityScore = calculateInvestabilityScore(companyContext, executiveDashboard);
  const valueAtRisk = totalLeakage?.value || "N/A";
  const revenueGrowth = calculateRevenueGrowth(revenue, prevRevenue);
  const investabilityGrade = calculateInvestabilityGrade(investabilityScore.value);

  const kpiCards: ERAKPICardProps[] = [
    // Top Row: Performance & Execution Health
    {
      type: "investability-score",
      title: "Investability Grade",
      value: investabilityGrade.grade,
      unit: `(${investabilityScore.value}/100)`,
      contextualLabel: investabilityGrade.label,
      trend: {
        direction: "flat",
        period: "vs last quarter"
      },
      benchmark: {
        text: investabilityScore.benchmark,
        status: investabilityScore.status
      },
      definition: "Composite indicator of company attractiveness to investors, blending growth, profitability, leverage, and execution health",
      fogScore: {
        level: "high",
        description: "Based on audited financial fundamentals"
      },
      onClick: () => navigateToSection("investor-benchmarks")
    },
    {
      type: "joy-score",
      title: "Joy Score",
      value: joyScore?.value?.toString() || "N/A",
      unit: `/ ${joyScore?.maxValue || 5}`,
      contextualLabel: determineJoyScoreLabel(joyScore),
      trend: {
        direction: "up",
        value: "+0.2",
        period: "vs last quarter"
      },
      benchmark: {
        text: `Industry avg: ${joyScore?.industryAverage || "N/A"}`,
        status: joyScore && joyScore.value < joyScore.industryAverage ? "below" : "above"
      },
      definition: "Execution maturity index measuring organizational capability across leadership, strategy, people, process, and technology",
      fogScore: {
        level: "medium",
        description: "Based on surveys and benchmarks"
      },
      onClick: () => navigateToSection("execution-maturity")
    },
    {
      type: "enterprise-value",
      title: "Revenue Growth",
      value: revenueGrowth.value,
      unit: revenueGrowth.unit,
      contextualLabel: revenueGrowth.label,
      trend: {
        direction: revenueGrowth.direction,
        period: "YoY"
      },
      benchmark: {
        text: revenueGrowth.benchmark,
        status: revenueGrowth.status
      },
      definition: "Year-over-year revenue growth rate showing top-line performance vs industry",
      fogScore: {
        level: "high",
        description: "Based on audited financial results"
      },
      onClick: () => navigateToSection("revenue-analysis")
    },
    // Bottom Row: Enterprise Value & Risk Outlook
    {
      type: "enterprise-value",
      title: "Enterprise Value",
      value: formatCurrency(enterpriseValue),
      contextualLabel: "watchlist" as ContextualLabel,
      trend: {
        direction: "up",
        value: "+3%",
        period: "vs last quarter"
      },
      benchmark: {
        text: "Market-based valuation",
        status: "at"
      },
      definition: "Total company value (market cap + debt - cash) reflecting market confidence in the enterprise",
      fogScore: {
        level: "high",
        description: "Based on real-time market data"
      },
      onClick: () => navigateToSection("enterprise-valuation")
    },
    {
      type: "execution-alpha",
      title: "Execution Alpha", 
      value: formatCurrency(executionAlpha),
      unit: `(≈${calculatePercentOfEV(executionAlpha, enterpriseValue)}% of EV)`,
      contextualLabel: "opportunity" as ContextualLabel,
      trend: {
        direction: "up",
        value: "+$5B",
        period: "vs last quarter"
      },
      benchmark: {
        text: "Value upside if execution gaps closed",
        status: "below"
      },
      definition: "Estimated value gain if company executed at peak potential - the gap between current valuation and optimal execution",
      fogScore: {
        level: "medium",
        description: "Model-derived estimate with uncertainty range"
      },
      onClick: () => navigateToSection("value-opportunities")
    },
    {
      type: "value-at-risk",
      title: "Value-at-Risk", 
      value: formatCurrency(valueAtRisk),
      unit: `(≈${calculateRiskPercentOfEV(valueAtRisk, enterpriseValue)}% of EV)`,
      contextualLabel: "risk" as ContextualLabel,
      trend: {
        direction: "up",
        value: "+$3B",
        period: "vs last quarter"
      },
      benchmark: {
        text: "Enterprise value at risk from execution risks",
        status: "above"
      },
      definition: "Estimated portion of enterprise value that could be lost if top execution risks materialize",
      fogScore: {
        level: "medium",
        description: "Scenario-based estimate with uncertainty"
      },
      onClick: () => navigateToSection("risk-analysis")
    }
  ];

  // Generate SEI-focused Joyce summary
  const joyceSummary = generateSEIJoyceSummary(
    companyDisplayName,
    executiveDashboard,
    valueLeakage,
    companyContext,
    revenueGrowth,
    investabilityGrade
  );

  return {
    kpiCards,
    joyceSummary
  };
}

function getCompanyDisplayName(companySymbol: string): string {
  const companyMap: { [key: string]: string } = {
    'acn': 'Accenture',
    'cvs': 'CVS Health',
    'ma': 'Mastercard', 
    'vz': 'Verizon'
  };
  return companyMap[companySymbol.toLowerCase()] || companySymbol.toUpperCase();
}

function formatCurrency(value: string): string {
  if (!value || value === "N/A") return "N/A";
  
  // Extract numeric value and format appropriately
  const numericValue = value.replace(/[^0-9.]/g, '');
  const parsed = parseFloat(numericValue);
  
  if (isNaN(parsed)) return value;
  
  if (parsed >= 1000) {
    return `$${(parsed / 1000).toFixed(1)}T`;
  } else if (parsed >= 1) {
    return `$${parsed.toFixed(1)}B`;
  } else {
    return `$${(parsed * 1000).toFixed(0)}M`;
  }
}

function calculatePercentOfEV(executionAlpha: string, enterpriseValue: string): string {
  const alphaNum = parseFloat(executionAlpha.replace(/[^0-9.]/g, ''));
  const evNum = parseFloat(enterpriseValue.replace(/[^0-9.]/g, ''));
  
  if (isNaN(alphaNum) || isNaN(evNum) || evNum === 0) return "N/A";
  
  return ((alphaNum / evNum) * 100).toFixed(0);
}

function determineJoyScoreLabel(joyScore: any): ContextualLabel {
  if (!joyScore) return "watchlist";
  
  const value = joyScore.value;
  const industryAvg = joyScore.industryAverage || 3.0;
  
  if (value >= 4.0) return "strength";
  if (value < 2.5) return "risk";
  if (value < industryAvg) return "watchlist";
  return "opportunity";
}

function determineAgilityLabel(agilityLevel: any): ContextualLabel {
  if (!agilityLevel) return "watchlist";
  
  const score = agilityLevel.score;
  
  if (score >= 4.0) return "strength";
  if (score < 2.5) return "risk"; 
  if (score < 3.0) return "watchlist";
  return "opportunity";
}

function calculateInvestabilityScore(companyContext: any, executiveDashboard: any): {
  value: string;
  label: ContextualLabel;
  benchmark: string;
  status: "above" | "below" | "at";
} {
  // Calculate based on available competitive metrics and execution data
  if (!companyContext?.competitorAnalysis?.competitiveMetrics && !executiveDashboard?.readinessSnapshot) {
    return {
      value: "N/A",
      label: "watchlist",
      benchmark: "No data available",
      status: "at"
    };
  }

  let score = 50; // Base score
  
  // Factor in Joy Score (execution maturity)
  const joyScore = executiveDashboard?.readinessSnapshot?.joyScore;
  if (joyScore?.value) {
    score += (joyScore.value / joyScore.maxValue) * 25; // Up to 25 points
  }
  
  // Factor in competitive position from metrics
  const metrics = companyContext?.competitorAnalysis?.competitiveMetrics;
  if (metrics) {
    // Look for revenue growth, EBITDA margin, customer satisfaction
    const revenueGrowthMetric = metrics.find((m: any) => m.metric.includes("Revenue Growth"));
    const marginMetric = metrics.find((m: any) => m.metric.includes("EBITDA"));
    const customerMetric = metrics.find((m: any) => m.metric.includes("Customer"));
    
    if (revenueGrowthMetric) {
      const companyValue = parseFloat(revenueGrowthMetric[companyContext.companySymbol.toLowerCase()] || "0");
      const industryAvg = parseFloat(revenueGrowthMetric.industry_avg || "0");
      if (companyValue > industryAvg) score += 10;
      else if (companyValue < industryAvg * 0.5) score -= 10;
    }
    
    if (marginMetric) {
      const companyValue = parseFloat(marginMetric[companyContext.companySymbol.toLowerCase()] || "0");
      const industryAvg = parseFloat(marginMetric.industry_avg || "0");
      if (companyValue > industryAvg) score += 10;
      else if (companyValue < industryAvg * 0.8) score -= 5;
    }
  }
  
  score = Math.min(100, Math.max(0, Math.round(score)));
  
  return {
    value: score.toString(),
    label: score >= 75 ? "strength" : score >= 60 ? "watchlist" : "risk",
    benchmark: score >= 75 ? "Top quartile" : score >= 50 ? "3rd quartile" : "Bottom quartile",
    status: score >= 70 ? "above" : score >= 50 ? "at" : "below"
  };
}

function generateJoyceSummary(
  companyName: string,
  executiveDashboard: any,
  valueLeakage: any,
  companyContext: any
): {
  summary: string;
  highlights: Array<{ text: string; type: "positive" | "negative" | "neutral" }>;
  suggestedQuestions: string[];
} {
  const joyScore = executiveDashboard?.readinessSnapshot?.joyScore;
  const totalLeakage = valueLeakage?.valueLeakageOverview?.totalLeakage;
  
  const summary = `Welcome back! ${companyName} continues to navigate its execution transformation. Recent analysis shows steady progress in organizational maturity with a Joy Score of ${joyScore?.value || "N/A"}, while significant value optimization opportunities remain. The execution gap analysis reveals approximately ${totalLeakage?.amount || "substantial"} in potential value uplift through improved execution across key operational areas.`;

  const highlights = [
    {
      text: `Joy Score holds steady at ${joyScore?.value || "N/A"} - structured processes in place but room for advancement`,
      type: "neutral" as const
    },
    {
      text: `${totalLeakage?.amount || "Significant"} execution alpha opportunity identified`,
      type: "positive" as const  
    },
    {
      text: "Strategic velocity remains moderate - agility improvements needed for competitive advantage",
      type: "neutral" as const
    }
  ];

  const suggestedQuestions = [
    `How does our Joy Score compare to industry leaders?`,
    `What's driving our current execution alpha opportunity?`,
    `Which initiatives would deliver the fastest ROI?`
  ];

  return {
    summary,
    highlights,
    suggestedQuestions
  };
}

function calculateRevenueGrowth(current: string, previous: string): {
  value: string;
  unit: string;
  label: ContextualLabel;
  direction: "up" | "down" | "flat";
  benchmark: string;
  status: "above" | "below" | "at";
} {
  if (!current || !previous || current === "N/A" || previous === "N/A") {
    return {
      value: "N/A",
      unit: "",
      label: "watchlist",
      direction: "flat",
      benchmark: "No historical data",
      status: "at"
    };
  }

  const currentNum = parseFloat(current.replace(/[^0-9.]/g, ''));
  const previousNum = parseFloat(previous.replace(/[^0-9.]/g, ''));
  
  if (isNaN(currentNum) || isNaN(previousNum) || previousNum === 0) {
    return {
      value: "N/A",
      unit: "",
      label: "watchlist",
      direction: "flat",
      benchmark: "Calculation error",
      status: "at"
    };
  }

  const growthRate = ((currentNum - previousNum) / previousNum) * 100;
  const industryAvg = 5; // Default industry average
  
  return {
    value: `+${growthRate.toFixed(1)}%`,
    unit: "YoY",
    label: growthRate >= 8 ? "strength" : growthRate >= 3 ? "opportunity" : "risk",
    direction: growthRate > 0 ? "up" : growthRate < 0 ? "down" : "flat",
    benchmark: `vs industry avg ${industryAvg}%`,
    status: growthRate > industryAvg ? "above" : growthRate < industryAvg * 0.7 ? "below" : "at"
  };
}

function calculateInvestabilityGrade(score: string): {
  grade: string;
  label: ContextualLabel;
} {
  const numScore = parseFloat(score);
  
  if (isNaN(numScore)) {
    return { grade: "N/A", label: "watchlist" };
  }
  
  if (numScore >= 90) return { grade: "A+", label: "strength" };
  if (numScore >= 85) return { grade: "A", label: "strength" };
  if (numScore >= 80) return { grade: "A-", label: "strength" };
  if (numScore >= 75) return { grade: "B+", label: "opportunity" };
  if (numScore >= 70) return { grade: "B", label: "opportunity" };
  if (numScore >= 65) return { grade: "B-", label: "opportunity" };
  if (numScore >= 60) return { grade: "C+", label: "watchlist" };
  if (numScore >= 55) return { grade: "C", label: "watchlist" };
  if (numScore >= 50) return { grade: "C-", label: "risk" };
  return { grade: "D", label: "risk" };
}

function calculateRiskPercentOfEV(risk: string, enterpriseValue: string): string {
  const riskNum = parseFloat(risk.replace(/[^0-9.]/g, ''));
  const evNum = parseFloat(enterpriseValue.replace(/[^0-9.]/g, ''));
  
  if (isNaN(riskNum) || isNaN(evNum) || evNum === 0) return "N/A";
  
  return ((riskNum / evNum) * 100).toFixed(0);
}

function generateSEIJoyceSummary(
  companyName: string,
  executiveDashboard: any,
  valueLeakage: any,
  companyContext: any,
  revenueGrowth: any,
  investabilityGrade: any
): {
  summary: string;
  highlights: Array<{ text: string; type: "positive" | "negative" | "neutral" }>;
  suggestedQuestions: string[];
} {
  const joyScore = executiveDashboard?.readinessSnapshot?.joyScore;
  const totalLeakage = valueLeakage?.valueLeakageOverview?.totalLeakage;
  
  const summary = `Welcome back! Here's what's shifted in ${companyName}'s execution and market environment since your last check-in: Revenue growth ${revenueGrowth.value || "steady"} ${revenueGrowth.benchmark || ""}, while maintaining an Investability Grade of ${investabilityGrade.grade}. Joy Score holds at ${joyScore?.value || "N/A"}, with approximately ${totalLeakage?.amount || "significant"} in execution alpha opportunities identified across key operational areas.`;

  const highlights = [
    {
      text: `Revenue growth ${revenueGrowth.value || "maintained"} - ${revenueGrowth.status === "above" ? "outpacing" : "tracking"} industry benchmarks`,
      type: revenueGrowth.status === "above" ? "positive" : "neutral" as const
    },
    {
      text: `Investability Grade: ${investabilityGrade.grade} - ${investabilityGrade.label === "strength" ? "strong fundamentals" : investabilityGrade.label === "risk" ? "concerns identified" : "moderate performance"}`,
      type: investabilityGrade.label === "strength" ? "positive" : investabilityGrade.label === "risk" ? "negative" : "neutral" as const  
    },
    {
      text: `${totalLeakage?.amount || "Significant"} execution alpha opportunity across operational efficiency and strategic initiatives`,
      type: "positive" as const
    }
  ];

  const suggestedQuestions = [
    `What's driving our ${revenueGrowth.status === "above" ? "outperformance" : "revenue trends"} vs industry?`,
    `How can we improve our Investability Grade from ${investabilityGrade.grade}?`,
    `Which execution alpha opportunities offer the fastest ROI?`
  ];

  return {
    summary,
    highlights,
    suggestedQuestions
  };
}

function navigateToSection(section: string) {
  // This would integrate with your routing system
  console.log(`Navigate to ${section} section`);
  // Example: router.push(`/era-report/${section}`);
}