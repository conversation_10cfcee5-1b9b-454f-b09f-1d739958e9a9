import { MirrorKPIData } from "@/components/digital-mirror/mirror-kpi-card";
import { DollarSign, TrendingUp, Users, Clock, Target, Zap, Heart, Shield, Smartphone, Building } from "lucide-react";

export const sampleCVSKPIs: MirrorKPIData[] = [
  // Enterprise Layer KPIs
  {
    id: "enterprise-operating-margin",
    name: "Operating Margin",
    icon: DollarSign,
    currentValue: 53,
    unit: "%",
    target: 55,
    benchmark: 54,
    trend: [51, 52, 52.5, 53.2, 52.8, 53],
    variance: "down",
    benchmarkDelta: -1.9,
    fogScore: 95,
    maturityLevel: "Advanced",
    executionAlphaImpact: 310000000,
    drivers: [
      "High service costs in APAC region", 
      "Onboarding inefficiencies in digital products",
      "Elevated customer acquisition costs"
    ],
    joyceInsight: "Focus on APAC operational efficiency and digital onboarding automation to close the 2-point margin gap.",
    layer: "enterprise",
    category: "financial",
    status: "at-risk"
  },
  {
    id: "enterprise-joy-score",
    name: "Joy Score (Execution Readiness)",
    icon: Heart,
    currentValue: 62,
    unit: "/100",
    target: 74,
    benchmark: 74,
    trend: [58, 59, 60, 61, 61.5, 62],
    variance: "up",
    benchmarkDelta: -16.2,
    fogScore: 88,
    maturityLevel: "Developing",
    executionAlphaImpact: 180000000,
    drivers: [
      "Tech & Innovation capability gaps",
      "Process standardization delays", 
      "Change management maturity"
    ],
    joyceInsight: "Prioritize technology modernization and process automation initiatives to improve execution capability.",
    layer: "enterprise",
    category: "maturity",
    status: "off-track"
  },
  {
    id: "enterprise-revenue-growth",
    name: "Revenue Growth Rate", 
    icon: TrendingUp,
    currentValue: 4.2,
    unit: "%",
    target: 6.5,
    benchmark: 5.8,
    trend: [3.8, 4.1, 4.0, 4.3, 4.1, 4.2],
    variance: "flat",
    benchmarkDelta: -27.6,
    fogScore: 92,
    executionAlphaImpact: 890000000,
    drivers: [
      "Digital transformation lag",
      "Market share pressure from Amazon", 
      "Customer acquisition challenges"
    ],
    joyceInsight: "Accelerate digital health initiatives and improve customer retention to close growth gap.",
    layer: "enterprise", 
    category: "financial",
    status: "off-track"
  },

  // Product/Service Layer KPIs
  {
    id: "product-retail-pharmacy-innovation",
    name: "Innovation Velocity (Retail Pharmacy)",
    icon: Zap,
    currentValue: 2,
    unit: " releases/yr",
    target: 4,
    benchmark: 3,
    trend: [1.5, 1.8, 1.9, 2.1, 2.0, 2],
    variance: "flat",
    benchmarkDelta: -33.3,
    fogScore: 78,
    maturityLevel: "Basic",
    executionAlphaImpact: 80000000,
    drivers: [
      "Legacy system constraints",
      "Regulatory approval delays",
      "Resource allocation to maintenance vs innovation"
    ],
    joyceInsight: "Modernize core pharmacy systems and streamline regulatory processes to double release velocity.",
    layer: "product",
    category: "operational", 
    status: "off-track"
  },
  {
    id: "product-minuteclinic-margin",
    name: "MinuteClinic Unit Economics",
    icon: Building,
    currentValue: 18.5,
    unit: "% margin",
    target: 22,
    benchmark: 20,
    trend: [17.2, 17.8, 18.1, 18.3, 18.4, 18.5],
    variance: "up",
    benchmarkDelta: -7.5,
    fogScore: 85,
    executionAlphaImpact: 45000000,
    drivers: [
      "Staffing cost inflation",
      "Lower patient volume per clinic",
      "Technology investment ROI lag"
    ],
    joyceInsight: "Optimize clinic staffing models and increase patient throughput to improve unit economics.",
    layer: "product",
    category: "financial",
    status: "at-risk"
  },
  {
    id: "product-digital-health-growth",
    name: "Digital Health Platform Growth",
    icon: Smartphone,
    currentValue: 28,
    unit: "% YoY",
    target: 40,
    benchmark: 35,
    trend: [22, 24, 26, 27, 27.5, 28],
    variance: "up",
    benchmarkDelta: -20,
    fogScore: 82,
    executionAlphaImpact: 125000000,
    drivers: [
      "App adoption slower than expected",
      "Integration challenges with pharmacy systems",
      "Customer education gaps"
    ],
    joyceInsight: "Enhance mobile app experience and accelerate pharmacy system integration to drive digital adoption.",
    layer: "product",
    category: "customer",
    status: "at-risk"
  },

  // Customer Journey Layer KPIs  
  {
    id: "customer-onboarding-lead-time",
    name: "Digital Onboarding Lead Time",
    icon: Clock,
    currentValue: 14,
    unit: " days",
    target: 5,
    benchmark: 6,
    trend: [16, 15.5, 15, 14.8, 14.2, 14],
    variance: "down",
    benchmarkDelta: 133,
    fogScore: 75,
    executionAlphaImpact: 120000000,
    drivers: [
      "Manual identity verification process",
      "Inconsistent staff training across regions",
      "Legacy system integration bottlenecks"
    ],
    joyceInsight: "Automate ID verification and standardize training to reduce onboarding time to 5 days.",
    layer: "customer",
    category: "operational",
    status: "off-track"
  },
  {
    id: "customer-prescription-nps",
    name: "Prescription Experience NPS",
    icon: Users,
    currentValue: 42,
    unit: " NPS",
    target: 55,
    benchmark: 48,
    trend: [38, 39, 40, 41, 41.5, 42],
    variance: "up",
    benchmarkDelta: -12.5,
    fogScore: 88,
    executionAlphaImpact: 85000000,
    drivers: [
      "Wait times during peak hours",
      "Pharmacy staff availability",
      "Insurance processing delays"
    ],
    joyceInsight: "Implement dynamic staffing and pre-authorization automation to improve customer experience.",
    layer: "customer",
    category: "customer",
    status: "at-risk"
  },
  {
    id: "customer-digital-adoption",
    name: "Digital Service Adoption Rate",
    icon: Target,
    currentValue: 32,
    unit: "%",
    target: 50,
    benchmark: 42,
    trend: [28, 29, 30, 31, 31.5, 32],
    variance: "up",
    benchmarkDelta: -23.8,
    fogScore: 80,
    executionAlphaImpact: 95000000,
    drivers: [
      "Limited mobile app functionality",
      "Customer awareness gaps",
      "Complex registration process"
    ],
    joyceInsight: "Enhance mobile capabilities and simplify digital onboarding to drive adoption.",
    layer: "customer",
    category: "customer",
    status: "off-track"
  },
  {
    id: "customer-retention-rate",
    name: "Customer Retention Rate (12M)",
    icon: Shield,
    currentValue: 78,
    unit: "%",
    target: 85,
    benchmark: 82,
    trend: [76, 76.5, 77, 77.5, 77.8, 78],
    variance: "up",
    benchmarkDelta: -4.9,
    fogScore: 90,
    executionAlphaImpact: 150000000,
    drivers: [
      "Competitive pricing pressure",
      "Service quality inconsistencies",
      "Limited loyalty program benefits"
    ],
    joyceInsight: "Strengthen loyalty programs and improve service consistency to boost retention.",
    layer: "customer",
    category: "customer", 
    status: "at-risk"
  }
];