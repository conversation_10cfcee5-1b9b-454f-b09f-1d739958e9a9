name: DB Migrations (Drizzle)

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      environment:
        description: "Target environment"
        required: true
        type: choice
        options:
          - production

permissions:
  contents: read

jobs:
  migrate-staging:
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    name: Migrate Staging
    runs-on: ubuntu-latest
    environment: staging
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Run Drizzle push (staging)
        env:
          DATABASE_URL: ${{ secrets.STAGING_MIGRATOR_URL }}
        run: |
          if [ -z "$DATABASE_URL" ]; then
            echo "STAGING_MIGRATOR_URL secret is not set in GitHub environment 'staging'"
            exit 1
          fi
          npx drizzle-kit push

  migrate-production:
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production'
    name: Migrate Production (Manual Approval)
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Install dependencies
        run: npm ci

      - name: Run Drizzle push (production)
        env:
          DATABASE_URL: ${{ secrets.PROD_MIGRATOR_URL }}
        run: |
          if [ -z "$DATABASE_URL" ]; then
            echo "PROD_MIGRATOR_URL secret is not set in GitHub environment 'production'"
            exit 1
          fi
          npx drizzle-kit push
