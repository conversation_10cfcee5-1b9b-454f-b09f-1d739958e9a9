name: Railway Auto Deploy

on:
  push:
    branches:
      - main

jobs:
  deploy-staging:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: staging
    container: ghcr.io/railwayapp/cli:latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4


      - name: Deploy to Railway (staging)
        env:
          RA<PERSON>WAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
          SVC_ID: ${{ secrets.RAILWAY_SERVICE_ID }}
        run: |
          set -e
          if [ -z "$RAILWAY_TOKEN" ] || [ -z "$SVC_ID" ]; then
            echo "Missing RAILWAY_TOKEN or RAILWAY_SERVICE_ID"; exit 1
          fi
          railway --version
          # Using recommended containerized CLI + project token + service id
          railway up --service="$SVC_ID"
