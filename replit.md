# Strategic Execution Intelligence (SEI) Platform

## Overview

This is a full-stack web application built for Rejoyce, providing Strategic Execution Intelligence (SEI) capabilities. The platform delivers comprehensive business analytics, performance dashboards, and AI-powered insights through an intuitive interface. It focuses on helping organizations analyze performance data, track KPIs, and generate strategic insights for better decision-making.

## System Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Build Tool**: Vite for fast development and optimized production builds
- **Routing**: Wouter for lightweight client-side routing
- **State Management**: TanStack React Query for server state management
- **UI Framework**: shadcn/ui component library built on Radix UI primitives
- **Styling**: Tailwind CSS with custom design system variables
- **Charts**: Recharts for data visualization

### Backend Architecture
- **Runtime**: Node.js with Express.js framework
- **Language**: TypeScript with ES modules
- **Database ORM**: Drizzle ORM with PostgreSQL dialect
- **Database Provider**: Neon Database (@neondatabase/serverless)
- **Authentication**: Session-based authentication with bcrypt for password hashing
- **API Design**: RESTful API with structured error handling

### Project Structure
- `client/` - Frontend React application
- `server/` - Backend Express.js API
- `shared/` - Shared TypeScript schemas and types
- `migrations/` - Database migration files

## Key Components

### Authentication System
- Username/password authentication with bcrypt hashing
- Session-based user management
- Role-based access control (analyst role by default)
- Protected route wrapper for authenticated content

### Dashboard Module
- KPI metrics visualization with trend indicators
- Performance charts using Recharts (line and bar charts)
- Real-time data refresh capabilities
- Company-specific performance tracking (focused on Verizon)

### SEI Report System
- Multi-section strategic execution intelligence reports
- Executive summary with actionable insights
- Enterprise layer analysis
- Product and services evaluation
- Customer engagement metrics
- Organizational maturity assessment
- Execution roadmap planning

### AI Agent (Joyce)
- Conversational AI interface for business intelligence
- Strategic insights and recommendations
- Query-based data analysis
- Contextual business advice

### Data Management
- Company performance tracking
- KPI metrics with historical comparisons
- Strategic insights storage
- Execution priorities management

## Data Flow

1. **Authentication Flow**: User credentials → Express API → Session storage → Protected routes
2. **Dashboard Data**: React Query → Express API → Drizzle ORM → PostgreSQL → Data transformation → UI components
3. **SEI Reports**: Static analysis data → React components → Interactive report sections
4. **AI Interactions**: User queries → Simulated AI responses → Conversational interface

## External Dependencies

### Database
- **PostgreSQL**: Primary database using Neon Database serverless
- **Drizzle ORM**: Type-safe database interactions with schema-first approach
- **Connection**: Environment-based DATABASE_URL configuration

### UI/UX Libraries
- **Radix UI**: Headless component primitives for accessibility
- **Tailwind CSS**: Utility-first CSS framework
- **Lucide React**: Icon library for consistent iconography
- **shadcn/ui**: Pre-built component library with consistent design

### Development Tools
- **Vite**: Fast build tool with Hot Module Replacement
- **TypeScript**: Type safety across frontend and backend
- **ESLint**: Code quality and consistency
- **PostCSS**: CSS processing and optimization

## Deployment Strategy

### Development Environment
- Vite dev server for frontend development
- Express server with middleware integration
- Hot module replacement for rapid development
- Replit-specific development tooling and error overlays

### Production Build
- Frontend: Vite build process generating optimized static assets
- Backend: esbuild bundling for Node.js deployment
- Single deployment artifact with static file serving
- Environment-based configuration management

### Database Strategy
- PostgreSQL connection via environment variables
- Drizzle migrations for schema management
- Connection pooling through Neon Database
- Schema-first development with type generation

## User Preferences

Preferred communication style: Simple, everyday language.

## Changelog

Changelog:
- July 07, 2025. Initial setup