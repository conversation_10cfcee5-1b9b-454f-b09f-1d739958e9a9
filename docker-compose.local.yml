version: "3.9"

services:
  db:
    image: postgres:16
    container_name: rejoyce-db-local
    environment:
      POSTGRES_USER: rejoyce
      POSTGRES_PASSWORD: rejoyce
      POSTGRES_DB: rejoyce_dev
    ports:
      - "5432:5432"
    volumes:
      - rejoyce_pgdata_local:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U rejoyce -d rejoyce_dev"]
      interval: 5s
      timeout: 5s
      retries: 10

  app:
    build: 
      context: .
      dockerfile: Dockerfile.local
    container_name: rejoyce-app-local
    ports:
      - "3000:5000"
    env_file:
      - .env.local
    environment:
      DATABASE_URL: ************************************/rejoyce_dev
      APP_DATABASE_URL: ************************************/rejoyce_dev
      NODE_ENV: development
      SKIP_OIDC: true
      PORT: 5000
      PUBLIC_BASE_URL: http://localhost:3000
    depends_on:
      db:
        condition: service_healthy
    restart: unless-stopped

volumes:
  rejoyce_pgdata_local: