#!/bin/bash

echo "🚀 Starting Rejoyce LOCAL DEVELOPMENT environment..."
echo "This mirrors production behavior but runs locally with <PERSON><PERSON>."
echo ""
echo "Database: PostgreSQL 16 (same as production)"
echo "App: Built and served like production, with DB migrations"
echo ""

# Use the local docker-compose file
docker-compose -f docker-compose.local.yml up --build

echo ""
echo "✅ Application running at http://localhost:5000"
echo "📊 Database running on localhost:5432"
echo "🏗️  Environment: Production-like with local data"
echo ""
echo "To stop: Ctrl+C or run: docker-compose -f docker-compose.local.yml down"