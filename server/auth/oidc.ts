// server/auth/oidc.ts
import type { Express, Request, Response } from "express";
import pkg from "express-openid-connect";
const { auth, requiresAuth } = pkg;

export function registerAuth(app: Express) {
  // Detect if we're running locally or should skip OIDC
  const isLocalhost = process.env.NODE_ENV === 'development' || process.env.SKIP_OIDC === 'true';
  const baseURL = isLocalhost 
    ? `http://localhost:${process.env.PORT || 3001}`
    : (process.env.PUBLIC_BASE_URL || "").trim();

  // Skip OIDC entirely in development mode
  if (isLocalhost) {
    console.log('🔧 Development mode: Skipping OIDC authentication');
    
    // Mock OIDC middleware for development
    app.use((req: any, res, next) => {
      req.oidc = {
        isAuthenticated: () => true,
        user: {
          sub: 'dev-user',
          email: '<EMAIL>',
          'https://rejoyce.ai/company_id': 'verizon',
          'https://rejoyce.ai/role': 'admin'
        },
        login: (options: any) => res.redirect('/'),
        logout: (options: any) => res.redirect('/')
      };
      next();
    });
    
    // Mock auth routes for development
    app.get("/api/auth/login", (req: Request, res: Response) => res.redirect("/"));
    app.get("/api/auth/logout", (req: Request, res: Response) => res.redirect("/"));
    app.get("/api/auth/refresh", (req: any, res: Response) => res.json({ ok: true }));
    app.get("/api/auth/me", (req: any, res: Response) => {
      res.json({
        sub: 'dev-user',
        email: '<EMAIL>',
        company_id: 'verizon',
        role: 'admin'
      });
    });
    
    return;
  }

  if (!isLocalhost) {
    try {
      if (!baseURL) throw new Error("missing");
      new URL(baseURL);
    } catch {
      console.error('❌ PUBLIC_BASE_URL is invalid or missing. Expected https://&lt;host&gt; for this environment. Value was:', process.env.PUBLIC_BASE_URL);
      throw new TypeError('"baseURL" must be a valid uri');
    }
  }

  // Check for required environment variables
  if (!process.env.OIDC_CLIENT_SECRET) {
    console.error('❌ OIDC_CLIENT_SECRET environment variable is required but not set');
    console.error('Available environment variables:', Object.keys(process.env).filter(key => key.includes('OIDC') || key.includes('AUTH')));
    throw new Error('OIDC_CLIENT_SECRET environment variable is required');
  }

  const issuerBaseURL = (process.env.OIDC_ISSUER_URL || "https://dev-fancj54tilibrlkt.us.auth0.com").trim();
  try {
    // Validate issuerBaseURL early to surface bad values (e.g., copied with markup or spaces)
    new URL(issuerBaseURL);
  } catch {
    console.error('❌ OIDC_ISSUER_URL is invalid or missing. Value was:', process.env.OIDC_ISSUER_URL);
    throw new TypeError('"issuerBaseURL" must be a valid uri');
  }

  const config = {
    issuerBaseURL: issuerBaseURL,
    baseURL: baseURL,
    clientID: process.env.OIDC_CLIENT_ID || "msC4bRk6DjE9VY77F6nHJTDmrH19Cc0K",
    clientSecret: process.env.OIDC_CLIENT_SECRET!,
    secret: process.env.SESSION_SECRET || process.env.OIDC_CLIENT_SECRET!,
    clientAuthMethod: "client_secret_basic",
    authRequired: false,
    idpLogout: true,
    authorizationParams: {
      response_type: "code",
      scope: "openid profile email",
      audience: process.env.OIDC_AUDIENCE || undefined
    },
    routes: {
      login: false as const,
      logout: false as const,
      callback: "/api/auth/callback",
      postLogoutRedirect: baseURL
    },
    session: {
      name: "rejoyce.sid",
      rolling: true,
      rollingDuration: 60 * 60,       // 1h
      absoluteDuration: 24 * 60 * 60  // 24h
      // Cookie flags (HttpOnly/Secure/SameSite) plus CSRF will be finalized in the dedicated session story.
    }
  };

  app.use(auth(config));

  app.get("/api/auth/login", (req: Request, res: Response) => 
    (res as any).oidc.login({
      returnTo: "/",
      authorizationParams: { prompt: "login" } // force login screen even if SSO/session exists
    }));

  app.get("/api/auth/logout", (req: Request, res: Response) => 
    (res as any).oidc.logout({ returnTo: "/" }));

  app.get("/api/auth/refresh", requiresAuth(), async (req: any, res: Response) => {
    // Accessing tokens triggers silent refresh if a refresh_token is present
    await req.oidc.fetchUserInfo(); // forces token check/refresh under the hood
    res.json({ ok: true });
  });

  app.get("/api/auth/me", (req: any, res: Response) => {
    if (!req.oidc?.isAuthenticated()) return res.status(401).json({ error: "not_authenticated" });
    const u = req.oidc.user || {};
    res.json({
      sub: u.sub,
      email: u.email,
      company_id: u["https://rejoyce.ai/company_id"],
      role: u["https://rejoyce.ai/role"]
    });
  });
}
