import "dotenv/config";
import express, { type Request, Response, NextFunction } from "express";
import { registerRoutes } from "./routes";
import { initStorage } from "./storage";
import { registerReadiness } from "./readiness";
import { registerAuth } from "./auth/oidc";
// Note: Avoid importing vite in production bundle. We'll dynamically import setupVite in development only.
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

function log(message: string, source = "express") {
  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true,
  });

  console.log(`${formattedTime} [${source}] ${message}`);
}

function serveStaticLocal(app: express.Express) {
  const distPath = path.resolve(__dirname, "public");

  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`,
    );
  }

  app.use(express.static(distPath));

  // fall through to index.html if the file doesn't exist
  app.use("*", (_req, res) => {
    res.sendFile(path.resolve(distPath, "index.html"));
  });
}

const app = express();
app.set("trust proxy", 1); // behind Railway/Cloudflare proxy: mark req.secure correctly for secure cookies
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
    }
  });

  next();
});

(async () => {
  await initStorage();
  
  // Always register auth (either real OIDC or mock for development)
  registerAuth(app);
  
  const server = await registerRoutes(app);
  registerReadiness(app);

  // Auto-redirect unauthenticated users to Auth0 on SPA routes (non-API) in non-local envs.
  // This removes the extra "Log In" click by sending users straight to the hosted login page
  // whenever they hit the app and are not authenticated.
  app.use((req, res, next) => {
    const isLocalhost = process.env.NODE_ENV === "development";
    const p = req.path;

    // Always allow API, auth callbacks, health, and common static assets.
    if (
      p.startsWith("/api") ||         // includes /api/auth/*
      p === "/ready" ||
      p.startsWith("/assets") ||
      p.startsWith("/static") ||
      p.startsWith("/favicon") ||
      p.startsWith("/robots") ||
      p.startsWith("/manifest")
    ) {
      return next();
    }

    const isAuthed = (req as any).oidc?.isAuthenticated?.();
    if (!isLocalhost && !isAuthed) {
      // Redirect to Auth0 hosted login, then return to the originally requested path
      return (res as any).oidc.login({ returnTo: (req as any).originalUrl || "/" });
    }
    next();
  });

  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";

    res.status(status).json({ message });
    throw err;
  });

  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (process.env.NODE_ENV !== "production") {
    const { setupVite } = await import("./vite");
    await setupVite(app, server);
  } else {
    serveStaticLocal(app);
  }

  // Serve on process.env.PORT when provided; default to 5000 (Replit expects 5000)
  // this serves both the API and the client.
  // Locally, you can set PORT to run on an alternate port.
  const port = Number(process.env.PORT) || 5000;
  server.listen({
    port,
    host: "0.0.0.0",
  }, () => {
    log(`serving on port ${port}`);
  });
})();
