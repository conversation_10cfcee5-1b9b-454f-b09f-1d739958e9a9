// server/db.ts
import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";

// Prefer APP_DATABASE_URL (for least privilege later), fall back to DATABASE_URL
const connectionString = process.env.APP_DATABASE_URL ?? process.env.DATABASE_URL;

if (!connectionString) {
  // Do not construct a Pool without a connection string.
  // This module should only be imported when a DB URL is configured.
  throw new Error("DATABASE_URL is not set. Set it in your environment to enable Postgres.");
}

 // Enable SSL when the URL asks for it (e.g., ?sslmode=require). Local dev stays plain.
 // If your provider needs looser TLS verification, set PG_REJECT_UNAUTHORIZED=false.
 const useSSL = /\bsslmode=require\b/i.test(connectionString);
 const ssl =
   useSSL
     ? (process.env.PG_REJECT_UNAUTHORIZED === "false" ? { rejectUnauthorized: false } : true)
     : undefined;

 export const pool = new Pool({ connectionString, ssl });
 export const db = drizzle(pool);
