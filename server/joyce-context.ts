import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __dirname = path.dirname(fileURLToPath(import.meta.url));

interface CompanyData {
  executiveSummary: any;
  enterpriseLayer: any;
}

interface JoyceContext {
  systemPrompt: string;
  companyContext?: string;
}

export class JoyceContextService {
  private static systemPromptCache: string | null = null;

  /**
   * Loads the system prompt from the prompts folder
   */
  private static async loadSystemPrompt(): Promise<string> {
    if (this.systemPromptCache) {
      return this.systemPromptCache;
    }

    try {
      const promptPath = path.join(__dirname, '../prompts/joyce-system-prompt.md');
      
      if (fs.existsSync(promptPath)) {
        this.systemPromptCache = fs.readFileSync(promptPath, 'utf-8');
        return this.systemPromptCache;
      }
    } catch (error) {
      console.warn('Could not load system prompt from file:', error);
    }

    // Fallback to basic prompt
    this.systemPromptCache = "You are <PERSON>, a helpful AI assistant for the Rejoyce enterprise analytics platform. You help users understand their business data and provide strategic insights.";
    return this.systemPromptCache;
  }

  /**
   * Loads company-specific data from JSON files
   */
  private static async loadCompanyData(companySymbol: string): Promise<CompanyData | null> {
    try {
      // Try multiple possible paths for the companies directory
      const possiblePaths = [
        path.join(__dirname, '../../client/public/data/companies'),
        path.join(__dirname, '../client/public/data/companies'),
        path.join(process.cwd(), 'client/public/data/companies'),
        path.join(process.cwd(), 'public/data/companies'),
      ];

      let companiesDir = '';
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          companiesDir = possiblePath;
          break;
        }
      }

      if (!companiesDir) {
        console.warn(`Companies directory not found. Tried paths:`, possiblePaths);
        return null;
      }

      const companyDir = path.join(companiesDir, companySymbol.toLowerCase());
      console.log(`Looking for company data at: ${companyDir}`);

      if (!fs.existsSync(companyDir)) {
        console.warn(`Company directory not found: ${companySymbol} at ${companyDir}`);
        return null;
      }

      const executiveSummaryPath = path.join(companyDir, 'executive-summary.json');
      const enterpriseLayerPath = path.join(companyDir, 'enterprise-layer.json');

      let executiveSummary = null;
      let enterpriseLayer = null;

      if (fs.existsSync(executiveSummaryPath)) {
        const data = fs.readFileSync(executiveSummaryPath, 'utf-8');
        executiveSummary = JSON.parse(data);
      }

      if (fs.existsSync(enterpriseLayerPath)) {
        const data = fs.readFileSync(enterpriseLayerPath, 'utf-8');
        enterpriseLayer = JSON.parse(data);
      }

      return { executiveSummary, enterpriseLayer };
    } catch (error) {
      console.error(`Error loading company data for ${companySymbol}:`, error);
      return null;
    }
  }

  /**
   * Formats company data into a structured context string
   */
  private static formatCompanyContext(companyData: CompanyData): string {
    const { executiveSummary, enterpriseLayer } = companyData;
    let context = '';

    if (executiveSummary) {
      context += `## Company: ${executiveSummary.companyName} (${executiveSummary.companySymbol})\n\n`;
      
      // Executive Snapshot
      if (executiveSummary.executiveSnapshot) {
        context += `### Executive Snapshot\n`;
        const snapshot = executiveSummary.executiveSnapshot;
        
        if (snapshot.investabilityGrade) {
          context += `- **Investability Grade**: ${snapshot.investabilityGrade.value} - ${snapshot.investabilityGrade.description}\n`;
        }
        if (snapshot.joyScore) {
          context += `- **Joy Score**: ${snapshot.joyScore.value}/${snapshot.joyScore.maxValue} - ${snapshot.joyScore.description}\n`;
        }
        if (snapshot.fogScore) {
          context += `- **Fog Score**: ${snapshot.fogScore.value}/${snapshot.fogScore.maxValue} - ${snapshot.fogScore.description}\n`;
        }
        if (snapshot.roiPotential) {
          context += `- **ROI Potential**: ${snapshot.roiPotential.value} - ${snapshot.roiPotential.description}\n`;
        }
        context += '\n';
      }

      // Summary Analysis
      if (executiveSummary.summaryAnalysis?.text) {
        context += `### Strategic Overview\n${executiveSummary.summaryAnalysis.text}\n\n`;
      }

      // Key Strengths
      if (executiveSummary.keyStrengths?.length > 0) {
        context += `### Key Strengths\n`;
        executiveSummary.keyStrengths.forEach((strength: any) => {
          context += `- ${strength.text}\n`;
        });
        context += '\n';
      }

      // Risk Flags
      if (executiveSummary.riskFlags?.length > 0) {
        context += `### Risk Factors\n`;
        executiveSummary.riskFlags.forEach((risk: any) => {
          context += `- ${risk.text}\n`;
        });
        context += '\n';
      }
    }

    if (enterpriseLayer) {
      // Executive Dashboard Metrics
      if (enterpriseLayer.executiveDashboard) {
        context += `### Key Financial Metrics\n`;
        const dashboard = enterpriseLayer.executiveDashboard;
        
        if (dashboard.marketCap) {
          context += `- **Market Cap**: ${dashboard.marketCap.value} (${dashboard.marketCap.status})\n`;
        }
        if (dashboard.revenue) {
          context += `- **Revenue**: ${dashboard.revenue.value} (${dashboard.revenue.status})\n`;
        }
        if (dashboard.ebitda) {
          context += `- **EBITDA**: ${dashboard.ebitda.value} (${dashboard.ebitda.status})\n`;
        }
        if (dashboard.pe_ratio) {
          context += `- **P/E Ratio**: ${dashboard.pe_ratio.value} (${dashboard.pe_ratio.status})\n`;
        }
        context += '\n';
      }

      // Core KPIs
      if (enterpriseLayer.coreKPIs?.length > 0) {
        context += `### Core KPIs\n`;
        enterpriseLayer.coreKPIs.slice(0, 10).forEach((kpi: any) => {
          context += `- **${kpi.name}**: ${kpi.currentValue} (Target: ${kpi.targetValue}, Status: ${kpi.status})\n`;
        });
        context += '\n';
      }
    }

    return context;
  }

  /**
   * Builds complete context for Joyce including system prompt and company data
   */
  public static async buildContext(companySymbol?: string): Promise<JoyceContext> {
    const systemPrompt = await this.loadSystemPrompt();
    
    if (!companySymbol) {
      return { systemPrompt };
    }

    const companyData = await this.loadCompanyData(companySymbol);
    
    if (!companyData) {
      return { systemPrompt };
    }

    const companyContext = this.formatCompanyContext(companyData);
    
    // Combine system prompt with company context
    const enhancedSystemPrompt = `${systemPrompt}

## Current Company Context
${companyContext}

Please use this company-specific data to provide relevant, insightful analysis and recommendations. Focus on the key metrics, strengths, and risk factors outlined above.`;

    return {
      systemPrompt: enhancedSystemPrompt,
      companyContext
    };
  }

  /**
   * Clears the system prompt cache (useful for development)
   */
  public static clearCache(): void {
    this.systemPromptCache = null;
  }
}