// server/readiness.ts
import type { Express, Request, Response } from "express";
import { sql } from "drizzle-orm";

/**
 * Registers a simple readiness endpoint.
 * - If a DB URL is configured, it returns 200 only if the DB responds to SELECT 1; otherwise 503.
 * - If no DB URL is configured (pure in-memory mode), it returns 200.
 */
export function registerReadiness(app: Express) {
  const hasDb =
    Boolean(process.env.APP_DATABASE_URL) || Boolean(process.env.DATABASE_URL);

  app.get("/ready", async (_req: Request, res: Response) => {
    if (!hasDb) {
      // In dev without DB configured, consider the service "ready"
      return res.status(200).send("OK (no DB configured)");
    }
    try {
      // Lazy import to avoid throwing when no DB URL is configured
      const { db } = await import("./db");
      await db.execute(sql`SELECT 1`);
      res.status(200).send("OK");
    } catch {
      res.status(503).send("DB unavailable");
    }
  });
}
