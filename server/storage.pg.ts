// server/storage.pg.ts
import { db } from "./db";
import type { IStorage } from "./storage";
import {
  users,
  companies,
  kpiMetrics,
  performanceData,
  strategicInsights,
  executionPriorities,
  type User,
  type InsertUser,
  type Company,
  type KpiMetric,
  type PerformanceData,
  type StrategicInsight,
  type ExecutionPriority,
} from "@shared/schema";
import { eq } from "drizzle-orm";

export class PgStorage implements IStorage {
  // User management
  async getUser(id: number): Promise<User | undefined> {
    const rows = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return rows[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const rows = await db
      .select()
      .from(users)
      .where(eq(users.username, username))
      .limit(1);
    return rows[0];
  }

  async createUser(u: InsertUser): Promise<User> {
    const [row] = await db
      .insert(users)
      .values({
        username: u.username,
        password: u.password,
        email: u.email,
      })
      .returning();
    return row!;
  }

  // Company management
  async getCompanies(): Promise<Company[]> {
    return await db.select().from(companies);
  }

  async getCompany(symbol: string): Promise<Company | undefined> {
    const rows = await db
      .select()
      .from(companies)
      .where(eq(companies.symbol, symbol))
      .limit(1);
    return rows[0];
  }

  // KPI metrics
  async getKpiMetrics(companyId: number): Promise<KpiMetric[]> {
    return await db
      .select()
      .from(kpiMetrics)
      .where(eq(kpiMetrics.companyId, companyId));
  }

  // Performance data
  async getPerformanceData(companyId: number): Promise<PerformanceData[]> {
    return await db
      .select()
      .from(performanceData)
      .where(eq(performanceData.companyId, companyId));
  }

  // Strategic insights
  async getStrategicInsights(companyId: number): Promise<StrategicInsight[]> {
    return await db
      .select()
      .from(strategicInsights)
      .where(eq(strategicInsights.companyId, companyId));
  }

  // Execution priorities
  async getExecutionPriorities(companyId: number): Promise<ExecutionPriority[]> {
    return await db
      .select()
      .from(executionPriorities)
      .where(eq(executionPriorities.companyId, companyId));
  }
}
