# syntax=docker/dockerfile:1

# Build stage: install all deps and build client + server
FROM node:20-bookworm-slim AS builder
WORKDIR /app

# Install dependencies first (leverage layer caching)
COPY package.json package-lock.json ./
RUN npm ci

# Copy the rest of the source
COPY . .

# Build client (Vite) into dist/public and bundle server into dist/index.js
RUN npm run build

# Runtime stage: only what's needed to run the app
FROM node:20-bookworm-slim AS runner
WORKDIR /app

# Install only production dependencies
COPY package.json package-lock.json ./
RUN npm ci --omit=dev

# Copy built artifacts
COPY --from=builder /app/dist ./dist

# Copy client/public data for routes that read from the source tree
# (server/routes.ts reads ../client/public/data/companies at runtime)
COPY --from=builder /app/client/public ./client/public

# Environment
ENV NODE_ENV=production
ENV PORT=5000

# Railway will set PORT automatically; this EXPOSE is informational
EXPOSE 5000

# Start the server (serves API and static files from dist/public)
CMD ["node", "dist/index.js"]
