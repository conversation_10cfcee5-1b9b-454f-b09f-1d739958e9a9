{"id": "1af03b76-371a-46fc-87c4-8d28833395b1", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "sector": {"name": "sector", "type": "text", "primaryKey": false, "notNull": true}, "market_cap": {"name": "market_cap", "type": "numeric", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_symbol_unique": {"name": "companies_symbol_unique", "nullsNotDistinct": false, "columns": ["symbol"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.execution_priorities": {"name": "execution_priorities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "owner": {"name": "owner", "type": "text", "primaryKey": false, "notNull": false}, "timeline": {"name": "timeline", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.kpi_metrics": {"name": "kpi_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "metric_name": {"name": "metric_name", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric", "primaryKey": false, "notNull": true}, "previous_value": {"name": "previous_value", "type": "numeric", "primaryKey": false, "notNull": false}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.performance_data": {"name": "performance_data", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "period": {"name": "period", "type": "text", "primaryKey": false, "notNull": true}, "revenue": {"name": "revenue", "type": "numeric", "primaryKey": false, "notNull": false}, "net_income": {"name": "net_income", "type": "numeric", "primaryKey": false, "notNull": false}, "free_cash_flow": {"name": "free_cash_flow", "type": "numeric", "primaryKey": false, "notNull": false}, "churn_rate": {"name": "churn_rate", "type": "numeric", "primaryKey": false, "notNull": false}, "customer_count": {"name": "customer_count", "type": "integer", "primaryKey": false, "notNull": false}, "arpu": {"name": "arpu", "type": "numeric", "primaryKey": false, "notNull": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.strategic_insights": {"name": "strategic_insights", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "company_id": {"name": "company_id", "type": "integer", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "assigned_to": {"name": "assigned_to", "type": "text", "primaryKey": false, "notNull": false}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "username": {"name": "username", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'analyst'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_username_unique": {"name": "users_username_unique", "nullsNotDistinct": false, "columns": ["username"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}